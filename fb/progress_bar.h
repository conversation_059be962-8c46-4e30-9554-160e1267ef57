	unsigned char logo_data_left[] = {
0x42, 0x4D, 0x36, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x36, 0x00, 0x00, 0x00, 0x28, 0x00,
0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x01, 0x00, 0x18, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x26, 0x26, 0x26, 0x23, 0x23, 0x23, 0x1F, 0x1F, 0x1F, 0x1F,
0x1F, 0x1F, 0x21, 0x21, 0x21, 0x25, 0x25, 0x25, 0x2B, 0x2B, 0x2B, 0x2A, 0x2A, 0x2A, 0x23, 0x23,
0x23, 0x21, 0x21, 0x21, 0x27, 0x27, 0x27, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29,
0x27, 0x27, 0x27, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x23, 0x23, 0x23, 0x21, 0x21, 0x21, 0x21,
0x21, 0x21, 0x22, 0x22, 0x22, 0x24, 0x24, 0x24, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x24, 0x24,
0x24, 0x22, 0x22, 0x22, 0x25, 0x25, 0x25, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26,
0x26, 0x26, 0x26, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x28, 0x28, 0x28, 0x29,
0x29, 0x29, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29, 0x25, 0x25, 0x25, 0x26, 0x26, 0x26, 0x29, 0x29,
0x29, 0x27, 0x27, 0x27, 0x1E, 0x1E, 0x1E, 0x1C, 0x1C, 0x1C, 0x20, 0x20, 0x20, 0x23, 0x23, 0x23,
0x26, 0x26, 0x26, 0x27, 0x27, 0x27, 0x25, 0x25, 0x25, 0x26, 0x26, 0x26, 0x2B, 0x2B, 0x2B, 0x2C,
0x2C, 0x2C, 0x28, 0x28, 0x28, 0x26, 0x26, 0x26, 0x24, 0x24, 0x24, 0x26, 0x26, 0x26, 0x2D, 0x2D,
0x2D, 0x2B, 0x2B, 0x2B, 0x21, 0x21, 0x21, 0x1D, 0x1D, 0x1D, 0x21, 0x21, 0x21, 0x25, 0x25, 0x25,
0x29, 0x29, 0x29, 0x2B, 0x2B, 0x2B, 0x21, 0x21, 0x21, 0x23, 0x23, 0x23, 0x2A, 0x2A, 0x2A, 0x28,
0x28, 0x28, 0x1C, 0x1C, 0x1C, 0x1B, 0x1B, 0x1B, 0x24, 0x24, 0x24, 0x2A, 0x2A, 0x2A, 0x2E, 0x2E,
0x2E, 0x2F, 0x2F, 0x2F, 0x2C, 0x2C, 0x2C, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x2D, 0x2D, 0x2D,
0x31, 0x31, 0x31, 0x32, 0x32, 0x32, 0x21, 0x21, 0x21, 0x23, 0x23, 0x23, 0x29, 0x29, 0x29, 0x27,
0x27, 0x27, 0x1D, 0x1D, 0x1D, 0x1C, 0x1C, 0x1C, 0x27, 0x27, 0x27, 0x2C, 0x2C, 0x2C, 0x2D, 0x2D,
0x2D, 0x2C, 0x2C, 0x2C, 0x29, 0x29, 0x29, 0x28, 0x28, 0x28, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29,
0x29, 0x29, 0x29, 0x28, 0x28, 0x28, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x28, 0x28, 0x28, 0x2A,
0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2B, 0x2B, 0x2B, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x29, 0x29,
0x29, 0x23, 0x23, 0x23, 0x18, 0x18, 0x18, 0x16, 0x16, 0x16, 0x1B, 0x1B, 0x1B, 0x19, 0x19, 0x19,
0x11, 0x11, 0x11, 0x0D, 0x0D, 0x0D, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x27, 0x27, 0x27, 0x2A,
0x2A, 0x2A, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2B, 0x2B, 0x2B, 0x26, 0x26, 0x26, 0x21, 0x21,
0x21, 0x1D, 0x1D, 0x1D, 0x1B, 0x1B, 0x1B, 0x20, 0x20, 0x20, 0x2C, 0x2C, 0x2C, 0x2F, 0x2F, 0x2F,
0x2B, 0x2B, 0x2B, 0x28, 0x28, 0x28, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x27, 0x27, 0x27, 0x2A,
0x2A, 0x2A, 0x2D, 0x2D, 0x2D, 0x2A, 0x2A, 0x2A, 0x22, 0x22, 0x22, 0x1B, 0x1B, 0x1B, 0x14, 0x14,
0x14, 0x1C, 0x1C, 0x1C, 0x32, 0x32, 0x32, 0x47, 0x47, 0x47, 0x5D, 0x5D, 0x5D, 0x6C, 0x6C, 0x6C,
0x74, 0x74, 0x74, 0x78, 0x78, 0x78, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x27,
0x27, 0x27, 0x29, 0x29, 0x29, 0x26, 0x26, 0x26, 0x1B, 0x1B, 0x1B, 0x18, 0x18, 0x18, 0x1B, 0x1B,
0x1B, 0x2E, 0x2E, 0x2E, 0x52, 0x52, 0x52, 0x6C, 0x6C, 0x6C, 0x7C, 0x7C, 0x7C, 0x89, 0x89, 0x89,
0x93, 0x93, 0x93, 0x98, 0x98, 0x98, 0x28, 0x28, 0x28, 0x26, 0x26, 0x26, 0x22, 0x22, 0x22, 0x22,
0x22, 0x22, 0x25, 0x25, 0x25, 0x21, 0x21, 0x21, 0x16, 0x16, 0x16, 0x1C, 0x1C, 0x1C, 0x35, 0x35,
0x35, 0x55, 0x55, 0x55, 0x7C, 0x7C, 0x7C, 0x8E, 0x8E, 0x8E, 0x8B, 0x8B, 0x8B, 0x89, 0x89, 0x89,
0x88, 0x88, 0x88, 0x87, 0x87, 0x87, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x23, 0x23, 0x23, 0x22,
0x22, 0x22, 0x23, 0x23, 0x23, 0x1E, 0x1E, 0x1E, 0x14, 0x14, 0x14, 0x24, 0x24, 0x24, 0x4E, 0x4E,
0x4E, 0x72, 0x72, 0x72, 0x92, 0x92, 0x92, 0x9B, 0x9B, 0x9B, 0x8F, 0x8F, 0x8F, 0x87, 0x87, 0x87,
0x83, 0x83, 0x83, 0x81, 0x81, 0x81, 0x20, 0x20, 0x20, 0x22, 0x22, 0x22, 0x26, 0x26, 0x26, 0x26,
0x26, 0x26, 0x24, 0x24, 0x24, 0x1F, 0x1F, 0x1F, 0x17, 0x17, 0x17, 0x2E, 0x2E, 0x2E, 0x64, 0x64,
0x64, 0x86, 0x86, 0x86, 0x92, 0x92, 0x92, 0x93, 0x93, 0x93, 0x89, 0x89, 0x89, 0x84, 0x84, 0x84,
0x86, 0x86, 0x86, 0x86, 0x86, 0x86, 0x20, 0x20, 0x20, 0x22, 0x22, 0x22, 0x28, 0x28, 0x28, 0x29,
0x29, 0x29, 0x26, 0x26, 0x26, 0x1F, 0x1F, 0x1F, 0x15, 0x15, 0x15, 0x32, 0x32, 0x32, 0x76, 0x76,
0x76, 0x97, 0x97, 0x97, 0x95, 0x95, 0x95, 0x91, 0x91, 0x91, 0x89, 0x89, 0x89, 0x87, 0x87, 0x87,
0x8A, 0x8A, 0x8A, 0x8B, 0x8B, 0x8B, 0x25, 0x25, 0x25, 0x28, 0x28, 0x28, 0x29, 0x29, 0x29, 0x2A,
0x2A, 0x2A, 0x29, 0x29, 0x29, 0x1F, 0x1F, 0x1F, 0x0E, 0x0E, 0x0E, 0x2F, 0x2F, 0x2F, 0x81, 0x81,
0x81, 0xA5, 0xA5, 0xA5, 0x9B, 0x9B, 0x9B, 0x94, 0x94, 0x94, 0x90, 0x90, 0x90, 0x8F, 0x8F, 0x8F,
0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x28, 0x28, 0x28, 0x29, 0x29, 0x29, 0x26, 0x26, 0x26, 0x27,
0x27, 0x27, 0x2C, 0x2C, 0x2C, 0x24, 0x24, 0x24, 0x0E, 0x0E, 0x0E, 0x31, 0x31, 0x31, 0x8B, 0x8B,
0x8B, 0xAF, 0xAF, 0xAF, 0x9B, 0x9B, 0x9B, 0x91, 0x91, 0x91, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93,
0x93, 0x93, 0x93, 0x92, 0x92, 0x92, 0x29, 0x29, 0x29, 0x27, 0x27, 0x27, 0x1C, 0x1C, 0x1C, 0x20,
0x20, 0x20, 0x31, 0x31, 0x31, 0x2E, 0x2E, 0x2E, 0x16, 0x16, 0x16, 0x38, 0x38, 0x38, 0x94, 0x94,
0x94, 0xB3, 0xB3, 0xB3, 0x95, 0x95, 0x95, 0x8A, 0x8A, 0x8A, 0x92, 0x92, 0x92, 0x94, 0x94, 0x94,
0x8F, 0x8F, 0x8F, 0x8F, 0x8F, 0x8F, 0x28, 0x28, 0x28, 0x26, 0x26, 0x26, 0x1C, 0x1C, 0x1C, 0x20,
0x20, 0x20, 0x30, 0x30, 0x30, 0x2D, 0x2D, 0x2D, 0x16, 0x16, 0x16, 0x3A, 0x3A, 0x3A, 0x98, 0x98,
0x98, 0xB8, 0xB8, 0xB8, 0x9A, 0x9A, 0x9A, 0x8F, 0x8F, 0x8F, 0x96, 0x96, 0x96, 0x98, 0x98, 0x98,
0x94, 0x94, 0x94, 0x93, 0x93, 0x93, 0x25, 0x25, 0x25, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x27,
0x27, 0x27, 0x2A, 0x2A, 0x2A, 0x22, 0x22, 0x22, 0x0E, 0x0E, 0x0E, 0x34, 0x34, 0x34, 0x96, 0x96,
0x96, 0xBD, 0xBD, 0xBD, 0xAA, 0xAA, 0xAA, 0xA0, 0xA0, 0xA0, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F,
0xA0, 0xA0, 0xA0, 0xA0, 0xA0, 0xA0, 0x24, 0x24, 0x24, 0x28, 0x28, 0x28, 0x2E, 0x2E, 0x2E, 0x2E,
0x2E, 0x2E, 0x28, 0x28, 0x28, 0x1C, 0x1C, 0x1C, 0x0B, 0x0B, 0x0B, 0x30, 0x30, 0x30, 0x8B, 0x8B,
0x8B, 0xB6, 0xB6, 0xB6, 0xB3, 0xB3, 0xB3, 0xAF, 0xAF, 0xAF, 0xA8, 0xA8, 0xA8, 0xA5, 0xA5, 0xA5,
0xA6, 0xA6, 0xA6, 0xA5, 0xA5, 0xA5, 0x24, 0x24, 0x24, 0x2B, 0x2B, 0x2B, 0x36, 0x36, 0x36, 0x35,
0x35, 0x35, 0x29, 0x29, 0x29, 0x1C, 0x1C, 0x1C, 0x0F, 0x0F, 0x0F, 0x2D, 0x2D, 0x2D, 0x76, 0x76,
0x76, 0xA3, 0xA3, 0xA3, 0xB6, 0xB6, 0xB6, 0xBB, 0xBB, 0xBB, 0xB1, 0xB1, 0xB1, 0xAA, 0xAA, 0xAA,
0xA5, 0xA5, 0xA5, 0xA2, 0xA2, 0xA2, 0x24, 0x24, 0x24, 0x2A, 0x2A, 0x2A, 0x34, 0x34, 0x34, 0x33,
0x33, 0x33, 0x27, 0x27, 0x27, 0x1D, 0x1D, 0x1D, 0x15, 0x15, 0x15, 0x27, 0x27, 0x27, 0x52, 0x52,
0x52, 0x7A, 0x7A, 0x7A, 0x9D, 0x9D, 0x9D, 0xAF, 0xAF, 0xAF, 0xB1, 0xB1, 0xB1, 0xB2, 0xB2, 0xB2,
0xB1, 0xB1, 0xB1, 0xB0, 0xB0, 0xB0, 0x24, 0x24, 0x24, 0x26, 0x26, 0x26, 0x28, 0x28, 0x28, 0x28,
0x28, 0x28, 0x23, 0x23, 0x23, 0x1F, 0x1F, 0x1F, 0x1C, 0x1C, 0x1C, 0x1D, 0x1D, 0x1D, 0x21, 0x21,
0x21, 0x39, 0x39, 0x39, 0x67, 0x67, 0x67, 0x8C, 0x8C, 0x8C, 0xA8, 0xA8, 0xA8, 0xBD, 0xBD, 0xBD,
0xCA, 0xCA, 0xCA, 0xCF, 0xCF, 0xCF, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x22, 0x22, 0x22, 0x21,
0x21, 0x21, 0x21, 0x21, 0x21, 0x21, 0x21, 0x21, 0x23, 0x23, 0x23, 0x1C, 0x1C, 0x1C, 0x0E, 0x0E,
0x0E, 0x1A, 0x1A, 0x1A, 0x3F, 0x3F, 0x3F, 0x60, 0x60, 0x60, 0x7C, 0x7C, 0x7C, 0x92, 0x92, 0x92,
0xA1, 0xA1, 0xA1, 0xA6, 0xA6, 0xA6, 0x2C, 0x2C, 0x2C, 0x2B, 0x2B, 0x2B, 0x22, 0x22, 0x22, 0x1F,
0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x22, 0x22, 0x22, 0x28, 0x28, 0x28, 0x25, 0x25, 0x25, 0x1B, 0x1B,
0x1B, 0x1B, 0x1B, 0x1B, 0x25, 0x25, 0x25, 0x2C, 0x2C, 0x2C, 0x2F, 0x2F, 0x2F, 0x32, 0x32, 0x32,
0x36, 0x36, 0x36, 0x37, 0x37, 0x37, 0x2D, 0x2D, 0x2D, 0x2C, 0x2C, 0x2C, 0x21, 0x21, 0x21, 0x1F,
0x1F, 0x1F, 0x23, 0x23, 0x23, 0x27, 0x27, 0x27, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29, 0x23, 0x23,
0x23, 0x1E, 0x1E, 0x1E, 0x1A, 0x1A, 0x1A, 0x15, 0x15, 0x15, 0x11, 0x11, 0x11, 0x0E, 0x0E, 0x0E,
0x0C, 0x0C, 0x0C, 0x0B, 0x0B, 0x0B, 0x2A, 0x2A, 0x2A, 0x28, 0x28, 0x28, 0x1F, 0x1F, 0x1F, 0x21,
0x21, 0x21, 0x2D, 0x2D, 0x2D, 0x30, 0x30, 0x30, 0x2A, 0x2A, 0x2A, 0x27, 0x27, 0x27, 0x27, 0x27,
0x27, 0x23, 0x23, 0x23, 0x1D, 0x1D, 0x1D, 0x1C, 0x1C, 0x1C, 0x23, 0x23, 0x23, 0x25, 0x25, 0x25,
0x23, 0x23, 0x23, 0x20, 0x20, 0x20, 0x29, 0x29, 0x29, 0x27, 0x27, 0x27, 0x21, 0x21, 0x21, 0x23,
0x23, 0x23, 0x2F, 0x2F, 0x2F, 0x31, 0x31, 0x31, 0x29, 0x29, 0x29, 0x25, 0x25, 0x25, 0x27, 0x27,
0x27, 0x25, 0x25, 0x25, 0x20, 0x20, 0x20, 0x21, 0x21, 0x21, 0x2A, 0x2A, 0x2A, 0x2E, 0x2E, 0x2E,
0x2C, 0x2C, 0x2C, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x26, 0x26, 0x26, 0x26,
0x26, 0x26, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x24, 0x24,
0x24, 0x24, 0x24, 0x24, 0x23, 0x23, 0x23, 0x24, 0x24, 0x24, 0x27, 0x27, 0x27, 0x29, 0x29, 0x29,
0x28, 0x28, 0x28, 0x27, 0x27, 0x27, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29, 0x28,
0x28, 0x28, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x24, 0x24,
0x24, 0x24, 0x24, 0x24, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25,
0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x28, 0x28, 0x28, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x28,
0x28, 0x28, 0x28, 0x28, 0x28, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x26, 0x26,
0x26, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23,
0x22, 0x22, 0x22, 0x24, 0x24, 0x24, 0x27, 0x27, 0x27, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29,
0x29, 0x29, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x27, 0x27,
0x27, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26, 0x24, 0x24, 0x24, 0x23, 0x23, 0x23, 0x21, 0x21, 0x21,
0x20, 0x20, 0x20, 0x23, 0x23, 0x23,
};
	unsigned char logo_data_set1[] = {
0x42, 0x4D, 0x36, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x36, 0x00, 0x00, 0x00, 0x28, 0x00,
0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x01, 0x00, 0x18, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26,
0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26,
0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26,
0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x27, 0x27, 0x27, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29,
0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29,
0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29,
0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x2B, 0x2B, 0x2B, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C,
0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C,
0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C,
0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x32, 0x32, 0x32, 0x30, 0x30, 0x30, 0x2F, 0x2F, 0x2F, 0x2F,
0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F,
0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F,
0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x28, 0x28, 0x28, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x25,
0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25,
0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25,
0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E,
0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E,
0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E,
0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28,
0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28,
0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28,
0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x75, 0x75, 0x75, 0x74, 0x74, 0x74, 0x73, 0x73, 0x73, 0x73,
0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73,
0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73,
0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x95, 0x95, 0x95, 0x94, 0x94, 0x94, 0x93, 0x93, 0x93, 0x93,
0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93,
0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93,
0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x87, 0x87, 0x87, 0x87, 0x87, 0x87, 0x88, 0x88, 0x88, 0x88,
0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x82, 0x82, 0x82, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83,
0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83,
0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83,
0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x86, 0x86, 0x86, 0x86, 0x86, 0x86, 0x86, 0x86, 0x86, 0x86,
0x86, 0x86, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x84, 0x84, 0x84, 0x84, 0x84,
0x84, 0x84, 0x84, 0x84, 0x84, 0x84, 0x84, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85,
0x86, 0x86, 0x86, 0x86, 0x86, 0x86, 0x89, 0x89, 0x89, 0x89, 0x89, 0x89, 0x89, 0x89, 0x89, 0x89,
0x89, 0x89, 0x89, 0x89, 0x89, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x87, 0x87,
0x87, 0x87, 0x87, 0x87, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x89, 0x89, 0x89,
0x89, 0x89, 0x89, 0x89, 0x89, 0x89, 0x8C, 0x8C, 0x8C, 0x8D, 0x8D, 0x8D, 0x8D, 0x8D, 0x8D, 0x8D,
0x8D, 0x8D, 0x8D, 0x8D, 0x8D, 0x8D, 0x8D, 0x8D, 0x8D, 0x8D, 0x8D, 0x8C, 0x8C, 0x8C, 0x8C, 0x8C,
0x8C, 0x8C, 0x8C, 0x8C, 0x8C, 0x8C, 0x8C, 0x8D, 0x8D, 0x8D, 0x8D, 0x8D, 0x8D, 0x8D, 0x8D, 0x8D,
0x8D, 0x8D, 0x8D, 0x8D, 0x8D, 0x8D, 0x90, 0x90, 0x90, 0x91, 0x91, 0x91, 0x92, 0x92, 0x92, 0x91,
0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x91,
0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x91, 0x91,
0x91, 0x91, 0x91, 0x92, 0x92, 0x92, 0x94, 0x94, 0x94, 0x95, 0x95, 0x95, 0x95, 0x95, 0x95, 0x95,
0x95, 0x95, 0x95, 0x95, 0x95, 0x96, 0x96, 0x96, 0x96, 0x96, 0x96, 0x96, 0x96, 0x96, 0x96, 0x96,
0x96, 0x96, 0x96, 0x96, 0x96, 0x96, 0x96, 0x96, 0x96, 0x96, 0x96, 0x96, 0x96, 0x95, 0x95, 0x95,
0x95, 0x95, 0x95, 0x95, 0x95, 0x95, 0x98, 0x98, 0x98, 0x98, 0x98, 0x98, 0x98, 0x98, 0x98, 0x98,
0x98, 0x98, 0x98, 0x98, 0x98, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x98, 0x98, 0x98,
0x98, 0x98, 0x98, 0x98, 0x98, 0x98, 0x9C, 0x9C, 0x9C, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B,
0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9C, 0x9C, 0x9C, 0x9C, 0x9C, 0x9C, 0x9C, 0x9C,
0x9C, 0x9C, 0x9C, 0x9C, 0x9C, 0x9C, 0x9C, 0x9C, 0x9C, 0x9C, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B,
0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9F, 0x9F, 0x9F, 0x9D, 0x9D, 0x9D, 0x9D, 0x9D, 0x9D, 0x9D,
0x9D, 0x9D, 0x9D, 0x9D, 0x9D, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F,
0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9D, 0x9D, 0x9D,
0x9D, 0x9D, 0x9D, 0x9D, 0x9D, 0x9D, 0xA0, 0xA0, 0xA0, 0x9F, 0x9F, 0x9F, 0x9E, 0x9E, 0x9E, 0x9F,
0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0xA0, 0xA0, 0xA0, 0xA0, 0xA0, 0xA0, 0xA0, 0xA0, 0xA0, 0xA0, 0xA0,
0xA0, 0xA0, 0xA0, 0xA0, 0xA0, 0xA0, 0xA0, 0xA0, 0xA0, 0xA0, 0xA0, 0xA0, 0xA0, 0x9F, 0x9F, 0x9F,
0x9F, 0x9F, 0x9F, 0x9E, 0x9E, 0x9E, 0xAE, 0xAE, 0xAE, 0xAD, 0xAD, 0xAD, 0xAD, 0xAD, 0xAD, 0xAD,
0xAD, 0xAD, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAF, 0xAF, 0xAF, 0xAF, 0xAF,
0xAF, 0xAF, 0xAF, 0xAF, 0xAF, 0xAF, 0xAF, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE, 0xAE,
0xAD, 0xAD, 0xAD, 0xAD, 0xAD, 0xAD, 0xC8, 0xC8, 0xC8, 0xC9, 0xC9, 0xC9, 0xC9, 0xC9, 0xC9, 0xC9,
0xC9, 0xC9, 0xC9, 0xC9, 0xC9, 0xC9, 0xC9, 0xC9, 0xCA, 0xCA, 0xCA, 0xCA, 0xCA, 0xCA, 0xCA, 0xCA,
0xCA, 0xCA, 0xCA, 0xCA, 0xCA, 0xCA, 0xCA, 0xCA, 0xCA, 0xCA, 0xC9, 0xC9, 0xC9, 0xC9, 0xC9, 0xC9,
0xC9, 0xC9, 0xC9, 0xC9, 0xC9, 0xC9, 0xA0, 0xA0, 0xA0, 0xA1, 0xA1, 0xA1, 0xA2, 0xA2, 0xA2, 0xA2,
0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2,
0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2,
0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0x36, 0x36, 0x36, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37,
0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37,
0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37,
0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x0A, 0x0A, 0x0A, 0x0A, 0x0A, 0x0A, 0x0B, 0x0B, 0x0B, 0x0B,
0x0B, 0x0B, 0x0A, 0x0A, 0x0A, 0x0A, 0x0A, 0x0A, 0x0A, 0x0A, 0x0A, 0x0A, 0x0A, 0x0A, 0x09, 0x09,
0x09, 0x09, 0x09, 0x09, 0x0A, 0x0A, 0x0A, 0x0A, 0x0A, 0x0A, 0x0A, 0x0A, 0x0A, 0x0A, 0x0A, 0x0A,
0x0B, 0x0B, 0x0B, 0x0B, 0x0B, 0x0B, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C,
0x1C, 0x1C, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1A, 0x1A,
0x1A, 0x1A, 0x1A, 0x1A, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B,
0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x25,
0x25, 0x25, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24,
0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x25, 0x25, 0x25,
0x25, 0x25, 0x25, 0x26, 0x26, 0x26, 0x28, 0x28, 0x28, 0x29, 0x29, 0x29, 0x28, 0x28, 0x28, 0x28,
0x28, 0x28, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26, 0x26, 0x26,
0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x27, 0x27, 0x27,
0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x28, 0x28, 0x28, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29,
0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x27, 0x27,
0x27, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x27, 0x27, 0x27,
0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x28,
0x28, 0x28, 0x29, 0x29, 0x29, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29, 0x28, 0x28,
0x28, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x25, 0x25, 0x25, 0x26, 0x26, 0x26,
0x28, 0x28, 0x28, 0x29, 0x29, 0x29, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x26, 0x26, 0x26, 0x28,
0x28, 0x28, 0x29, 0x29, 0x29, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29, 0x28, 0x28,
0x28, 0x26, 0x26, 0x26, 0x24, 0x24, 0x24, 0x23, 0x23, 0x23, 0x24, 0x24, 0x24, 0x26, 0x26, 0x26,
0x28, 0x28, 0x28, 0x2B, 0x2B, 0x2B, 0x27, 0x27, 0x27, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x26,
0x26, 0x26, 0x28, 0x28, 0x28, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29, 0x28, 0x28,
0x28, 0x25, 0x25, 0x25, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x26, 0x26, 0x26,
0x29, 0x29, 0x29, 0x2C, 0x2C, 0x2C,
};
	unsigned char logo_data_mid[] = {
0x42, 0x4D, 0x36, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x36, 0x00, 0x00, 0x00, 0x28, 0x00,
0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x01, 0x00, 0x18, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28,
0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28,
0x28, 0x27, 0x27, 0x27, 0x29, 0x29, 0x29, 0x2C, 0x2C, 0x2C, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D,
0x2B, 0x2B, 0x2B, 0x28, 0x28, 0x28, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29,
0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x2A, 0x2A, 0x2A, 0x2D, 0x2D, 0x2D, 0x2B, 0x2B,
0x2B, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x29, 0x29, 0x29, 0x2B, 0x2B, 0x2B, 0x2C, 0x2C, 0x2C,
0x2A, 0x2A, 0x2A, 0x25, 0x25, 0x25, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D,
0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2D, 0x2F, 0x2F, 0x2F, 0x32, 0x32, 0x32, 0x30, 0x30,
0x30, 0x29, 0x29, 0x29, 0x27, 0x27, 0x27, 0x2A, 0x2A, 0x2A, 0x2C, 0x2C, 0x2C, 0x2D, 0x2D, 0x2D,
0x2B, 0x2B, 0x2B, 0x27, 0x27, 0x27, 0x34, 0x34, 0x34, 0x34, 0x34, 0x34, 0x34, 0x34, 0x34, 0x35,
0x35, 0x35, 0x35, 0x35, 0x35, 0x35, 0x35, 0x35, 0x36, 0x36, 0x36, 0x37, 0x37, 0x37, 0x35, 0x35,
0x35, 0x31, 0x31, 0x31, 0x2F, 0x2F, 0x2F, 0x31, 0x31, 0x31, 0x31, 0x31, 0x31, 0x30, 0x30, 0x30,
0x2E, 0x2E, 0x2E, 0x2D, 0x2D, 0x2D, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B,
0x2B, 0x2B, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2B, 0x2B,
0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x2A, 0x2A, 0x2A, 0x28, 0x28, 0x28,
0x29, 0x29, 0x29, 0x2D, 0x2D, 0x2D, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10,
0x10, 0x10, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x0F, 0x0F, 0x0F, 0x11, 0x11,
0x11, 0x17, 0x17, 0x17, 0x1A, 0x1A, 0x1A, 0x19, 0x19, 0x19, 0x18, 0x18, 0x18, 0x15, 0x15, 0x15,
0x1A, 0x1A, 0x1A, 0x27, 0x27, 0x27, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29,
0x29, 0x29, 0x29, 0x29, 0x29, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29, 0x28, 0x28, 0x28, 0x29, 0x29,
0x29, 0x2D, 0x2D, 0x2D, 0x2B, 0x2B, 0x2B, 0x23, 0x23, 0x23, 0x1B, 0x1B, 0x1B, 0x10, 0x10, 0x10,
0x12, 0x12, 0x12, 0x1F, 0x1F, 0x1F, 0x76, 0x76, 0x76, 0x76, 0x76, 0x76, 0x76, 0x76, 0x76, 0x76,
0x76, 0x76, 0x76, 0x76, 0x76, 0x76, 0x76, 0x76, 0x76, 0x76, 0x76, 0x76, 0x76, 0x76, 0x72, 0x72,
0x72, 0x6C, 0x6C, 0x6C, 0x5E, 0x5E, 0x5E, 0x49, 0x49, 0x49, 0x33, 0x33, 0x33, 0x1A, 0x1A, 0x1A,
0x10, 0x10, 0x10, 0x16, 0x16, 0x16, 0x95, 0x95, 0x95, 0x95, 0x95, 0x95, 0x94, 0x94, 0x94, 0x94,
0x94, 0x94, 0x94, 0x94, 0x94, 0x94, 0x94, 0x94, 0x95, 0x95, 0x95, 0x95, 0x95, 0x95, 0x92, 0x92,
0x92, 0x8A, 0x8A, 0x8A, 0x7D, 0x7D, 0x7D, 0x6C, 0x6C, 0x6C, 0x53, 0x53, 0x53, 0x33, 0x33, 0x33,
0x1E, 0x1E, 0x1E, 0x16, 0x16, 0x16, 0x86, 0x86, 0x86, 0x86, 0x86, 0x86, 0x85, 0x85, 0x85, 0x85,
0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x87, 0x87, 0x87, 0x88, 0x88,
0x88, 0x88, 0x88, 0x88, 0x89, 0x89, 0x89, 0x8B, 0x8B, 0x8B, 0x7B, 0x7B, 0x7B, 0x5A, 0x5A, 0x5A,
0x3B, 0x3B, 0x3B, 0x1E, 0x1E, 0x1E, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x82, 0x82, 0x82, 0x83, 0x83,
0x83, 0x84, 0x84, 0x84, 0x8B, 0x8B, 0x8B, 0x96, 0x96, 0x96, 0x91, 0x91, 0x91, 0x7A, 0x7A, 0x7A,
0x56, 0x56, 0x56, 0x24, 0x24, 0x24, 0x86, 0x86, 0x86, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85,
0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x86, 0x86, 0x86, 0x87, 0x87, 0x87, 0x84, 0x84,
0x84, 0x7E, 0x7E, 0x7E, 0x81, 0x81, 0x81, 0x8D, 0x8D, 0x8D, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93,
0x6F, 0x6F, 0x6F, 0x28, 0x28, 0x28, 0x89, 0x89, 0x89, 0x89, 0x89, 0x89, 0x89, 0x89, 0x89, 0x89,
0x89, 0x89, 0x89, 0x89, 0x89, 0x89, 0x89, 0x89, 0x8A, 0x8A, 0x8A, 0x8D, 0x8D, 0x8D, 0x8A, 0x8A,
0x8A, 0x82, 0x82, 0x82, 0x83, 0x83, 0x83, 0x8C, 0x8C, 0x8C, 0x96, 0x96, 0x96, 0xA0, 0xA0, 0xA0,
0x7C, 0x7C, 0x7C, 0x29, 0x29, 0x29, 0x8C, 0x8C, 0x8C, 0x8C, 0x8C, 0x8C, 0x8C, 0x8C, 0x8C, 0x8C,
0x8C, 0x8C, 0x8C, 0x8C, 0x8C, 0x8C, 0x8C, 0x8C, 0x8F, 0x8F, 0x8F, 0x93, 0x93, 0x93, 0x94, 0x94,
0x94, 0x91, 0x91, 0x91, 0x90, 0x90, 0x90, 0x92, 0x92, 0x92, 0x98, 0x98, 0x98, 0xA1, 0xA1, 0xA1,
0x7C, 0x7C, 0x7C, 0x29, 0x29, 0x29, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90,
0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x91, 0x91, 0x91, 0x94, 0x94, 0x94, 0x95, 0x95,
0x95, 0x95, 0x95, 0x95, 0x94, 0x94, 0x94, 0x94, 0x94, 0x94, 0x99, 0x99, 0x99, 0xA3, 0xA3, 0xA3,
0x7F, 0x7F, 0x7F, 0x2B, 0x2B, 0x2B, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93,
0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x93, 0x92, 0x92, 0x92, 0x8E, 0x8E, 0x8E, 0x8D, 0x8D,
0x8D, 0x8E, 0x8E, 0x8E, 0x8F, 0x8F, 0x8F, 0x90, 0x90, 0x90, 0x99, 0x99, 0x99, 0xA8, 0xA8, 0xA8,
0x85, 0x85, 0x85, 0x2E, 0x2E, 0x2E, 0x97, 0x97, 0x97, 0x97, 0x97, 0x97, 0x97, 0x97, 0x97, 0x97,
0x97, 0x97, 0x97, 0x97, 0x97, 0x97, 0x97, 0x97, 0x95, 0x95, 0x95, 0x92, 0x92, 0x92, 0x90, 0x90,
0x90, 0x90, 0x90, 0x90, 0x91, 0x91, 0x91, 0x94, 0x94, 0x94, 0x9C, 0x9C, 0x9C, 0xAA, 0xAA, 0xAA,
0x85, 0x85, 0x85, 0x2E, 0x2E, 0x2E, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B,
0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9B, 0x9C, 0x9C, 0x9C, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E,
0x9E, 0x9A, 0x9A, 0x9A, 0x9A, 0x9A, 0x9A, 0x9D, 0x9D, 0x9D, 0xA2, 0xA2, 0xA2, 0xA7, 0xA7, 0xA7,
0x80, 0x80, 0x80, 0x2B, 0x2B, 0x2B, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E,
0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9F, 0x9F, 0x9F, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1,
0xA1, 0xA0, 0xA0, 0xA0, 0xA1, 0xA1, 0xA1, 0xA6, 0xA6, 0xA6, 0xA4, 0xA4, 0xA4, 0x9C, 0x9C, 0x9C,
0x73, 0x73, 0x73, 0x29, 0x29, 0x29, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F,
0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x9D, 0x9D, 0x9D, 0x99, 0x99, 0x99, 0x9B, 0x9B,
0x9B, 0xA3, 0xA3, 0xA3, 0xA9, 0xA9, 0xA9, 0xAD, 0xAD, 0xAD, 0xA3, 0xA3, 0xA3, 0x8A, 0x8A, 0x8A,
0x61, 0x61, 0x61, 0x26, 0x26, 0x26, 0xAD, 0xAD, 0xAD, 0xAD, 0xAD, 0xAD, 0xAD, 0xAD, 0xAD, 0xAD,
0xAD, 0xAD, 0xAD, 0xAD, 0xAD, 0xAD, 0xAD, 0xAD, 0xAB, 0xAB, 0xAB, 0xA8, 0xA8, 0xA8, 0xA8, 0xA8,
0xA8, 0xAD, 0xAD, 0xAD, 0xAC, 0xAC, 0xAC, 0xA5, 0xA5, 0xA5, 0x8F, 0x8F, 0x8F, 0x6A, 0x6A, 0x6A,
0x44, 0x44, 0x44, 0x1F, 0x1F, 0x1F, 0xC8, 0xC8, 0xC8, 0xC8, 0xC8, 0xC8, 0xC8, 0xC8, 0xC8, 0xC8,
0xC8, 0xC8, 0xC8, 0xC8, 0xC8, 0xC8, 0xC8, 0xC8, 0xCA, 0xCA, 0xCA, 0xCC, 0xCC, 0xCC, 0xC8, 0xC8,
0xC8, 0xBF, 0xBF, 0xBF, 0xAC, 0xAC, 0xAC, 0x8F, 0x8F, 0x8F, 0x69, 0x69, 0x69, 0x3A, 0x3A, 0x3A,
0x1D, 0x1D, 0x1D, 0x12, 0x12, 0x12, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1,
0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0xA3, 0xA3, 0xA3, 0xA7, 0xA7, 0xA7, 0xA3, 0xA3,
0xA3, 0x98, 0x98, 0x98, 0x83, 0x83, 0x83, 0x66, 0x66, 0x66, 0x44, 0x44, 0x44, 0x1C, 0x1C, 0x1C,
0x0B, 0x0B, 0x0B, 0x10, 0x10, 0x10, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37,
0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x37, 0x38, 0x38, 0x38, 0x38, 0x38,
0x38, 0x37, 0x37, 0x37, 0x32, 0x32, 0x32, 0x2B, 0x2B, 0x2B, 0x20, 0x20, 0x20, 0x11, 0x11, 0x11,
0x0F, 0x0F, 0x0F, 0x19, 0x19, 0x19, 0x0B, 0x0B, 0x0B, 0x0B, 0x0B, 0x0B, 0x0B, 0x0B, 0x0B, 0x0B,
0x0B, 0x0B, 0x0B, 0x0B, 0x0B, 0x0B, 0x0B, 0x0B, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0D, 0x0D,
0x0D, 0x0E, 0x0E, 0x0E, 0x10, 0x10, 0x10, 0x14, 0x14, 0x14, 0x15, 0x15, 0x15, 0x14, 0x14, 0x14,
0x18, 0x18, 0x18, 0x20, 0x20, 0x20, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E,
0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x20, 0x20, 0x20, 0x24, 0x24, 0x24, 0x23, 0x23,
0x23, 0x1E, 0x1E, 0x1E, 0x1D, 0x1D, 0x1D, 0x1F, 0x1F, 0x1F, 0x21, 0x21, 0x21, 0x25, 0x25, 0x25,
0x26, 0x26, 0x26, 0x24, 0x24, 0x24, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28,
0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x2A, 0x2A, 0x2A, 0x2D, 0x2D, 0x2D, 0x2C, 0x2C,
0x2C, 0x27, 0x27, 0x27, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x27, 0x27, 0x27, 0x2B, 0x2B, 0x2B,
0x2B, 0x2B, 0x2B, 0x27, 0x27, 0x27, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x2A,
0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x29, 0x29,
0x29, 0x29, 0x29, 0x29, 0x28, 0x28, 0x28, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x28, 0x28, 0x28,
0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A,
0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29,
0x29, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x28, 0x28, 0x28, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26,
0x27, 0x27, 0x27, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29, 0x28,
0x28, 0x28, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26, 0x27, 0x27, 0x27, 0x2A, 0x2A, 0x2A, 0x2C, 0x2C,
0x2C, 0x2D, 0x2D, 0x2D, 0x2C, 0x2C, 0x2C, 0x29, 0x29, 0x29, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27,
0x29, 0x29, 0x29, 0x2D, 0x2D, 0x2D, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29, 0x28, 0x28, 0x28, 0x26,
0x26, 0x26, 0x25, 0x25, 0x25, 0x23, 0x23, 0x23, 0x25, 0x25, 0x25, 0x29, 0x29, 0x29, 0x2C, 0x2C,
0x2C, 0x2D, 0x2D, 0x2D, 0x2C, 0x2C, 0x2C, 0x29, 0x29, 0x29, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27,
0x29, 0x29, 0x29, 0x2E, 0x2E, 0x2E, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29, 0x27, 0x27, 0x27, 0x26,
0x26, 0x26, 0x24, 0x24, 0x24, 0x23, 0x23, 0x23, 0x24, 0x24, 0x24, 0x27, 0x27, 0x27, 0x2A, 0x2A,
0x2A, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x28, 0x28, 0x28, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26,
0x28, 0x28, 0x28, 0x2C, 0x2C, 0x2C,
};
	unsigned char logo_data_set0[] = {
0x42, 0x4D, 0x36, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x36, 0x00, 0x00, 0x00, 0x28, 0x00,
0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x01, 0x00, 0x18, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x26,
0x26, 0x26, 0x27, 0x27, 0x27, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28,
0x28, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A,
0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x24, 0x24, 0x24, 0x22, 0x22, 0x22, 0x23, 0x23, 0x23, 0x24,
0x24, 0x24, 0x24, 0x24, 0x24, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x26, 0x26,
0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27,
0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x28, 0x28, 0x28, 0x26, 0x26, 0x26, 0x27, 0x27, 0x27, 0x27,
0x27, 0x27, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x29, 0x29, 0x29, 0x29, 0x29,
0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29,
0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x32, 0x32, 0x32, 0x30, 0x30, 0x30, 0x31, 0x31, 0x31, 0x31,
0x31, 0x31, 0x31, 0x31, 0x31, 0x31, 0x31, 0x31, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32,
0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x31, 0x31, 0x31, 0x31, 0x31, 0x31,
0x31, 0x31, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x2E, 0x2E, 0x2E, 0x2F, 0x2F, 0x2F, 0x2F,
0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
0x30, 0x30, 0x30, 0x30, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2F, 0x2E, 0x2E, 0x2E, 0x2D, 0x2D, 0x2D,
0x2D, 0x2D, 0x2D, 0x2C, 0x2C, 0x2C, 0x22, 0x22, 0x22, 0x21, 0x21, 0x21, 0x21, 0x21, 0x21, 0x21,
0x21, 0x21, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x23, 0x23, 0x23, 0x23, 0x23,
0x23, 0x22, 0x22, 0x22, 0x21, 0x21, 0x21, 0x20, 0x20, 0x20, 0x1F, 0x1F, 0x1F, 0x1E, 0x1E, 0x1E,
0x1D, 0x1D, 0x1D, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C,
0x1C, 0x1C, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E,
0x1E, 0x1E, 0x1E, 0x1E, 0x1D, 0x1D, 0x1D, 0x1C, 0x1C, 0x1C, 0x1A, 0x1A, 0x1A, 0x19, 0x19, 0x19,
0x18, 0x18, 0x18, 0x19, 0x19, 0x19, 0x22, 0x22, 0x22, 0x21, 0x21, 0x21, 0x21, 0x21, 0x21, 0x21,
0x21, 0x21, 0x21, 0x21, 0x21, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x23, 0x23, 0x23, 0x23, 0x23,
0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x21, 0x21, 0x21, 0x20, 0x20, 0x20, 0x1F, 0x1F, 0x1F,
0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x23, 0x23, 0x23, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
0x22, 0x22, 0x23, 0x23, 0x23, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x25, 0x25, 0x25, 0x26, 0x26,
0x26, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x23, 0x23, 0x23, 0x21, 0x21, 0x21,
0x20, 0x20, 0x20, 0x1F, 0x1F, 0x1F, 0x22, 0x22, 0x22, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
0x20, 0x20, 0x21, 0x21, 0x21, 0x22, 0x22, 0x22, 0x24, 0x24, 0x24, 0x25, 0x25, 0x25, 0x26, 0x26,
0x26, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x23, 0x23, 0x23, 0x21, 0x21, 0x21,
0x20, 0x20, 0x20, 0x1F, 0x1F, 0x1F, 0x21, 0x21, 0x21, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F,
0x1F, 0x1F, 0x20, 0x20, 0x20, 0x21, 0x21, 0x21, 0x22, 0x22, 0x22, 0x23, 0x23, 0x23, 0x23, 0x23,
0x23, 0x24, 0x24, 0x24, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x22, 0x22, 0x22, 0x21, 0x21, 0x21,
0x20, 0x20, 0x20, 0x1F, 0x1F, 0x1F, 0x21, 0x21, 0x21, 0x20, 0x20, 0x20, 0x1F, 0x1F, 0x1F, 0x1F,
0x1F, 0x1F, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F,
0x1F, 0x1F, 0x1F, 0x1F, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
0x20, 0x20, 0x20, 0x21, 0x21, 0x21, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1E,
0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E,
0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1E, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x20, 0x20, 0x20,
0x20, 0x20, 0x20, 0x21, 0x21, 0x21, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1E,
0x1E, 0x1E, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1E, 0x1E, 0x1E,
0x1E, 0x1E, 0x1E, 0x1F, 0x1F, 0x1F, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1D, 0x1D, 0x1D, 0x1E,
0x1E, 0x1E, 0x1F, 0x1F, 0x1F, 0x20, 0x20, 0x20, 0x21, 0x21, 0x21, 0x21, 0x21, 0x21, 0x21, 0x21,
0x21, 0x21, 0x21, 0x21, 0x20, 0x20, 0x20, 0x1F, 0x1F, 0x1F, 0x1E, 0x1E, 0x1E, 0x1D, 0x1D, 0x1D,
0x1D, 0x1D, 0x1D, 0x1E, 0x1E, 0x1E, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1D, 0x1D, 0x1D, 0x1E,
0x1E, 0x1E, 0x1F, 0x1F, 0x1F, 0x20, 0x20, 0x20, 0x21, 0x21, 0x21, 0x22, 0x22, 0x22, 0x22, 0x22,
0x22, 0x21, 0x21, 0x21, 0x20, 0x20, 0x20, 0x1F, 0x1F, 0x1F, 0x1E, 0x1E, 0x1E, 0x1D, 0x1D, 0x1D,
0x1C, 0x1C, 0x1C, 0x1D, 0x1D, 0x1D, 0x1C, 0x1C, 0x1C, 0x1B, 0x1B, 0x1B, 0x1C, 0x1C, 0x1C, 0x1D,
0x1D, 0x1D, 0x1E, 0x1E, 0x1E, 0x1F, 0x1F, 0x1F, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
0x20, 0x20, 0x20, 0x20, 0x1F, 0x1F, 0x1F, 0x1E, 0x1E, 0x1E, 0x1D, 0x1D, 0x1D, 0x1C, 0x1C, 0x1C,
0x1B, 0x1B, 0x1B, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1C,
0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1C, 0x1C,
0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1A, 0x1A, 0x1A,
0x1A, 0x1A, 0x1A, 0x1B, 0x1B, 0x1B, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C,
0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B,
0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1A, 0x1A, 0x1A, 0x1A, 0x1A, 0x1A, 0x1A, 0x1A, 0x1A,
0x1A, 0x1A, 0x1A, 0x1B, 0x1B, 0x1B, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D,
0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1D, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C,
0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B,
0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1B, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C,
0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C,
0x1C, 0x1C, 0x1C, 0x1C, 0x1B, 0x1B, 0x1B, 0x1A, 0x1A, 0x1A, 0x1A, 0x1A, 0x1A, 0x19, 0x19, 0x19,
0x19, 0x19, 0x19, 0x19, 0x19, 0x19, 0x17, 0x17, 0x17, 0x17, 0x17, 0x17, 0x18, 0x18, 0x18, 0x18,
0x18, 0x18, 0x19, 0x19, 0x19, 0x1A, 0x1A, 0x1A, 0x1A, 0x1A, 0x1A, 0x1B, 0x1B, 0x1B, 0x1A, 0x1A,
0x1A, 0x19, 0x19, 0x19, 0x18, 0x18, 0x18, 0x17, 0x17, 0x17, 0x16, 0x16, 0x16, 0x15, 0x15, 0x15,
0x14, 0x14, 0x14, 0x15, 0x15, 0x15, 0x14, 0x14, 0x14, 0x14, 0x14, 0x14, 0x14, 0x14, 0x14, 0x15,
0x15, 0x15, 0x16, 0x16, 0x16, 0x17, 0x17, 0x17, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x17, 0x17,
0x17, 0x17, 0x17, 0x17, 0x15, 0x15, 0x15, 0x14, 0x14, 0x14, 0x12, 0x12, 0x12, 0x11, 0x11, 0x11,
0x10, 0x10, 0x10, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x12, 0x12, 0x12, 0x13,
0x13, 0x13, 0x14, 0x14, 0x14, 0x14, 0x14, 0x14, 0x15, 0x15, 0x15, 0x14, 0x14, 0x14, 0x14, 0x14,
0x14, 0x13, 0x13, 0x13, 0x12, 0x12, 0x12, 0x11, 0x11, 0x11, 0x0F, 0x0F, 0x0F, 0x0E, 0x0E, 0x0E,
0x0D, 0x0D, 0x0D, 0x0E, 0x0E, 0x0E, 0x17, 0x17, 0x17, 0x17, 0x17, 0x17, 0x17, 0x17, 0x17, 0x17,
0x17, 0x17, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x17, 0x17, 0x17, 0x17, 0x17,
0x17, 0x16, 0x16, 0x16, 0x15, 0x15, 0x15, 0x14, 0x14, 0x14, 0x13, 0x13, 0x13, 0x13, 0x13, 0x13,
0x12, 0x12, 0x12, 0x13, 0x13, 0x13, 0x23, 0x23, 0x23, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x23,
0x23, 0x23, 0x23, 0x23, 0x23, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x21, 0x21, 0x20, 0x20,
0x20, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F,
0x1F, 0x1F, 0x1F, 0x20, 0x20, 0x20, 0x2B, 0x2B, 0x2B, 0x2C, 0x2C, 0x2C, 0x2A, 0x2A, 0x2A, 0x29,
0x29, 0x29, 0x28, 0x28, 0x28, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x25, 0x25,
0x25, 0x25, 0x25, 0x25, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26,
0x26, 0x26, 0x26, 0x27, 0x27, 0x27, 0x2E, 0x2E, 0x2E, 0x2E, 0x2E, 0x2E, 0x29, 0x29, 0x29, 0x27,
0x27, 0x27, 0x28, 0x28, 0x28, 0x27, 0x27, 0x27, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x25, 0x25,
0x25, 0x27, 0x27, 0x27, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26,
0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x2D, 0x2D, 0x2D, 0x2C, 0x2C, 0x2C, 0x28, 0x28, 0x28, 0x27,
0x27, 0x27, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x26, 0x26,
0x26, 0x28, 0x28, 0x28, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26,
0x28, 0x28, 0x28, 0x29, 0x29, 0x29, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29, 0x26, 0x26, 0x26, 0x27,
0x27, 0x27, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x28, 0x28, 0x28, 0x27, 0x27, 0x27, 0x27, 0x27,
0x27, 0x28, 0x28, 0x28, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25,
0x27, 0x27, 0x27, 0x29, 0x29, 0x29, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x27,
0x27, 0x27, 0x2B, 0x2B, 0x2B, 0x2C, 0x2C, 0x2C, 0x29, 0x29, 0x29, 0x28, 0x28, 0x28, 0x27, 0x27,
0x27, 0x27, 0x27, 0x27, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25,
0x28, 0x28, 0x28, 0x29, 0x29, 0x29, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x26,
0x26, 0x26, 0x2B, 0x2B, 0x2B, 0x2C, 0x2C, 0x2C, 0x2A, 0x2A, 0x2A, 0x28, 0x28, 0x28, 0x26, 0x26,
0x26, 0x25, 0x25, 0x25, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x26, 0x26, 0x26,
0x29, 0x29, 0x29, 0x2A, 0x2A, 0x2A,
};
	unsigned char logo_data_right[] = {
0x42, 0x4D, 0x36, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x36, 0x00, 0x00, 0x00, 0x28, 0x00,
0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x01, 0x00, 0x18, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x25, 0x25, 0x25, 0x20, 0x20, 0x20, 0x1F, 0x1F, 0x1F, 0x22,
0x22, 0x22, 0x24, 0x24, 0x24, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x25, 0x25,
0x25, 0x27, 0x27, 0x27, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x27, 0x27, 0x27,
0x26, 0x26, 0x26, 0x27, 0x27, 0x27, 0x25, 0x25, 0x25, 0x20, 0x20, 0x20, 0x1F, 0x1F, 0x1F, 0x23,
0x23, 0x23, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x25, 0x25,
0x25, 0x28, 0x28, 0x28, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x27, 0x27, 0x27,
0x26, 0x26, 0x26, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26, 0x21, 0x21, 0x21, 0x20, 0x20, 0x20, 0x24,
0x24, 0x24, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x25, 0x25,
0x25, 0x27, 0x27, 0x27, 0x28, 0x28, 0x28, 0x29, 0x29, 0x29, 0x28, 0x28, 0x28, 0x27, 0x27, 0x27,
0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x28, 0x28, 0x28, 0x22, 0x22, 0x22, 0x21, 0x21, 0x21, 0x25,
0x25, 0x25, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x25, 0x25,
0x25, 0x27, 0x27, 0x27, 0x28, 0x28, 0x28, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x27, 0x27, 0x27,
0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x2B, 0x2B, 0x2B, 0x23, 0x23, 0x23, 0x21, 0x21, 0x21, 0x25,
0x25, 0x25, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x23, 0x23, 0x23, 0x24, 0x24,
0x24, 0x26, 0x26, 0x26, 0x28, 0x28, 0x28, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29,
0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x2A, 0x2A, 0x2A, 0x23, 0x23, 0x23, 0x22, 0x22, 0x22, 0x26,
0x26, 0x26, 0x28, 0x28, 0x28, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26, 0x23, 0x23, 0x23, 0x23, 0x23,
0x23, 0x25, 0x25, 0x25, 0x27, 0x27, 0x27, 0x29, 0x29, 0x29, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29,
0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x25, 0x25, 0x25, 0x29,
0x29, 0x29, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29, 0x27, 0x27, 0x27, 0x24, 0x24, 0x24, 0x23, 0x23,
0x23, 0x25, 0x25, 0x25, 0x26, 0x26, 0x26, 0x28, 0x28, 0x28, 0x29, 0x29, 0x29, 0x28, 0x28, 0x28,
0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x20, 0x20, 0x20, 0x23, 0x23, 0x23, 0x26, 0x26, 0x26, 0x2A,
0x2A, 0x2A, 0x2C, 0x2C, 0x2C, 0x2A, 0x2A, 0x2A, 0x28, 0x28, 0x28, 0x24, 0x24, 0x24, 0x23, 0x23,
0x23, 0x25, 0x25, 0x25, 0x26, 0x26, 0x26, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x27, 0x27, 0x27,
0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x21, 0x21, 0x21, 0x24, 0x24, 0x24, 0x27, 0x27, 0x27, 0x2B,
0x2B, 0x2B, 0x2D, 0x2D, 0x2D, 0x2C, 0x2C, 0x2C, 0x29, 0x29, 0x29, 0x25, 0x25, 0x25, 0x24, 0x24,
0x24, 0x25, 0x25, 0x25, 0x27, 0x27, 0x27, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x26, 0x26, 0x26,
0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x21, 0x21, 0x21, 0x23, 0x23, 0x23, 0x26, 0x26, 0x26, 0x2A,
0x2A, 0x2A, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x29, 0x29, 0x29, 0x26, 0x26, 0x26, 0x25, 0x25,
0x25, 0x26, 0x26, 0x26, 0x28, 0x28, 0x28, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x27, 0x27, 0x27,
0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x22, 0x22, 0x22, 0x28,
0x28, 0x28, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x28, 0x28, 0x28, 0x25, 0x25, 0x25, 0x25, 0x25,
0x25, 0x27, 0x27, 0x27, 0x29, 0x29, 0x29, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x2B, 0x2A, 0x2A, 0x2A,
0x28, 0x28, 0x28, 0x26, 0x26, 0x26, 0x1E, 0x1E, 0x1E, 0x1D, 0x1D, 0x1D, 0x20, 0x20, 0x20, 0x27,
0x27, 0x27, 0x29, 0x29, 0x29, 0x28, 0x28, 0x28, 0x26, 0x26, 0x26, 0x24, 0x24, 0x24, 0x24, 0x24,
0x24, 0x27, 0x27, 0x27, 0x29, 0x29, 0x29, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29,
0x28, 0x28, 0x28, 0x27, 0x27, 0x27, 0x1B, 0x1B, 0x1B, 0x1A, 0x1A, 0x1A, 0x1E, 0x1E, 0x1E, 0x27,
0x27, 0x27, 0x2A, 0x2A, 0x2A, 0x27, 0x27, 0x27, 0x24, 0x24, 0x24, 0x20, 0x20, 0x20, 0x21, 0x21,
0x21, 0x25, 0x25, 0x25, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27,
0x27, 0x27, 0x27, 0x28, 0x28, 0x28, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x1D, 0x1D, 0x1D, 0x28,
0x28, 0x28, 0x2B, 0x2B, 0x2B, 0x27, 0x27, 0x27, 0x23, 0x23, 0x23, 0x20, 0x20, 0x20, 0x20, 0x20,
0x20, 0x24, 0x24, 0x24, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25,
0x26, 0x26, 0x26, 0x27, 0x27, 0x27, 0x14, 0x14, 0x14, 0x17, 0x17, 0x17, 0x1E, 0x1E, 0x1E, 0x29,
0x29, 0x29, 0x2C, 0x2C, 0x2C, 0x28, 0x28, 0x28, 0x25, 0x25, 0x25, 0x22, 0x22, 0x22, 0x22, 0x22,
0x22, 0x25, 0x25, 0x25, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x23, 0x23, 0x23,
0x23, 0x23, 0x23, 0x24, 0x24, 0x24, 0x14, 0x14, 0x14, 0x16, 0x16, 0x16, 0x1C, 0x1C, 0x1C, 0x27,
0x27, 0x27, 0x2B, 0x2B, 0x2B, 0x28, 0x28, 0x28, 0x25, 0x25, 0x25, 0x23, 0x23, 0x23, 0x23, 0x23,
0x23, 0x26, 0x26, 0x26, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25,
0x24, 0x24, 0x24, 0x25, 0x25, 0x25, 0x17, 0x17, 0x17, 0x13, 0x13, 0x13, 0x17, 0x17, 0x17, 0x22,
0x22, 0x22, 0x27, 0x27, 0x27, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x23, 0x23, 0x23, 0x24, 0x24,
0x24, 0x28, 0x28, 0x28, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29,
0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x19, 0x19, 0x19, 0x14, 0x14, 0x14, 0x17, 0x17, 0x17, 0x21,
0x21, 0x21, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x25, 0x25,
0x25, 0x29, 0x29, 0x29, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29,
0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x1A, 0x1A, 0x1A, 0x18, 0x18, 0x18, 0x1B, 0x1B, 0x1B, 0x24,
0x24, 0x24, 0x28, 0x28, 0x28, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x26, 0x26,
0x26, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26,
0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x1A, 0x1A, 0x1A, 0x1A, 0x1A, 0x1A, 0x1F, 0x1F, 0x1F, 0x27,
0x27, 0x27, 0x2A, 0x2A, 0x2A, 0x28, 0x28, 0x28, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27,
0x27, 0x28, 0x28, 0x28, 0x27, 0x27, 0x27, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24,
0x24, 0x24, 0x24, 0x23, 0x23, 0x23, 0x17, 0x17, 0x17, 0x1C, 0x1C, 0x1C, 0x22, 0x22, 0x22, 0x28,
0x28, 0x28, 0x2B, 0x2B, 0x2B, 0x29, 0x29, 0x29, 0x28, 0x28, 0x28, 0x27, 0x27, 0x27, 0x27, 0x27,
0x27, 0x27, 0x27, 0x27, 0x25, 0x25, 0x25, 0x23, 0x23, 0x23, 0x22, 0x22, 0x22, 0x24, 0x24, 0x24,
0x23, 0x23, 0x23, 0x22, 0x22, 0x22, 0x17, 0x17, 0x17, 0x1D, 0x1D, 0x1D, 0x22, 0x22, 0x22, 0x28,
0x28, 0x28, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29, 0x28, 0x28, 0x28, 0x27, 0x27, 0x27, 0x27, 0x27,
0x27, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x24, 0x24, 0x24,
0x24, 0x24, 0x24, 0x22, 0x22, 0x22, 0x18, 0x18, 0x18, 0x1D, 0x1D, 0x1D, 0x21, 0x21, 0x21, 0x26,
0x26, 0x26, 0x28, 0x28, 0x28, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27,
0x27, 0x26, 0x26, 0x26, 0x24, 0x24, 0x24, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x26, 0x26, 0x26,
0x27, 0x27, 0x27, 0x25, 0x25, 0x25, 0x1B, 0x1B, 0x1B, 0x1F, 0x1F, 0x1F, 0x23, 0x23, 0x23, 0x26,
0x26, 0x26, 0x28, 0x28, 0x28, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x28, 0x28, 0x28, 0x27, 0x27,
0x27, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x22, 0x22, 0x22, 0x23, 0x23, 0x23, 0x26, 0x26, 0x26,
0x27, 0x27, 0x27, 0x25, 0x25, 0x25, 0x1F, 0x1F, 0x1F, 0x23, 0x23, 0x23, 0x26, 0x26, 0x26, 0x29,
0x29, 0x29, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x2A, 0x2A, 0x2A, 0x28, 0x28,
0x28, 0x25, 0x25, 0x25, 0x22, 0x22, 0x22, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x23, 0x23, 0x23,
0x23, 0x23, 0x23, 0x21, 0x21, 0x21, 0x22, 0x22, 0x22, 0x24, 0x24, 0x24, 0x26, 0x26, 0x26, 0x29,
0x29, 0x29, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29, 0x29, 0x29, 0x29, 0x2A, 0x2A, 0x2A, 0x29, 0x29,
0x29, 0x26, 0x26, 0x26, 0x23, 0x23, 0x23, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x23, 0x23, 0x23,
0x23, 0x23, 0x23, 0x21, 0x21, 0x21, 0x25, 0x25, 0x25, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x25,
0x25, 0x25, 0x26, 0x26, 0x26, 0x27, 0x27, 0x27, 0x28, 0x28, 0x28, 0x2A, 0x2A, 0x2A, 0x2A, 0x2A,
0x2A, 0x27, 0x27, 0x27, 0x24, 0x24, 0x24, 0x22, 0x22, 0x22, 0x23, 0x23, 0x23, 0x26, 0x26, 0x26,
0x27, 0x27, 0x27, 0x24, 0x24, 0x24, 0x27, 0x27, 0x27, 0x23, 0x23, 0x23, 0x22, 0x22, 0x22, 0x24,
0x24, 0x24, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x27, 0x27, 0x27, 0x28, 0x28, 0x28, 0x28, 0x28,
0x28, 0x26, 0x26, 0x26, 0x24, 0x24, 0x24, 0x23, 0x23, 0x23, 0x24, 0x24, 0x24, 0x28, 0x28, 0x28,
0x29, 0x29, 0x29, 0x28, 0x28, 0x28, 0x29, 0x29, 0x29, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x26,
0x26, 0x26, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x24, 0x24,
0x24, 0x23, 0x23, 0x23, 0x22, 0x22, 0x22, 0x23, 0x23, 0x23, 0x24, 0x24, 0x24, 0x27, 0x27, 0x27,
0x29, 0x29, 0x29, 0x2A, 0x2A, 0x2A, 0x29, 0x29, 0x29, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26, 0x27,
0x27, 0x27, 0x27, 0x27, 0x27, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x24, 0x24,
0x24, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x23, 0x24, 0x24, 0x24, 0x26, 0x26, 0x26,
0x28, 0x28, 0x28, 0x2A, 0x2A, 0x2A, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x25, 0x25, 0x25, 0x26,
0x26, 0x26, 0x27, 0x27, 0x27, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x27, 0x27,
0x27, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x25, 0x25, 0x25,
0x25, 0x25, 0x25, 0x26, 0x26, 0x26, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x26,
0x26, 0x26, 0x27, 0x27, 0x27, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x28, 0x27, 0x27,
0x27, 0x26, 0x26, 0x26, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24,
0x24, 0x24, 0x24, 0x25, 0x25, 0x25,
};
