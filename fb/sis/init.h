/* $XFree86$ */
/* $XdotOrg$ */
/*
 * Data and prototypes for init.c
 *
 * Copyright (C) 2001-2005 by <PERSON>, Vienna, Austria
 *
 * If distributed as part of the Linux kernel, the following license terms
 * apply:
 *
 * * This program is free software; you can redistribute it and/or modify
 * * it under the terms of the GNU General Public License as published by
 * * the Free Software Foundation; either version 2 of the named License,
 * * or any later version.
 * *
 * * This program is distributed in the hope that it will be useful,
 * * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * * GNU General Public License for more details.
 * *
 * * You should have received a copy of the GNU General Public License
 * * along with this program; if not, write to the Free Software
 * * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-1307, USA
 *
 * Otherwise, the following license terms apply:
 *
 * * Redistribution and use in source and binary forms, with or without
 * * modification, are permitted provided that the following conditions
 * * are met:
 * * 1) Redistributions of source code must retain the above copyright
 * *    notice, this list of conditions and the following disclaimer.
 * * 2) Redistributions in binary form must reproduce the above copyright
 * *    notice, this list of conditions and the following disclaimer in the
 * *    documentation and/or other materials provided with the distribution.
 * * 3) The name of the author may not be used to endorse or promote products
 * *    derived from this software without specific prior written permission.
 * *
 * * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
 * * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * * IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
 * * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
 * * NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
 * * THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * Author: 	Thomas Winischhofer <<EMAIL>>
 *
 */

#ifndef _INIT_H_
#define _INIT_H_

#include "osdef.h"
#include "initdef.h"

#ifdef SIS_XORG_XF86
#include "sis.h"
#define SIS_NEED_inSISREG
#define SIS_NEED_inSISREGW
#define SIS_NEED_inSISREGL
#define SIS_NEED_outSISREG
#define SIS_NEED_outSISREGW
#define SIS_NEED_outSISREGL
#include "sis_regs.h"
#endif

#ifdef SIS_LINUX_KERNEL
#include "vgatypes.h"
#include "vstruct.h"
#ifdef SIS_CP
#undef SIS_CP
#endif
#include "sis.h"
#endif

/* Mode numbers */
static const unsigned short ModeIndex_320x200[]      = {0x59, 0x41, 0x00, 0x4f};
static const unsigned short ModeIndex_320x240[]      = {0x50, 0x56, 0x00, 0x53};
static const unsigned short ModeIndex_320x240_FSTN[] = {0x5a, 0x5b, 0x00, 0x00};  /* FSTN */
static const unsigned short ModeIndex_400x300[]      = {0x51, 0x57, 0x00, 0x54};
static const unsigned short ModeIndex_512x384[]      = {0x52, 0x58, 0x00, 0x5c};
static const unsigned short ModeIndex_640x400[]      = {0x2f, 0x5d, 0x00, 0x5e};
static const unsigned short ModeIndex_640x480[]      = {0x2e, 0x44, 0x00, 0x62};
static const unsigned short ModeIndex_720x480[]      = {0x31, 0x33, 0x00, 0x35};
static const unsigned short ModeIndex_720x576[]      = {0x32, 0x34, 0x00, 0x36};
static const unsigned short ModeIndex_768x576[]      = {0x5f, 0x60, 0x00, 0x61};
static const unsigned short ModeIndex_800x480[]      = {0x70, 0x7a, 0x00, 0x76};
static const unsigned short ModeIndex_800x600[]      = {0x30, 0x47, 0x00, 0x63};
static const unsigned short ModeIndex_848x480[]      = {0x39, 0x3b, 0x00, 0x3e};
static const unsigned short ModeIndex_856x480[]      = {0x3f, 0x42, 0x00, 0x45};
static const unsigned short ModeIndex_960x540[]      = {0x1d, 0x1e, 0x00, 0x1f};  /* 315 series only */
static const unsigned short ModeIndex_960x600[]      = {0x20, 0x21, 0x00, 0x22};  /* 315 series only */
static const unsigned short ModeIndex_1024x768[]     = {0x38, 0x4a, 0x00, 0x64};
static const unsigned short ModeIndex_1024x576[]     = {0x71, 0x74, 0x00, 0x77};
static const unsigned short ModeIndex_1024x600[]     = {0x20, 0x21, 0x00, 0x22};  /* 300 series only */
static const unsigned short ModeIndex_1280x1024[]    = {0x3a, 0x4d, 0x00, 0x65};
static const unsigned short ModeIndex_1280x960[]     = {0x7c, 0x7d, 0x00, 0x7e};
static const unsigned short ModeIndex_1152x768[]     = {0x23, 0x24, 0x00, 0x25};  /* 300 series only */
static const unsigned short ModeIndex_1152x864[]     = {0x29, 0x2a, 0x00, 0x2b};
static const unsigned short ModeIndex_300_1280x768[] = {0x55, 0x5a, 0x00, 0x5b};
static const unsigned short ModeIndex_310_1280x768[] = {0x23, 0x24, 0x00, 0x25};
static const unsigned short ModeIndex_1280x720[]     = {0x79, 0x75, 0x00, 0x78};
static const unsigned short ModeIndex_1280x800[]     = {0x14, 0x15, 0x00, 0x16};
static const unsigned short ModeIndex_1280x854[]     = {0x1a, 0x1b, 0x00, 0x1c};
static const unsigned short ModeIndex_1360x768[]     = {0x48, 0x4b, 0x00, 0x4e};
static const unsigned short ModeIndex_300_1360x1024[]= {0x67, 0x6f, 0x00, 0x72};  /* 300 series, BARCO only */
static const unsigned short ModeIndex_1400x1050[]    = {0x26, 0x27, 0x00, 0x28};  /* 315 series only */
static const unsigned short ModeIndex_1680x1050[]    = {0x17, 0x18, 0x00, 0x19};  /* 315 series only */
static const unsigned short ModeIndex_1600x1200[]    = {0x3c, 0x3d, 0x00, 0x66};
static const unsigned short ModeIndex_1920x1080[]    = {0x2c, 0x2d, 0x00, 0x73};  /* 315 series only */
static const unsigned short ModeIndex_1920x1440[]    = {0x68, 0x69, 0x00, 0x6b};
static const unsigned short ModeIndex_300_2048x1536[]= {0x6c, 0x6d, 0x00, 0x00};
static const unsigned short ModeIndex_310_2048x1536[]= {0x6c, 0x6d, 0x00, 0x6e};

static const unsigned short SiS_DRAMType[17][5]={
	{0x0C,0x0A,0x02,0x40,0x39},
	{0x0D,0x0A,0x01,0x40,0x48},
	{0x0C,0x09,0x02,0x20,0x35},
	{0x0D,0x09,0x01,0x20,0x44},
	{0x0C,0x08,0x02,0x10,0x31},
	{0x0D,0x08,0x01,0x10,0x40},
	{0x0C,0x0A,0x01,0x20,0x34},
	{0x0C,0x09,0x01,0x08,0x32},
	{0x0B,0x08,0x02,0x08,0x21},
	{0x0C,0x08,0x01,0x08,0x30},
	{0x0A,0x08,0x02,0x04,0x11},
	{0x0B,0x0A,0x01,0x10,0x28},
	{0x09,0x08,0x02,0x02,0x01},
	{0x0B,0x09,0x01,0x08,0x24},
	{0x0B,0x08,0x01,0x04,0x20},
	{0x0A,0x08,0x01,0x02,0x10},
	{0x09,0x08,0x01,0x01,0x00}
};

static const unsigned short SiS_SDRDRAM_TYPE[13][5] =
{
	{ 2,12, 9,64,0x35},
	{ 1,13, 9,64,0x44},
	{ 2,12, 8,32,0x31},
	{ 2,11, 9,32,0x25},
	{ 1,12, 9,32,0x34},
	{ 1,13, 8,32,0x40},
	{ 2,11, 8,16,0x21},
	{ 1,12, 8,16,0x30},
	{ 1,11, 9,16,0x24},
	{ 1,11, 8, 8,0x20},
	{ 2, 9, 8, 4,0x01},
	{ 1,10, 8, 4,0x10},
	{ 1, 9, 8, 2,0x00}
};

static const unsigned short SiS_DDRDRAM_TYPE[4][5] =
{
	{ 2,12, 9,64,0x35},
	{ 2,12, 8,32,0x31},
	{ 2,11, 8,16,0x21},
	{ 2, 9, 8, 4,0x01}
};

static const unsigned char SiS_MDA_DAC[] =
{
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x15,0x15,0x15,0x15,0x15,0x15,0x15,0x15,
        0x15,0x15,0x15,0x15,0x15,0x15,0x15,0x15,
        0x3F,0x3F,0x3F,0x3F,0x3F,0x3F,0x3F,0x3F,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x15,0x15,0x15,0x15,0x15,0x15,0x15,0x15,
        0x15,0x15,0x15,0x15,0x15,0x15,0x15,0x15,
        0x3F,0x3F,0x3F,0x3F,0x3F,0x3F,0x3F,0x3F
};

static const unsigned char SiS_CGA_DAC[] =
{
        0x00,0x10,0x04,0x14,0x01,0x11,0x09,0x15,
        0x00,0x10,0x04,0x14,0x01,0x11,0x09,0x15,
        0x2A,0x3A,0x2E,0x3E,0x2B,0x3B,0x2F,0x3F,
        0x2A,0x3A,0x2E,0x3E,0x2B,0x3B,0x2F,0x3F,
        0x00,0x10,0x04,0x14,0x01,0x11,0x09,0x15,
        0x00,0x10,0x04,0x14,0x01,0x11,0x09,0x15,
        0x2A,0x3A,0x2E,0x3E,0x2B,0x3B,0x2F,0x3F,
        0x2A,0x3A,0x2E,0x3E,0x2B,0x3B,0x2F,0x3F
};

static const unsigned char SiS_EGA_DAC[] =
{
        0x00,0x10,0x04,0x14,0x01,0x11,0x05,0x15,
        0x20,0x30,0x24,0x34,0x21,0x31,0x25,0x35,
        0x08,0x18,0x0C,0x1C,0x09,0x19,0x0D,0x1D,
        0x28,0x38,0x2C,0x3C,0x29,0x39,0x2D,0x3D,
        0x02,0x12,0x06,0x16,0x03,0x13,0x07,0x17,
        0x22,0x32,0x26,0x36,0x23,0x33,0x27,0x37,
        0x0A,0x1A,0x0E,0x1E,0x0B,0x1B,0x0F,0x1F,
        0x2A,0x3A,0x2E,0x3E,0x2B,0x3B,0x2F,0x3F
};

static const unsigned char SiS_VGA_DAC[] =
{
	0x00,0x10,0x04,0x14,0x01,0x11,0x09,0x15,
	0x2A,0x3A,0x2E,0x3E,0x2B,0x3B,0x2F,0x3F,
	0x00,0x05,0x08,0x0B,0x0E,0x11,0x14,0x18,
	0x1C,0x20,0x24,0x28,0x2D,0x32,0x38,0x3F,
	0x00,0x10,0x1F,0x2F,0x3F,0x1F,0x27,0x2F,
	0x37,0x3F,0x2D,0x31,0x36,0x3A,0x3F,0x00,
	0x07,0x0E,0x15,0x1C,0x0E,0x11,0x15,0x18,
	0x1C,0x14,0x16,0x18,0x1A,0x1C,0x00,0x04,
	0x08,0x0C,0x10,0x08,0x0A,0x0C,0x0E,0x10,
	0x0B,0x0C,0x0D,0x0F,0x10
};

static const struct SiS_St SiS_SModeIDTable[] =
{
	{0x01,0x9208,0x01,0x00,0x00,0x00,0x01,0x00,0x40},
	{0x01,0x1210,0x14,0x01,0x01,0x00,0x01,0x00,0x40},
	{0x01,0x1010,0x17,0x02,0x02,0x00,0x01,0x01,0x40},
	{0x03,0x8208,0x03,0x00,0x00,0x00,0x01,0x02,0x40},
	{0x03,0x0210,0x16,0x01,0x01,0x00,0x01,0x02,0x40},
	{0x03,0x0010,0x18,0x02,0x02,0x00,0x01,0x03,0x40},
	{0x05,0x9209,0x05,0x00,0x00,0x00,0x00,0x04,0x40},
	{0x06,0x8209,0x06,0x00,0x00,0x00,0x00,0x05,0x40},
	{0x07,0x0000,0x07,0x03,0x03,0x00,0x01,0x03,0x40},
	{0x07,0x0000,0x19,0x02,0x02,0x00,0x01,0x03,0x40},
	{0x0d,0x920a,0x0d,0x00,0x00,0x00,0x00,0x04,0x40},
	{0x0e,0x820a,0x0e,0x00,0x00,0x00,0x00,0x05,0x40},
	{0x0f,0x0202,0x11,0x01,0x01,0x00,0x00,0x05,0x40},
	{0x10,0x0212,0x12,0x01,0x01,0x00,0x00,0x05,0x40},
	{0x11,0x0212,0x1a,0x04,0x04,0x00,0x00,0x05,0x40},
	{0x12,0x0212,0x1b,0x04,0x04,0x00,0x00,0x05,0x40},
	{0x13,0x021b,0x1c,0x00,0x00,0x00,0x00,0x04,0x40},
	{0x12,0x0010,0x18,0x02,0x02,0x00,0x00,0x05,0x40},
	{0x12,0x0210,0x18,0x01,0x01,0x00,0x00,0x05,0x40},
	{0xff,0x0000,0x00,0x00,0x00,0x00,0x00,0x00,0x00}
};

static const struct SiS_StResInfo_S SiS_StResInfo[]=
{
	{ 640,400},
	{ 640,350},
	{ 720,400},
	{ 720,350},
	{ 640,480}
};

static const struct SiS_ModeResInfo_S SiS_ModeResInfo[] =
{
	{  320, 200, 8, 8},   /* 0x00 */
	{  320, 240, 8, 8},   /* 0x01 */
	{  320, 400, 8, 8},   /* 0x02 */
	{  400, 300, 8, 8},   /* 0x03 */
	{  512, 384, 8, 8},   /* 0x04 */
	{  640, 400, 8,16},   /* 0x05 */
	{  640, 480, 8,16},   /* 0x06 */
	{  800, 600, 8,16},   /* 0x07 */
	{ 1024, 768, 8,16},   /* 0x08 */
	{ 1280,1024, 8,16},   /* 0x09 */
	{ 1600,1200, 8,16},   /* 0x0a */
	{ 1920,1440, 8,16},   /* 0x0b */
	{ 2048,1536, 8,16},   /* 0x0c */
	{  720, 480, 8,16},   /* 0x0d */
	{  720, 576, 8,16},   /* 0x0e */
	{ 1280, 960, 8,16},   /* 0x0f */
	{  800, 480, 8,16},   /* 0x10 */
	{ 1024, 576, 8,16},   /* 0x11 */
	{ 1280, 720, 8,16},   /* 0x12 */
	{  856, 480, 8,16},   /* 0x13 */
	{ 1280, 768, 8,16},   /* 0x14 */
	{ 1400,1050, 8,16},   /* 0x15 */
	{ 1152, 864, 8,16},   /* 0x16 */
	{  848, 480, 8,16},   /* 0x17 */
	{ 1360, 768, 8,16},   /* 0x18 */
	{ 1024, 600, 8,16},   /* 0x19 */
	{ 1152, 768, 8,16},   /* 0x1a */
	{  768, 576, 8,16},   /* 0x1b */
	{ 1360,1024, 8,16},   /* 0x1c */
	{ 1680,1050, 8,16},   /* 0x1d */
	{ 1280, 800, 8,16},   /* 0x1e */
	{ 1920,1080, 8,16},   /* 0x1f */
	{  960, 540, 8,16},   /* 0x20 */
	{  960, 600, 8,16},   /* 0x21 */
	{ 1280, 854, 8,16}    /* 0x22 */
};

#if defined(SIS300) || defined(SIS315H)
static const struct SiS_StandTable_S SiS_StandTable[]=
{
/* 0x00: MD_0_200 */
 {
  0x28,0x18,0x08,0x0800,
  {0x09,0x03,0x00,0x02},
  0x63,
  {0x2d,0x27,0x28,0x90,0x2b,0xa0,0xbf,0x1f,
   0x00,0xc7,0x06,0x07,0x00,0x00,0x00,0x00,
   0x9c,0x8e,0x8f,0x14,0x1f,0x96,0xb9,0xa3,
   0xff},
  {0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,
   0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,
   0x08,0x00,0x0f,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x10,0x0e,0x00,
   0xff}
 },
/* 0x01: MD_1_200 */
 {
  0x28,0x18,0x08,0x0800,
  {0x09,0x03,0x00,0x02},
  0x63,
  {0x2d,0x27,0x28,0x90,0x2b,0xa0,0xbf,0x1f,
   0x00,0xc7,0x06,0x07,0x00,0x00,0x00,0x00,
   0x9c,0x8e,0x8f,0x14,0x1f,0x96,0xb9,0xa3,
   0xff},
  {0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,
   0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,
   0x08,0x00,0x0f,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x10,0x0e,0x00,
   0xff}
 },
/* 0x02: MD_2_200 */
 {
  0x50,0x18,0x08,0x1000,
  {0x01,0x03,0x00,0x02},
  0x63,
  {0x5f,0x4f,0x50,0x82,0x55,0x81,0xbf,0x1f,
   0x00,0xc7,0x06,0x07,0x00,0x00,0x00,0x00,
   0x9c,0x8e,0x8f,0x28,0x1f,0x96,0xb9,0xa3,
   0xff},
  {0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,
   0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,
   0x08,0x00,0x0f,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x10,0x0e,0x00,
   0xff}
 },
/* 0x03: MD_3_200 - mode 0x03 - 0 */
 {
  0x50,0x18,0x08,0x1000,
  {0x01,0x03,0x00,0x02},
  0x63,
  {0x5f,0x4f,0x50,0x82,0x55,0x81,0xbf,0x1f,
   0x00,0xc7,0x06,0x07,0x00,0x00,0x00,0x00,
   0x9c,0x8e,0x8f,0x28,0x1f,0x96,0xb9,0xa3,
   0xff},
  {0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,
   0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,
   0x08,0x00,0x0f,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x10,0x0e,0x00,
   0xff}
 },
/* 0x04: MD_4 */
 {
  0x28,0x18,0x08,0x4000,
  {0x09,0x03,0x00,0x02},
  0x63,
  {0x2d,0x27,0x28,0x90,0x2c,0x80,0xbf,0x1f,   /* 0x2c is 2b for 300 */
   0x00,0xc1,0x00,0x00,0x00,0x00,0x00,0x00,
   0x9c,0x8e,0x8f,0x14,0x00,0x96,0xb9,0xa2,
   0xff},
  {0x00,0x13,0x15,0x17,0x02,0x04,0x06,0x07,
   0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,
   0x01,0x00,0x03,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x30,0x0f,0x00,
   0xff}
 },
/* 0x05: MD_5 */
 {
  0x28,0x18,0x08,0x4000,
  {0x09,0x03,0x00,0x02},
  0x63,
  {0x2d,0x27,0x28,0x90,0x2c,0x80,0xbf,0x1f,   /* 0x2c is 2b for 300 */
   0x00,0xc1,0x00,0x00,0x00,0x00,0x00,0x00,
   0x9c,0x8e,0x8f,0x14,0x00,0x96,0xb9,0xa2,
   0xff},
  {0x00,0x13,0x15,0x17,0x02,0x04,0x06,0x07,
   0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,
   0x01,0x00,0x03,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x30,0x0f,0x00,
   0xff}
 },
/* 0x06: MD_6 */
 {
  0x50,0x18,0x08,0x4000,
  {0x01,0x01,0x00,0x06},
  0x63,
  {0x5f,0x4f,0x50,0x82,0x55,0x81,0xbf,0x1f,   /* 55,81 is 54,80 for 300 */
   0x00,0xc1,0x00,0x00,0x00,0x00,0x00,0x00,
   0x9c,0x8e,0x8f,0x28,0x00,0x96,0xb9,0xc2,
   0xff},
  {0x00,0x17,0x17,0x17,0x17,0x17,0x17,0x17,
   0x17,0x17,0x17,0x17,0x17,0x17,0x17,0x17,
   0x01,0x00,0x01,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x00,0x0d,0x00,
   0xff}
 },
/* 0x07: MD_7 */
 {
  0x50,0x18,0x0e,0x1000,
  {0x00,0x03,0x00,0x03},
  0xa6,
  {0x5f,0x4f,0x50,0x82,0x55,0x81,0xbf,0x1f,
   0x00,0x4d,0x0b,0x0c,0x00,0x00,0x00,0x00,
   0x83,0x85,0x5d,0x28,0x0d,0x63,0xba,0xa3,
   0xff},
  {0x00,0x08,0x08,0x08,0x08,0x08,0x08,0x08,
   0x10,0x18,0x18,0x18,0x18,0x18,0x18,0x18,
   0x0e,0x00,0x0f,0x08},
  {0x00,0x00,0x00,0x00,0x00,0x10,0x0a,0x00,
   0xff}
 },
/* 0x08: MDA_DAC */
 {
  0x00,0x00,0x00,0x0000,
  {0x00,0x00,0x00,0x15},
  0x15,
  {0x15,0x15,0x15,0x15,0x15,0x15,0x15,0x15,
   0x15,0x15,0x15,0x15,0x15,0x15,0x3f,0x3f,
   0x3f,0x3f,0x3f,0x3f,0x3f,0x3f,0x00,0x00,
   0x00},
  {0x00,0x00,0x00,0x00,0x00,0x15,0x15,0x15,
   0x15,0x15,0x15,0x15,0x15,0x15,0x15,0x15,
   0x15,0x15,0x15,0x15},
  {0x15,0x3f,0x3f,0x3f,0x3f,0x3f,0x3f,0x3f,
   0x3f}
 },
/* 0x09: CGA_DAC */
 {
  0x00,0x10,0x04,0x0114,
  {0x11,0x09,0x15,0x00},
  0x10,
  {0x04,0x14,0x01,0x11,0x09,0x15,0x2a,0x3a,
   0x2e,0x3e,0x2b,0x3b,0x2f,0x3f,0x2a,0x3a,
   0x2e,0x3e,0x2b,0x3b,0x2f,0x3f,0x00,0x10,
   0x04},
  {0x14,0x01,0x11,0x09,0x15,0x00,0x10,0x04,
   0x14,0x01,0x11,0x09,0x15,0x2a,0x3a,0x2e,
   0x3e,0x2b,0x3b,0x2f},
  {0x3f,0x2a,0x3a,0x2e,0x3e,0x2b,0x3b,0x2f,
   0x3f}
 },
/* 0x0a: EGA_DAC */
 {
  0x00,0x10,0x04,0x0114,
  {0x11,0x05,0x15,0x20},
  0x30,
  {0x24,0x34,0x21,0x31,0x25,0x35,0x08,0x18,
   0x0c,0x1c,0x09,0x19,0x0d,0x1d,0x28,0x38,
   0x2c,0x3c,0x29,0x39,0x2d,0x3d,0x02,0x12,
   0x06},
  {0x16,0x03,0x13,0x07,0x17,0x22,0x32,0x26,
   0x36,0x23,0x33,0x27,0x37,0x0a,0x1a,0x0e,
   0x1e,0x0b,0x1b,0x0f},
  {0x1f,0x2a,0x3a,0x2e,0x3e,0x2b,0x3b,0x2f,
   0x3f}
 },
/* 0x0b: VGA_DAC */
 {
  0x00,0x10,0x04,0x0114,
  {0x11,0x09,0x15,0x2a},
  0x3a,
  {0x2e,0x3e,0x2b,0x3b,0x2f,0x3f,0x00,0x05,
   0x08,0x0b,0x0e,0x11,0x14,0x18,0x1c,0x20,
   0x24,0x28,0x2d,0x32,0x38,0x3f,0x00,0x10,
   0x1f},
  {0x2f,0x3f,0x1f,0x27,0x2f,0x37,0x3f,0x2d,
   0x31,0x36,0x3a,0x3f,0x00,0x07,0x0e,0x15,
   0x1c,0x0e,0x11,0x15},
  {0x18,0x1c,0x14,0x16,0x18,0x1a,0x1c,0x00,
   0x04}
 },
/* 0x0c */
 {
  0x08,0x0c,0x10,0x0a08,
  {0x0c,0x0e,0x10,0x0b},
  0x0c,
  {0x0d,0x0f,0x10,0x10,0x01,0x08,0x00,0x00,
   0x00,0x00,0x01,0x00,0x02,0x02,0x01,0x00,
   0x04,0x04,0x01,0x00,0x05,0x02,0x05,0x00,
   0x06},
  {0x01,0x06,0x05,0x06,0x00,0x08,0x01,0x08,
   0x00,0x07,0x02,0x07,0x06,0x07,0x00,0x00,
   0x00,0x00,0x00,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00}
 },
/* 0x0d: MD_D */
 {
  0x28,0x18,0x08,0x2000,
  {0x09,0x0f,0x00,0x06},
  0x63,
  {0x2d,0x27,0x28,0x90,0x2c,0x80,0xbf,0x1f,     /* 2c is 2b for 300 */
   0x00,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,
   0x9c,0x8e,0x8f,0x14,0x00,0x96,0xb9,0xe3,
   0xff},
  {0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,
   0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,
   0x01,0x00,0x0f,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x00,0x05,0x0f,
   0xff}
 },
/* 0x0e: MD_E */
 {
  0x50,0x18,0x08,0x4000,
  {0x01,0x0f,0x00,0x06},
  0x63,
  {0x5f,0x4f,0x50,0x82,0x55,0x81,0xbf,0x1f,     /* 55,81 is 54,80 for 300 */
   0x00,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,
   0x9c,0x8e,0x8f,0x28,0x00,0x96,0xb9,0xe3,
   0xff},
  {0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,
   0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,
   0x01,0x00,0x0f,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x00,0x05,0x0f,
   0xff}
 },
/* 0x0f: ExtVGATable - modes > 0x13 */
 {
  0x00,0x00,0x00,0x0000,
  {0x01,0x0f,0x00,0x0e},
  0x23,
  {0x5f,0x4f,0x50,0x82,0x54,0x80,0x0b,0x3e,
   0x00,0x40,0x00,0x00,0x00,0x00,0x00,0x00,
   0xea,0x8c,0xdf,0x28,0x40,0xe7,0x04,0xa3,
   0xff},
  {0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,
   0x08,0x09,0x0a,0x0b,0x0c,0x0d,0x0e,0x0f,
   0x01,0x00,0x00,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x40,0x05,0x0f,
   0xff}
 },
/* 0x10: ROM_SAVEPTR - totally different for 300 */
 {
  0x9f,0x3b,0x00,0x00c0,
  {0x00,0x00,0x00,0x00},
  0x00,
  {0x00,0x00,0x00,0x00,0x00,0x00,0xbb,0x3f,
   0x00,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x1a,0x00,0xac,0x3e,0x00,0xc0,
   0x00},
  {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00}
 },
/* 0x11: MD_F */
 {
  0x50,0x18,0x0e,0x8000,
  {0x01,0x0f,0x00,0x06},
  0xa2,
  {0x5f,0x4f,0x50,0x82,0x55,0x81,0xbf,0x1f,    /* 55,81 is 54,80 on 300 */
   0x00,0x40,0x00,0x00,0x00,0x00,0x00,0x00,
   0x82,0x84,0x5d,0x28,0x0f,0x63,0xba,0xe3,    /* 82,84 is 83,85 on 300 */
   0xff},
  {0x00,0x08,0x00,0x00,0x18,0x18,0x00,0x00,
   0x00,0x08,0x00,0x00,0x00,0x18,0x00,0x00,
   0x0b,0x00,0x05,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x00,0x05,0x05,
   0xff}
 },
/* 0x12: MD_10 */
 {
  0x50,0x18,0x0e,0x8000,
  {0x01,0x0f,0x00,0x06},
  0xa3,
  {0x5f,0x4f,0x50,0x82,0x55,0x81,0xbf,0x1f,    /* 55,81 is 54,80 on 300 */
   0x00,0x40,0x00,0x00,0x00,0x00,0x00,0x00,
   0x82,0x84,0x5d,0x28,0x0f,0x63,0xba,0xe3,    /* 82,84 is 83,85 on 300 */
   0xff},
  {0x00,0x01,0x02,0x03,0x04,0x05,0x14,0x07,
   0x38,0x39,0x3a,0x3b,0x3c,0x3d,0x3e,0x3f,
   0x01,0x00,0x0f,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x00,0x05,0x0f,
   0xff}
 },
/* 0x13: MD_0_350 */
 {
  0x28,0x18,0x0e,0x0800,
  {0x09,0x03,0x00,0x02},
  0xa3,
  {0x2d,0x27,0x28,0x90,0x2b,0xb1,0xbf,0x1f,    /* b1 is a0 on 300 */
   0x00,0x4d,0x0b,0x0c,0x00,0x00,0x00,0x00,
   0x83,0x85,0x5d,0x14,0x1f,0x63,0xba,0xa3,
   0xff},
  {0x00,0x01,0x02,0x03,0x04,0x05,0x14,0x07,
   0x38,0x39,0x3a,0x3b,0x3c,0x3d,0x3e,0x3f,
   0x08,0x00,0x0f,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x10,0x0e,0x00,
   0xff}
 },
/* 0x14: MD_1_350 */
 {
  0x28,0x18,0x0e,0x0800,
  {0x09,0x03,0x00,0x02},
  0xa3,
  {0x2d,0x27,0x28,0x90,0x2b,0xa0,0xbf,0x1f,
   0x00,0x4d,0x0b,0x0c,0x00,0x00,0x00,0x00,
   0x83,0x85,0x5d,0x14,0x1f,0x63,0xba,0xa3,
   0xff},
  {0x00,0x01,0x02,0x03,0x04,0x05,0x14,0x07,
   0x38,0x39,0x3a,0x3b,0x3c,0x3d,0x3e,0x3f,
   0x08,0x00,0x0f,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x10,0x0e,0x00,
   0xff}
 },
/* 0x15: MD_2_350 */
 {
  0x50,0x18,0x0e,0x1000,
  {0x01,0x03,0x00,0x02},
  0xa3,
  {0x5f,0x4f,0x50,0x82,0x55,0x81,0xbf,0x1f,
   0x00,0x4d,0x0b,0x0c,0x00,0x00,0x00,0x00,
   0x83,0x85,0x5d,0x28,0x1f,0x63,0xba,0xa3,
   0xff},
  {0x00,0x01,0x02,0x03,0x04,0x05,0x14,0x07,
   0x38,0x39,0x3a,0x3b,0x3c,0x3d,0x3e,0x3f,
   0x08,0x00,0x0f,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x10,0x0e,0x00,
   0xff}
 },
/* 0x16: MD_3_350 - mode 0x03 - 1 */
 {
  0x50,0x18,0x0e,0x1000,
  {0x01,0x03,0x00,0x02},
  0xa3,
  {0x5f,0x4f,0x50,0x82,0x55,0x81,0xbf,0x1f,
   0x00,0x4d,0x0b,0x0c,0x00,0x00,0x00,0x00,
   0x83,0x85,0x5d,0x28,0x1f,0x63,0xba,0xa3,
   0xff},
  {0x00,0x01,0x02,0x03,0x04,0x05,0x14,0x07,
   0x38,0x39,0x3a,0x3b,0x3c,0x3d,0x3e,0x3f,
   0x08,0x00,0x0f,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x10,0x0e,0x00,
   0xff}
 },
/* 0x17: MD_0_1_400 */
 {
  0x28,0x18,0x10,0x0800,
  {0x08,0x03,0x00,0x02},
  0x67,
  {0x2d,0x27,0x28,0x90,0x2b,0xb1,0xbf,0x1f,    /* b1 is a0 on 300 */
   0x00,0x4f,0x0d,0x0e,0x00,0x00,0x00,0x00,
   0x9c,0x8e,0x8f,0x14,0x1f,0x96,0xb9,0xa3,
   0xff},
  {0x00,0x01,0x02,0x03,0x04,0x05,0x14,0x07,
   0x38,0x39,0x3a,0x3b,0x3c,0x3d,0x3e,0x3f,
   0x0c,0x00,0x0f,0x08},
  {0x00,0x00,0x00,0x00,0x00,0x10,0x0e,0x00,
   0xff}
 },
/* 0x18: MD_2_3_400 - mode 0x03 - 2 */
 {
  0x50,0x18,0x10,0x1000,
  {0x00,0x03,0x00,0x02},
  0x67,
  {0x5f,0x4f,0x50,0x82,0x55,0x81,0xbf,0x1f,
   0x00,0x4f,0x0d,0x0e,0x00,0x00,0x00,0x00,
   0x9c,0x8e,0x8f,0x28,0x1f,0x96,0xb9,0xa3,
   0xff},
  {0x00,0x01,0x02,0x03,0x04,0x05,0x14,0x07,
   0x38,0x39,0x3a,0x3b,0x3c,0x3d,0x3e,0x3f,
   0x0c,0x00,0x0f,0x08},
  {0x00,0x00,0x00,0x00,0x00,0x10,0x0e,0x00,
   0xff}
 },
/* 0x19: MD_7_400 */
 {
  0x50,0x18,0x10,0x1000,
  {0x00,0x03,0x00,0x02},
  0x66,
  {0x5f,0x4f,0x50,0x82,0x55,0x81,0xbf,0x1f,
   0x00,0x4f,0x0d,0x0e,0x00,0x00,0x00,0x00,
   0x9c,0x8e,0x8f,0x28,0x0f,0x96,0xb9,0xa3,
   0xff},
  {0x00,0x08,0x08,0x08,0x08,0x08,0x08,0x08,
   0x10,0x18,0x18,0x18,0x18,0x18,0x18,0x18,
   0x0e,0x00,0x0f,0x08},
  {0x00,0x00,0x00,0x00,0x00,0x10,0x0a,0x00,
   0xff}
 },
/* 0x1a: MD_11 */
 {
  0x50,0x1d,0x10,0xa000,
  {0x01,0x0f,0x00,0x06},
  0xe3,
  {0x5f,0x4f,0x50,0x82,0x55,0x81,0x0b,0x3e,    /* 55,81 is 54,80 on 300 */
   0x00,0x40,0x00,0x00,0x00,0x00,0x00,0x00,
   0xe9,0x8b,0xdf,0x28,0x00,0xe7,0x04,0xc3,    /* e9,8b is ea,8c on 300 */
   0xff},
  {0x00,0x3f,0x3f,0x3f,0x3f,0x3f,0x3f,0x3f,
   0x3f,0x3f,0x3f,0x3f,0x3f,0x3f,0x3f,0x3f,
   0x01,0x00,0x0f,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x00,0x05,0x01,
   0xff}
 },
/* 0x1b: ExtEGATable - Modes <= 0x02 */
 {
  0x50,0x1d,0x10,0xa000,
  {0x01,0x0f,0x00,0x06},
  0xe3,
  {0x5f,0x4f,0x50,0x82,0x55,0x81,0x0b,0x3e,    /* 55,81 is 54,80 on 300 */
   0x00,0x40,0x00,0x00,0x00,0x00,0x00,0x00,
   0xe9,0x8b,0xdf,0x28,0x00,0xe7,0x04,0xe3,    /* e9,8b is ea,8c on 300 */
   0xff},
  {0x00,0x01,0x02,0x03,0x04,0x05,0x14,0x07,
   0x38,0x39,0x3a,0x3b,0x3c,0x3d,0x3e,0x3f,
   0x01,0x00,0x0f,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x00,0x05,0x0f,
   0xff}
 },
/* 0x1c: MD_13 */
 {
  0x28,0x18,0x08,0x2000,
  {0x01,0x0f,0x00,0x0e},
  0x63,
  {0x5f,0x4f,0x50,0x82,0x55,0x81,0xbf,0x1f,    /* 55,81 is 54,80 on 300 */
   0x00,0x41,0x00,0x00,0x00,0x00,0x00,0x00,
   0x9c,0x8e,0x8f,0x28,0x40,0x96,0xb9,0xa3,
   0xff},
  {0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,
   0x08,0x09,0x0a,0x0b,0x0c,0x0d,0x0e,0x0f,
   0x41,0x00,0x0f,0x00},
  {0x00,0x00,0x00,0x00,0x00,0x40,0x05,0x0f,
   0xff}
 }
};
#endif

/**************************************************************/
/* SIS VIDEO BRIDGE ----------------------------------------- */
/**************************************************************/

static const unsigned char SiS_SoftSetting  = 0x30;   /* RAM setting */

static const unsigned char SiS_OutputSelect = 0x40;

static const unsigned char SiS_NTSCTiming[] = {
	0x17,0x1d,0x03,0x09,0x05,0x06,0x0c,0x0c,
	0x94,0x49,0x01,0x0a,0x06,0x0d,0x04,0x0a,
	0x06,0x14,0x0d,0x04,0x0a,0x00,0x85,0x1b,
	0x0c,0x50,0x00,0x97,0x00,0xda,0x4a,0x17,
	0x7d,0x05,0x4b,0x00,0x00,0xe2,0x00,0x02,
	0x03,0x0a,0x65,0x9d,0x08,0x92,0x8f,0x40,
	0x60,0x80,0x14,0x90,0x8c,0x60,0x14,0x50,
	0x00,0x40,0x44,0x00,0xdb,0x02,0x3b,0x00
};

static const unsigned char SiS_PALTiming[] = {
	0x19,0x52,0x35,0x6e,0x04,0x38,0x3d,0x70,
	0x94,0x49,0x01,0x12,0x06,0x3e,0x35,0x6d,
	0x06,0x14,0x3e,0x35,0x6d,0x00,0x45,0x2b,
	0x70,0x50,0x00,0x9b,0x00,0xd9,0x5d,0x17,
	0x7d,0x05,0x45,0x00,0x00,0xe8,0x00,0x02,
	0x0d,0x00,0x68,0xb0,0x0b,0x92,0x8f,0x40,
	0x60,0x80,0x14,0x90,0x8c,0x60,0x14,0x63,
	0x00,0x40,0x3e,0x00,0xe1,0x02,0x28,0x00
};

static const unsigned char SiS_HiTVExtTiming[] = {
	0x32,0x65,0x2c,0x5f,0x08,0x31,0x3a,0x64,
	0x28,0x02,0x01,0x3d,0x06,0x3e,0x35,0x6d,
	0x06,0x14,0x3e,0x35,0x6d,0x00,0xc5,0x3f,
	0x64,0x90,0x33,0x8c,0x18,0x36,0x3e,0x13,
	0x2a,0xde,0x2a,0x44,0x40,0x2a,0x44,0x40,
	0x8e,0x8e,0x82,0x07,0x0b,0x92,0x0f,0x40,
	0x60,0x80,0x14,0x90,0x8c,0x60,0x14,0x3d,
	0x63,0x4f,0x27,0x00,0xfc,0xff,0x6a,0x00
};

static const unsigned char SiS_HiTVSt1Timing[] = {
	0x32,0x65,0x2c,0x5f,0x08,0x31,0x3a,0x65,
	0x28,0x02,0x01,0x3d,0x06,0x3e,0x35,0x6d,
	0x06,0x14,0x3e,0x35,0x6d,0x00,0xc5,0x3f,
	0x65,0x90,0x7b,0xa8,0x03,0xf0,0x87,0x03,
	0x11,0x15,0x11,0xcf,0x10,0x11,0xcf,0x10,
	0x35,0x35,0x3b,0x69,0x1d,0x92,0x0f,0x40,
	0x60,0x80,0x14,0x90,0x8c,0x60,0x04,0x86,
	0xaf,0x5d,0x0e,0x00,0xfc,0xff,0x2d,0x00
};

static const unsigned char SiS_HiTVSt2Timing[] = {
	0x32,0x65,0x2c,0x5f,0x08,0x31,0x3a,0x64,
	0x28,0x02,0x01,0x3d,0x06,0x3e,0x35,0x6d,
	0x06,0x14,0x3e,0x35,0x6d,0x00,0xc5,0x3f,
	0x64,0x90,0x33,0x8c,0x18,0x36,0x3e,0x13,
	0x2a,0xde,0x2a,0x44,0x40,0x2a,0x44,0x40,
	0x8e,0x8e,0x82,0x07,0x0b,0x92,0x0f,0x40,
	0x60,0x80,0x14,0x90,0x8c,0x60,0x14,0x3d,
	0x63,0x4f,0x27,0x00,0xfc,0xff,0x6a,0x00
};

#if 0
static const unsigned char SiS_HiTVTextTiming[] = {
	0x32,0x65,0x2c,0x5f,0x08,0x31,0x3a,0x65,
	0x28,0x02,0x01,0x3d,0x06,0x3e,0x35,0x6d,
	0x06,0x14,0x3e,0x35,0x6d,0x00,0xc5,0x3f,
	0x65,0x90,0xe7,0xbc,0x03,0x0c,0x97,0x03,
	0x14,0x78,0x14,0x08,0x20,0x14,0x08,0x20,
	0xc8,0xc8,0x3b,0xd2,0x26,0x92,0x0f,0x40,
        0x60,0x80,0x14,0x90,0x8c,0x60,0x04,0x96,
	0x72,0x5c,0x11,0x00,0xfc,0xff,0x32,0x00
};
#endif

static const unsigned char SiS_HiTVGroup3Data[] = {
	0x00,0x1a,0x22,0x63,0x62,0x22,0x08,0x5f,
	0x05,0x21,0xb2,0xb2,0x55,0x77,0x2a,0xa6,
	0x25,0x2f,0x47,0xfa,0xc8,0xff,0x8e,0x20,
	0x8c,0x6e,0x60,0x2e,0x58,0x48,0x72,0x44,
	0x56,0x36,0x4f,0x6e,0x3f,0x80,0x00,0x80,
	0x4f,0x7f,0x03,0xa8,0x7d,0x20,0x1a,0xa9,
	0x14,0x05,0x03,0x7e,0x64,0x31,0x14,0x75,
	0x18,0x05,0x18,0x05,0x4c,0xa8,0x01
};

static const unsigned char SiS_HiTVGroup3Simu[] = {
	0x00,0x1a,0x22,0x63,0x62,0x22,0x08,0x95,
	0xdb,0x20,0xb8,0xb8,0x55,0x47,0x2a,0xa6,
	0x25,0x2f,0x47,0xfa,0xc8,0xff,0x8e,0x20,
	0x8c,0x6e,0x60,0x15,0x26,0xd3,0xe4,0x11,
	0x56,0x36,0x4f,0x6e,0x3f,0x80,0x00,0x80,
	0x67,0x36,0x01,0x47,0x0e,0x10,0xbe,0xb4,
	0x01,0x05,0x03,0x7e,0x65,0x31,0x14,0x75,
	0x18,0x05,0x18,0x05,0x4c,0xa8,0x01
};

#if 0
static const unsigned char SiS_HiTVGroup3Text[] = {
	0x00,0x1a,0x22,0x63,0x62,0x22,0x08,0xa7,
	0xf5,0x20,0xce,0xce,0x55,0x47,0x2a,0xa6,
	0x25,0x2f,0x47,0xfa,0xc8,0xff,0x8e,0x20,
	0x8c,0x6e,0x60,0x18,0x2c,0x0c,0x20,0x22,
	0x56,0x36,0x4f,0x6e,0x3f,0x80,0x00,0x80,
	0x93,0x3c,0x01,0x50,0x2f,0x10,0xf4,0xca,
	0x01,0x05,0x03,0x7e,0x65,0x31,0x14,0x75,
	0x18,0x05,0x18,0x05,0x4c,0xa8,0x01
};
#endif

static const struct SiS_TVData SiS_StPALData[] =
{
 {    1,   1, 864, 525,1270, 400, 100, 0, 760,    0,0xf4,0xff,0x1c,0x22},
 {    1,   1, 864, 525,1270, 350, 100, 0, 760,    0,0xf4,0xff,0x1c,0x22},
 {    1,   1, 864, 525,1270, 400,   0, 0, 720,    0,0xf1,0x04,0x1f,0x18},
 {    1,   1, 864, 525,1270, 350,   0, 0, 720,    0,0xf4,0x0b,0x1c,0x0a},
 {    1,   1, 864, 525,1270, 480,  50, 0, 760,    0,0xf4,0xff,0x1c,0x22},
 {    1,   1, 864, 525,1270, 600,  50, 0,   0,0x300,0xf4,0xff,0x1c,0x22}
};

static const struct SiS_TVData SiS_ExtPALData[] =
{
 {   27,  10, 848, 448,1270, 530,  50, 0,  50,    0,0xf4,0xff,0x1c,0x22},  /* 640x400, 320x200 */
 {  108,  35, 848, 398,1270, 530,  50, 0,  50,    0,0xf4,0xff,0x1c,0x22},
 {   12,   5, 954, 448,1270, 530,  50, 0,  50,    0,0xf1,0x04,0x1f,0x18},
 {    9,   4, 960, 463,1644, 438,  50, 0,  50,    0,0xf4,0x0b,0x1c,0x0a},
 {    9,   4, 848, 528,1270, 530,   0, 0,  50,    0,0xf5,0xfb,0x1b,0x2a},  /* 640x480, 320x240 */
 {   36,  25,1060, 648,1270, 530, 438, 0, 438,    0,0xeb,0x05,0x25,0x16},  /* 800x600, 400x300 */
 {    3,   2,1080, 619,1270, 540, 438, 0, 438,    0,0xf3,0x00,0x1d,0x20},  /* 720x576 */
 {    1,   1,1170, 821,1270, 520, 686, 0, 686,    0,0xF3,0x00,0x1D,0x20},  /* 1024x768 */
 {    1,   1,1170, 821,1270, 520, 686, 0, 686,    0,0xF3,0x00,0x1D,0x20},  /* 1024x768 (for NTSC equ) */
 {    9,   4, 848, 528,1270, 530,   0, 0,  50,    0,0xf5,0xfb,0x1b,0x2a}   /* 720x480 */
};

static const struct SiS_TVData SiS_StNTSCData[] =
{
 {    1,   1, 858, 525,1270, 400,  50, 0, 760,    0,0xf1,0x04,0x1f,0x18},
 {    1,   1, 858, 525,1270, 350,  50, 0, 640,    0,0xf1,0x04,0x1f,0x18},
 {    1,   1, 858, 525,1270, 400,   0, 0, 720,    0,0xf1,0x04,0x1f,0x18},
 {    1,   1, 858, 525,1270, 350,   0, 0, 720,    0,0xf4,0x0b,0x1c,0x0a},
 {    1,   1, 858, 525,1270, 480,   0, 0, 760,    0,0xf1,0x04,0x1f,0x18}
};

static const struct SiS_TVData SiS_ExtNTSCData[] =
{
 {  143,  65, 858, 443,1270, 440, 171, 0, 171,    0,0xf1,0x04,0x1f,0x18},    /* 640x400, 320x200 */
 {   88,  35, 858, 393,1270, 440, 171, 0, 171,    0,0xf1,0x04,0x1f,0x18},
 {  143,  70, 924, 443,1270, 440,  92, 0,  92,    0,0xf1,0x04,0x1f,0x18},
 {  143,  70, 924, 393,1270, 440,  92, 0,  92,    0,0xf4,0x0b,0x1c,0x0a},
 {  143,  76, 836, 523,1270, 440, 224, 0,   0,    0,0xf1,0x05,0x1f,0x16},    /* 640x480, 320x240 */
 {  143, 120,1056, 643,1270, 440,   0, 1,   0,    0,0xf4,0x10,0x1c,0x00},    /* 800x600, 400x300  */
 {  143,  76, 836, 523,1270, 440,   0, 1,   0,    0,0xee,0x0c,0x22,0x08},    /* 720x480 - BETTER (from 300 series) */
 {    1,   1,1100, 811,1412, 440,   0, 1,   0,    0,0xee,0x0c,0x22,0x08},    /* 1024x768 (525i) CORRECTED */
#if 0  /* flimmert und ist unten abgeschnitten (NTSCHT, NTSC clock) */
 {   65,  64,1056, 791,1270, 480, 455, 0,   0,    0,0x00,0x00,0x00,0x00}     /* 1024x768 (525p) */
#endif
#if 0
 {    1,   1,1100, 811,1412, 440,   0, 1,   0,    0,0x00,0x00,0x00,0x00}     /* 1024x768 (525p) */
#endif
#if 0
 {    1,   1,1120, 821,1516, 420,   0, 1,   0,    0,0x00,0x00,0x00,0x00}     /* 1024x768 (525p) */
#endif
#if 0
 {    1,   1, 938, 821,1516, 420,   0, 1,   0,    0,0x00,0x00,0x00,0x00}     /* 1024x768 (525p) */
#endif
#if 0 /* zoom hin, unten abgeschnitten (NTSC2HT, NTSC1024 clock) */
 {    1,   1,1072, 791,1270, 480, 455, 0,   0,    0,0x00,0x00,0x00,0x00}     /* 1024x768 (525p) */
#endif
#if 1 /* zu weit links (squeezed) (NTSC2HT, NTSC1024 clock) */
 {    1,   1,1100, 846,1270, 440, 455, 0,   0,    0,0x00,0x00,0x00,0x00}     /* 1024x768 (525p) */
#endif
#if 0 /* zu weit links, rechts abgeschnitten (NTSC2HT, NTSC1024 clock) */
 {    1,   1,1100, 846,1412, 440, 455, 0,   0,    0,0x00,0x00,0x00,0x00}     /* 1024x768 (525p) */
#endif
};

static const struct SiS_TVData SiS_StHiTVData[] =  /* Slave + TVSimu */
{
 {    1,   1, 0x37c,0x233,0x2b2,0x320,    0, 0, 0, 0, 0, 0, 0, 0},
 {    1,   1, 0x37c,0x233,0x2b2,0x2bc,    0, 0, 0, 0, 0, 0, 0, 0},
 {    1,   1, 0x37c,0x233,0x2b2,0x320,    0, 0, 0, 0, 0, 0, 0, 0},
 {    1,   1, 0x37c,0x233,0x2b2,0x2bc,    0, 0, 0, 0, 0, 0, 0, 0},
 {    1,   1, 0x37c,0x233,0x2b2,0x3c0,    0, 0, 0, 0, 0, 0, 0, 0},
 {    8,   5, 0x41a,0x2ab,0x670,0x3c0,0x150, 1, 0, 0, 0, 0, 0, 0}
};

static const struct SiS_TVData SiS_St2HiTVData[] = /* Slave */
{
 {    3,   1, 0x348,0x1e3,0x670,0x3c0,0x032, 0, 0, 0, 0, 0, 0, 0},
 {    1,   1, 0x37c,0x233,0x2b2,0x2bc, 	  0, 0, 0, 0, 0, 0, 0, 0},
 {    3,   1, 0x348,0x1e3,0x670,0x3c0,0x032, 0, 0, 0, 0, 0, 0, 0},
 {    1,   1, 0x37c,0x233,0x2b2,0x2bc,    0, 0, 0, 0, 0, 0, 0, 0},
 {    5,   2, 0x348,0x233,0x670,0x3c0,0x08d, 1, 0, 0, 0, 0, 0, 0},
 {    8,   5, 0x41a,0x2ab,0x670,0x3c0,0x17c, 1, 0, 0, 0, 0, 0, 0}
};

static const struct SiS_TVData SiS_ExtHiTVData[] =
{ /* all ok */
 {    6,   1, 0x348,0x233,0x660,0x3c0,    0, 0, 0, 0, 0, 0, 0, 0},
 {    3,   1, 0x3c0,0x233,0x660,0x3c0,    0, 0, 0, 0, 0, 0, 0, 0},
 {    6,   1, 0x348,0x233,0x660,0x3c0,    0, 0, 0, 0, 0, 0, 0, 0},
 {    3,   1, 0x3c0,0x233,0x660,0x3c0,    0, 0, 0, 0, 0, 0, 0, 0},
 {    5,   1, 0x348,0x233,0x670,0x3c0,0x166, 1, 0, 0, 0, 0, 0, 0},  /* 640x480   */
 {   16,   5, 0x41a,0x2ab,0x670,0x3c0,0x143, 1, 0, 0, 0, 0, 0, 0},  /* 800x600   */
 {   25,  12, 0x4ec,0x353,0x670,0x3c0,0x032, 0, 0, 0, 0, 0, 0, 0},  /* 1024x768  */
 {    5,   4, 0x627,0x464,0x670,0x3c0,0x128, 0, 0, 0, 0, 0, 0, 0},  /* 1280x1024 */
 {    4,   1, 0x41a,0x233,0x60c,0x3c0,0x143, 1, 0, 0, 0, 0, 0, 0},  /* 800x480   */
 {    5,   2, 0x578,0x293,0x670,0x3c0,0x032, 0, 0, 0, 0, 0, 0, 0},  /* 1024x576  */
 {    8,   5, 0x6d6,0x323,0x670,0x3c0,0x128, 0, 0, 0, 0, 0, 0, 0},  /* 1280x720  */
 {    8,   3, 0x4ec,0x353,0x670,0x3c0,0x032, 0, 0, 0, 0, 0, 0, 0},  /* 960x600  */
};

static const struct SiS_TVData SiS_St525pData[] =
{
 {    1,   1, 0x6b4,0x20d,0x4f6,0x190,   50, 0, 0x2f8, 0, 0, 0, 0, 0},
 {    1,   1, 0x6b4,0x20d,0x4f6,0x15e,   50, 0, 0x280, 0, 0, 0, 0, 0},
 {    1,   1, 0x6b4,0x20d,0x4f6,0x190,   50, 0, 0x2f8, 0, 0, 0, 0, 0},
 {    1,   1, 0x6b4,0x20d,0x4f6,0x15e,   50, 0, 0x280, 0, 0, 0, 0, 0},
 {    1,   1, 0x6b4,0x20d,0x4f6,0x1e0,    0, 0, 0x2f8, 0, 0, 0, 0, 0}
};

static const struct SiS_TVData SiS_St750pData[] =
{
 {    1,   1, 0x672,0x2ee,0x500,0x190,   50, 0, 0x2f8, 0, 0, 0, 0, 0},
 {    1,   1, 0x672,0x2ee,0x500,0x15e,   50, 0, 0x280, 0, 0, 0, 0, 0},
 {    1,   1, 0x672,0x2ee,0x500,0x190,    0, 0, 0x2d0, 0, 0, 0, 0, 0},
 {    1,   1, 0x672,0x2ee,0x500,0x15e,    0, 0, 0x2d0, 0, 0, 0, 0, 0},
 {    1,   1, 0x672,0x2ee,0x500,0x1e0,    0, 0, 0x2f8, 0, 0, 0, 0, 0}
};

static const struct SiS_TVData SiS_Ext750pData[] =
{ /* all ok */
 {    3,  1,  935, 470, 1130, 680,  50, 0, 0, 0, 0, 0, 0, 0},  /* 320x200/640x400 */
 {   24,  7,  935, 420, 1130, 680,  50, 0, 0, 0, 0, 0, 0, 0},
 {    3,  1,  935, 470, 1130, 680,  50, 0, 0, 0, 0, 0, 0, 0},
 {   24,  7,  935, 420, 1130, 680,  50, 0, 0, 0, 0, 0, 0, 0},
 {    2,  1, 1100, 590, 1130, 640,  50, 0, 0, 0, 0, 0, 0, 0},  /* 640x480 */
 {    3,  2, 1210, 690, 1130, 660,  50, 0, 0, 0, 0, 0, 0, 0},  /* 800x600 OK */
 {    2,  1, 1100, 562, 1130, 640,   0, 1, 0, 0, 0, 0, 0, 0},  /* 720x480 OK */
 {    1,  1, 1375, 878, 1130, 640, 638, 0, 0, 0, 0, 0, 0, 0},  /* 1024x768 OK */
 {    5,  3, 1100, 675, 1130, 640,   0, 1, 0, 0, 0, 0, 0, 0},  /* 720/768x576 OK */
 {   25, 24, 1496, 755, 1120, 680,  50, 0, 0, 0, 0, 0, 0, 0}   /* 1280x720 OK */
};

static const struct SiS_LCDData SiS_LCD1280x720Data[] =  /* 2.03.00 */
{
	{  44,   15,  864,  430, 1408,  806 }, /* 640x400 */
	{ 128,   35,  792,  385, 1408,  806 },
	{  44,   15,  864,  430, 1408,  806 },
	{ 128,   35,  792,  385, 1408,  806 },
	{  22,    9,  864,  516, 1408,  806 }, /* 640x480 */
	{   8,    5, 1056,  655, 1408,  806 }, /* 800x600 */
	{   0,    0,    0,    0,    0,    0 }, /* 1024x768 */
	{   0,    0,    0,    0,    0,    0 }, /* 1280x1024 */
	{   0,    0,    0,    0,    0,    0 },
	{   0,    0,    0,    0,    0,    0 },
	{   1,    1, 1408,  806, 1408,  806 }  /* 1280x720 */
};

/* About 1280x768: For TMDS, Panel_1280x768 will only be set if
 * the panel is a Fujitsu 7911 (VL-17WDX8) (with clock 81, 1688x802)
 * Other TMDS panels of this resolution will be treated as custom.
 * For LVDS, we know another type (_2).
 * (Note: 1280x768_3 is now special for SiS301/NetVista
 */

static const struct SiS_LCDData SiS_StLCD1280x768_2Data[] = /* 2.03.00 */
{
	{  64,   21,  858,  434, 1408,  806 }, /* 640x400 */
	{  32,    9,  858,  372, 1408,  806 },
	{  64,   21,  858,  434, 1408,  806 },
	{  32,    9,  858,  372, 1408,  806 },
	{ 143,   68, 1024,  527, 1408,  806 }, /* 640x480 */
	{  64,   51, 1364,  663, 1408,  806 }, /* 800x600 */
	{  88,   81, 1296,  806, 1408,  806 }, /* 1024x768 */
	{   0,    0,    0,    0,    0,    0 },
	{   1,    1, 1408,  806, 1408,  806 }, /* 1280x768 */
	{   0,    0,    0,    0,    0,    0 },
	{  16,   15, 1600,  750, 1600,  806 }  /* 1280x720 - from Ext */
};

static const struct SiS_LCDData SiS_ExtLCD1280x768_2Data[] = /* 2.03.00 */
{
	{  16,    5,  960,  410, 1600,  806 }, /* 640x400 */
	{  64,   21, 1152,  364, 1600,  806 },
	{  16,    5,  960,  410, 1600,  806 },
	{  64,   21, 1152,  364, 1600,  806 },
	{  32,   13, 1040,  493, 1600,  806 }, /* 640x480 */
	{  16,    9, 1152,  618, 1600,  806 }, /* 800x600 */
	{  25,   21, 1344,  796, 1600,  806 }, /* 1024x768 */
	{   0,    0,    0,    0,    0,    0 },
	{   1,    1, 1600,  806, 1600,  806 }, /* 1280x768 */
	{   0,    0,    0,    0,    0,    0 },
	{  16,   15, 1600,  750, 1600,  806 }  /* 1280x720 */
};

#if 0  /* Not used; _3 now reserved for NetVista (SiS301) */
static const struct SiS_LCDData SiS_LCD1280x768_3Data[] =
{
	{  64,   25, 1056,  422, 1664,  798 },			/* 640x400 */
	{ 128,   39,  884,  396, 1408,  806 }, /* ,640 */
	{  64,   25, 1056,  422, 1664,  798 },			/* 640x400 */
	{ 128,   39,  884,  396, 1408,  806 }, /* ,640 */
	{  32,   15, 1056,  513, 1408,  806 }, /* ,664 */	/* 640x480 */
	{ 176,  125, 1280,  640, 1408,  806 }, /* ,768 */	/* 800x600 */
	{  64,   61, 1342,  806, 1408,  806 },			/* 1024x768 */
	{   0,    0,    0,    0,    0,    0 },
	{   1,    1, 1408,  806, 1408,  806 },			/* 1280x768 */
	{   0,    0,    0,    0,    0,    0 },
	{  16,   15, 1600,  750, 1600,  806 }  /* 1280x720  from above */
};
#endif

static const struct SiS_LCDData SiS_LCD1280x800Data[] = /* 0.93.12a (TMDS) */
{
	{ 128,   51, 1122,  412, 1408,  816 },  /* 640x400 */
	{ 128,   49, 1232,  361, 1408,  816 },
	{ 128,   51, 1122,  412, 1408,  816 },
	{ 128,   49, 1232,  361, 1408,  816 },
	{   8,    3,  880,  491, 1408,  816 },  /* 640x480 */
	{  11,    6, 1024,  612, 1408,  816 },  /* 800x600 */
	{  22,   21, 1400,  784, 1408,  816 },  /* 1024x768 */
	{   0,    0,    0,    0,    0,    0 },  /* 1280x1024 */
	{   1,    1, 1408,  816, 1408,  816 },  /* 1280x800 */
	{   0,    0,    0,    0,    0,    0 },  /* 1280x768 (patch index) */
	{   0,    0,    0,    0,    0,    0 }   /* 1280x720 */
};

static const struct SiS_LCDData SiS_LCD1280x800_2Data[] = /* 2.03.00 (LVDS) */
{
	{  97,   42, 1344,  409, 1552,  812 }, /* 640x400 */
	{  97,   35, 1280,  358, 1552,  812 },
	{  97,   42, 1344,  409, 1552,  812 },
	{  97,   35, 1280,  358, 1552,  812 },
	{  97,   39, 1040,  488, 1552,  812 }, /* 640x480 */
	{ 194,  105, 1120,  608, 1552,  812 }, /* 800x600 */
	{  97,   84, 1400,  780, 1552,  812 }, /* 1024x768 */
	{   0,    0,    0,    0,    0,    0 }, /* 1280x1024 */
	{   1,    1, 1552,  812, 1552,  812 }, /* 1280x800 */
	{  97,   96, 1600,  780, 1552,  812 }, /* 1280x768 - patch index */
	{  97,   90, 1600,  730, 1552,  812 }  /* 1280x720 */
};

#if 0
static const struct SiS_LCDData SiS_LCD1280x800_3Data[] = /* 2.02.05a (LVDS); m250 */
{
	{ 128,   51, 1122,  412, 1408,  816 }, /* 640x400 */
	{ 128,   49, 1232,  361, 1408,  816 },
	{ 128,   51, 1122,  412, 1408,  816 },
	{ 128,   49, 1232,  361, 1408,  816 },
	{   8,    3,  880,  491, 1408,  816 }, /* 640x480 */
	{  11,    6, 1024,  612, 1408,  816 }, /* 800x600 */
	{  22,   21, 1400,  784, 1408,  816 }, /* 1024x768 */
	{   0,    0,    0,    0,    0,    0 }, /* 1280x1024 */
	{   1,    1, 1408,  816, 1408,  816 }, /* 1280x800 */
	{   0,    0,    0,    0,    0,    0 }, /* 1280x768 - patch index */
	{   0,    0,    0,    0,    0,    0 }  /* 1280x720 */
};
#endif

static const struct SiS_LCDData SiS_LCD1280x854Data[] = /* 2.21.00CS (LVDS) */
{
	{  56,   15,  936,  410, 1664,  861 },  /* 640x400 */
	{  64,   25, 1586,  355, 1664,  861 },
	{  56,   15,  936,  410, 1664,  861 },
	{  64,   25, 1586,  355, 1664,  861 },
	{  91,   45, 1464,  485, 1664,  861 },  /* 640x480 */
	{ 182,   75,  976,  605, 1664,  861 },  /* 800x600 */
	{  91,   66, 1342,  774, 1664,  861 },  /* 1024x768 */
	{   0,    0,    0,    0,    0,    0 },  /* 1280x1024 */
	{  26,   25, 1708,  807, 1664,  861 },  /* 1280x800 */
	{  13,   12, 1708,  774, 1664,  861 },  /* 1280x768 - patch index */
	{  52,   45, 1708,  725, 1664,  861 },  /* 1280x720 */
	{   0,    0,    0,    0,    0,    0 },
	{   0,    0,    0,    0,    0,    0 },
	{   1,    1, 1664,  861, 1664,  861 }   /* 1280x854 */
};

static const struct SiS_LCDData SiS_LCD1280x960Data[] =
{
	{    9,   2,  800,  500, 1800, 1000 },
	{    9,   2,  800,  500, 1800, 1000 },
	{    4,   1,  900,  500, 1800, 1000 },
	{    4,   1,  900,  500, 1800, 1000 },
	{    9,   2,  800,  500, 1800, 1000 },
	{   30,  11, 1056,  625, 1800, 1000 },
	{    5,   3, 1350,  800, 1800, 1000 },
	{    1,   1, 1576, 1050, 1576, 1050 },
	{    1,   1, 1800, 1000, 1800, 1000 },
	{   0,    0,    0,    0,    0,    0 },
	{   0,    0,    0,    0,    0,    0 },
	{   0,    0,    0,    0,    0,    0 },
	{   0,    0,    0,    0,    0,    0 },
	{   0,    0,    0,    0,    0,    0 }
};

static const struct SiS_LCDData SiS_StLCD1400x1050Data[] =
{
	{ 211,  100, 2100,  408, 1688, 1066 },
	{ 211,   64, 1536,  358, 1688, 1066 },
	{ 211,  100, 2100,  408, 1688, 1066 },
	{ 211,   64, 1536,  358, 1688, 1066 },
	{ 211,   48,  840,  488, 1688, 1066 },
	{ 211,   72, 1008,  609, 1688, 1066 },
	{ 211,  128, 1400,  776, 1688, 1066 },
	{ 211,  205, 1680, 1041, 1688, 1066 },
	{   1,    1, 1688, 1066, 1688, 1066 },
	{   0,    0,    0,    0,    0,    0 },
	{   0,    0,    0,    0,    0,    0 },
	{   0,    0,    0,    0,    0,    0 },
	{   0,    0,    0,    0,    0,    0 },
	{   0,    0,    0,    0,    0,    0 }
};

static const struct SiS_LCDData SiS_ExtLCD1400x1050Data[] =
{
/*	{ 211,   60, 1260,  410, 1688, 1066 },    640x400 (6330) */
	{ 211,  100, 2100,  408, 1688, 1066 }, /* 640x400 (6325) WORKS */
	{ 211,   64, 1536,  358, 1688, 1066 },
	{ 211,  100, 2100,  408, 1688, 1066 },
	{ 211,   64, 1536,  358, 1688, 1066 },
/*	{ 211,   80, 1400,  490, 1688, 1066 },    640x480 (6330) */
	{ 211,   48,  840,  488, 1688, 1066 }, /* 640x480 (6325) WORKS */
/*	{ 211,  117, 1638,  613, 1688, 1066 },    800x600 (6330) */
	{ 211,   72, 1008,  609, 1688, 1066 }, /* 800x600 (6325) WORKS */
	{ 211,  128, 1400,  776, 1688, 1066 }, /* 1024x768 */
	{ 211,  205, 1680, 1041, 1688, 1066 }, /* 1280x1024 - not used (always unscaled) */
	{   1,    1, 1688, 1066, 1688, 1066 }, /* 1400x1050 */
	{   0,    0,    0,    0,    0,    0 }, /* kludge */
	{ 211,  120, 1400,  730, 1688, 1066 }, /* 1280x720 */
	{   0,    0,    0,    0,    0,    0 },
	{   0,    0,    0,    0,    0,    0 },
	{   0,    0,    0,    0,    0,    0 }
};

static const struct SiS_LCDData SiS_LCD1680x1050Data[] =
{
	{  95,   24, 1260,  410, 1900, 1066 }, /*  0 640x400 */
	{  10,    3, 1710,  362, 1900, 1066 },
	{  95,   24, 1260,  410, 1900, 1066 },
	{  10,    3, 1710,  362, 1900, 1066 },
	{  95,   32, 1400,  490, 1900, 1066 }, /*  4 640x480 */
	{  95,   42, 1470,  610, 1900, 1066 }, /*  5 800x600 */
	{  95,   64, 1750,  784, 1900, 1066 }, /*  6 1024x768 */
	{  95,   94, 1900, 1055, 1900, 1066 }, /*  7 1280x1024 */
	{  41,   31, 1900,  806, 1900, 1066 }, /*  8 1280x768 */
	{  95,   69, 1800,  817, 1900, 1066 }, /*  9 1280x800 patch index */
	{  13,    9, 1900,  739, 1900, 1066 }, /* 10 1280x720 */
	{  95,   94, 1880, 1066, 1900, 1066 }, /* 11 1400x1050 patch index */
	{   1,    1, 1900, 1066, 1900, 1066 }, /* 12 1680x1050 */
	{   0,    0,    0,    0,    0,    0 }
};

static const struct SiS_LCDData SiS_StLCD1600x1200Data[] =
{
	{27,  4, 800, 500, 2160, 1250 },
	{27,  4, 800, 500, 2160, 1250 },
	{ 6,  1, 900, 500, 2160, 1250 },
	{ 6,  1, 900, 500, 2160, 1250 },
	{27,  1, 800, 500, 2160, 1250 },
	{ 4,  1,1080, 625, 2160, 1250 },
	{ 5,  2,1350, 800, 2160, 1250 },
	{135,88,1600,1100, 2160, 1250 },
	{72, 49,1680,1092, 2160, 1250 },
	{ 1,  1,2160,1250, 2160, 1250 },
	{ 0,  0,   0,   0,    0,    0 },
	{ 0,  0,   0,   0,    0,    0 },
	{ 0,  0,   0,   0,    0,    0 },
	{ 0,  0,   0,   0,    0,    0 }
};

static const struct SiS_LCDData SiS_ExtLCD1600x1200Data[] =
{
	{72,11, 990, 422, 2160, 1250 }, /* 640x400 (6330) WORKS */
/*	{27, 4, 800, 500, 2160, 1250 },    640x400 (6235) */
	{27, 4, 800, 500, 2160, 1250 },
	{ 6, 1, 900, 500, 2160, 1250 },
	{ 6, 1, 900, 500, 2160, 1250 },
	{45, 8, 960, 505, 2160, 1250 }, /* 640x480 (6330) WORKS */
/*	{27, 1, 800, 500, 2160, 1250 },    640x480 (6325) */
	{ 4, 1,1080, 625, 2160, 1250 },
	{ 5, 2,1350, 800, 2160, 1250 },
	{27,16,1500,1064, 2160, 1250 }, /* 1280x1024 */
	{72,49,1680,1092, 2160, 1250 }, /* 1400x1050 (6330, was not supported on 6325) */
	{ 1, 1,2160,1250, 2160, 1250 },
	{ 0, 0,   0,   0,    0,    0 },
	{ 0, 0,   0,   0,    0,    0 },
	{ 0, 0,   0,   0,    0,    0 },
	{ 0, 0,   0,   0,    0,    0 }
};

static const struct SiS_LCDData SiS_NoScaleData[] =
{
	{ 1, 1, 800, 449, 800, 449 },  /* 0x00: 320x200, 640x400 */
	{ 1, 1, 800, 449, 800, 449 },
	{ 1, 1, 900, 449, 900, 449 },
	{ 1, 1, 900, 449, 900, 449 },
	{ 1, 1, 800, 525, 800, 525 },  /* 0x04: 320x240, 640x480  */
	{ 1, 1,1056, 628,1056, 628 },  /* 0x05: 400x300, 800x600  */
	{ 1, 1,1344, 806,1344, 806 },  /* 0x06: 512x384, 1024x768 */
	{ 1, 1,1688,1066,1688,1066 },  /* 0x07: 1280x1024 */
        { 1, 1,1688, 802,1688, 802 },  /* 0x08: 1280x768: Fujitsu, TMDS only */
        { 1, 1,2160,1250,2160,1250 },  /* 0x09: 1600x1200 */
	{ 1, 1,1800,1000,1800,1000 },  /* 0x0a: 1280x960  */
	{ 1, 1,1688,1066,1688,1066 },  /* 0x0b: 1400x1050 */
	{ 1, 1,1650, 750,1650, 750 },  /* 0x0c: 1280x720 (TMDS, projector)  */
	{ 1, 1,1552, 812,1552, 812 },  /* 0x0d: 1280x800_2 (LVDS) (was: 1408,816/ 1656,841) */
	{ 1, 1,1900,1066,1900,1066 },  /* 0x0e: 1680x1050 (LVDS) */
	{ 1, 1,1660, 806,1660, 806 },  /* 0x0f: 1280x768_2 (LVDS) */
	{ 1, 1,1664, 798,1664, 798 },  /* 0x10: 1280x768_3 (NetVista SiS 301) - TODO */
	{ 1, 1,1688, 802,1688, 802 },  /* 0x11: 1280x768   (TMDS Fujitsu) */
	{ 1, 1,1408, 806,1408, 806 },  /* 0x12: 1280x720 (LVDS) */
	{ 1, 1, 896, 497, 896, 497 },  /* 0x13: 720x480 */
	{ 1, 1, 912, 597, 912, 597 },  /* 0x14: 720x576 */
	{ 1, 1, 912, 597, 912, 597 },  /* 0x15: 768x576 */
	{ 1, 1,1056, 497,1056, 497 },  /* 0x16: 848x480 */
	{ 1, 1,1064, 497,1064, 497 },  /* 0x17: 856x480 */
	{ 1, 1,1056, 497,1056, 497 },  /* 0x18: 800x480 */
	{ 1, 1,1328, 739,1328, 739 },  /* 0x19: 1024x576 */
	{ 1, 1,1680, 892,1680, 892 },  /* 0x1a: 1152x864 */
	{ 1, 1,1808, 808,1808, 808 },  /* 0x1b: 1360x768 */
	{ 1, 1,1104, 563,1104, 563 },  /* 0x1c: 960x540 */
	{ 1, 1,1120, 618,1120, 618 },  /* 0x1d: 960x600 */
	{ 1, 1,1408, 816,1408, 816 },  /* 0x1f: 1280x800 (TMDS special) */
	{ 1, 1,1760,1235,1760,1235 },  /* 0x20: 1600x1200 for LCDA */
	{ 1, 1,2048,1320,2048,1320 },  /* 0x21: 1600x1200 for non-SiS LVDS */
	{ 1, 1,1664, 861,1664, 861 }   /* 0x22: 1280x854 */
};

/**************************************************************/
/* LVDS ----------------------------------------------------- */
/**************************************************************/

/* FSTN/DSTN 320x240, 2 variants */
static const struct SiS_LVDSData SiS_LVDS320x240Data_1[]=
{
	{ 848, 433, 400, 525},
	{ 848, 389, 400, 525},
	{ 848, 433, 400, 525},
	{ 848, 389, 400, 525},
	{ 848, 518, 400, 525},
	{1056, 628, 400, 525},
	{ 400, 525, 400, 525}  /* xSTN */
};

static const struct SiS_LVDSData SiS_LVDS320x240Data_2[]=
{
	{ 800, 445, 800, 525},
	{ 800, 395, 800, 525},
	{ 800, 445, 800, 525},
	{ 800, 395, 800, 525},
	{ 800, 525, 800, 525},
	{1056, 628,1056, 628},
	{ 480, 525, 480, 525} /* xSTN */
};

static const struct SiS_LVDSData SiS_LVDS640x480Data_1[]=
{
	{ 800, 445, 800, 525},   /* 800, 449, 800, 449 */
	{ 800, 395, 800, 525},
	{ 800, 445, 800, 525},
	{ 800, 395, 800, 525},
	{ 800, 525, 800, 525}
};

static const struct SiS_LVDSData SiS_LVDS800x600Data_1[]=
{
	{ 848, 433,1060, 629},
	{ 848, 389,1060, 629},
	{ 848, 433,1060, 629},
	{ 848, 389,1060, 629},
	{ 848, 518,1060, 629},
	{1056, 628,1056, 628}
};

static const struct SiS_LVDSData SiS_LVDS1024x600Data_1[] =
{
	{ 840, 604,1344, 800},
	{ 840, 560,1344, 800},
	{ 840, 604,1344, 800},
	{ 840, 560,1344, 800},
	{ 840, 689,1344, 800},
	{1050, 800,1344, 800},
	{1344, 800,1344, 800}
};

static const struct SiS_LVDSData SiS_LVDS1024x768Data_1[]=
{
	{ 840, 438,1344, 806},
	{ 840, 409,1344, 806},
	{ 840, 438,1344, 806},
	{ 840, 409,1344, 806},
	{ 840, 518,1344, 806},   /* 640x480 */
	{1050, 638,1344, 806},   /* 800x600 */
	{1344, 806,1344, 806},   /* 1024x768 */
};

static const struct SiS_LVDSData SiS_CHTVUNTSCData[]=
{
	{ 840, 600, 840, 600},
	{ 840, 600, 840, 600},
	{ 840, 600, 840, 600},
	{ 840, 600, 840, 600},
	{ 784, 600, 784, 600},
	{1064, 750,1064, 750},
        {1160, 945,1160, 945}
};

static const struct SiS_LVDSData SiS_CHTVONTSCData[]=
{
	{ 840, 525, 840, 525},
	{ 840, 525, 840, 525},
	{ 840, 525, 840, 525},
	{ 840, 525, 840, 525},
	{ 784, 525, 784, 525},
	{1040, 700,1040, 700},
        {1160, 840,1160, 840}
};

/* CRT1 CRTC data for slave modes */

static const struct SiS_LVDSCRT1Data SiS_LVDSCRT1320x240_1[] =
{
 {{0x65,0x4f,0x89,0x56,0x83,0xaa,0x1f,
   0x90,0x85,0x8f,0xab,0x30,0x00,0x05,
   0x00 }},
 {{0x65,0x4f,0x89,0x56,0x83,0x83,0x1f,
   0x5e,0x83,0x5d,0x79,0x10,0x00,0x05,
   0x00 }},
 {{0x65,0x4f,0x89,0x54,0x9f,0xc4,0x1f,
   0x92,0x89,0x8f,0xb5,0x30,0x00,0x01,
   0x00 }},
 {{0x65,0x4f,0x89,0x56,0x83,0x83,0x1f,
   0x5e,0x83,0x5d,0x79,0x10,0x00,0x05,
   0x00 }},
 {{0x65,0x4f,0x89,0x56,0x83,0x04,0x3e,
   0xe0,0x85,0xdf,0xfb,0x10,0x00,0x05,
   0x00 }},
 {{0x7f,0x63,0x83,0x6c,0x1c,0x72,0xf0,
   0x58,0x8c,0x57,0x73,0x20,0x00,0x06,
   0x01 }},
 {{0x2d,0x27,0x90,0x2c,0x80,0x0b,0x3e,
   0xe9,0x8b,0xe7,0x04,0x00,0x00,0x00,
   0x00 }}
};

static const struct SiS_LVDSCRT1Data SiS_LVDSCRT1320x240_2[] =
{
 {{0x5f,0x4f,0x82,0x55,0x81,0x0b,0x3e,
   0xe9,0x8b,0xdf,0x04,0x30,0x00,0x05,
   0x00}},
 {{0x5f,0x4f,0x82,0x55,0x81,0x0b,0x3e,
   0xe9,0x8b,0xdf,0x04,0x30,0x00,0x05,
   0x00}},
 {{0x5f,0x4f,0x82,0x55,0x81,0x0b,0x3e,
   0xe9,0x8b,0xdf,0x04,0x30,0x00,0x05,
   0x00}},
 {{0x5f,0x4f,0x82,0x55,0x81,0x0b,0x3e,
   0xe9,0x8b,0xdf,0x04,0x30,0x00,0x05,
   0x00}},
 {{0x5f,0x4f,0x82,0x55,0x81,0x0b,0x3e,
   0xe9,0x8b,0xdf,0x04,0x30,0x00,0x05,
   0x00}},
 {{0x7f,0x63,0x83,0x6c,0x1c,0x72,0xf0,
   0x58,0x8c,0x57,0x73,0x20,0x00,0x06,
   0x01}},
#if 0
 {{0x2d,0x27,0x90,0x2c,0x80,0x0b,0x3e,
   0xe9,0x8b,0xe7,0x04,0x00,0x00,0x00,
   0x00}}
#endif
 {{0x5f,0x4f,0x83,0x55,0x81,0x0b,0x3e,
   0xe9,0x8b,0xe8,0x0c,0x00,0x00,0x05,
   0x00}},
};

static const struct SiS_LVDSCRT1Data SiS_LVDSCRT1320x240_2_H[] =
{
 {{0x65,0x4f,0x89,0x56,0x83,0xaa,0x1f,
   0x90,0x85,0x8f,0xab,0x30,0x00,0x05,
   0x00}},
 {{0x65,0x4f,0x89,0x56,0x83,0x83,0x1f,
   0x5e,0x83,0x5d,0x79,0x10,0x00,0x05,
   0x00}},
 {{0x65,0x4f,0x89,0x54,0x9f,0xc4,0x1f,
   0x92,0x89,0x8f,0xb5,0x30,0x00,0x01,
   0x00}},
 {{0x65,0x4f,0x89,0x56,0x83,0x83,0x1f,
   0x5e,0x83,0x5d,0x79,0x10,0x00,0x05,
   0x00}},
 {{0x65,0x4f,0x89,0x56,0x83,0x04,0x3e,
   0xe0,0x85,0xdf,0xfb,0x10,0x00,0x05,
   0x00}},
 {{0x7f,0x63,0x83,0x6c,0x1c,0x72,0xf0,
   0x58,0x8c,0x57,0x73,0x20,0x00,0x06,
   0x01}},
 {{0x2d,0x27,0x90,0x2c,0x80,0x0b,0x3e,
   0xe9,0x8b,0xe7,0x04,0x00,0x00,0x00,
   0x00}}
};

static const struct SiS_LVDSCRT1Data SiS_LVDSCRT1320x240_3[] =
{
 {{0x5f,0x4f,0x82,0x55,0x81,0x0b,0x3e,
   0xe9,0x8b,0xdf,0x04,0x00,0x00,0x05,
   0x00}},
 {{0x5f,0x4f,0x82,0x55,0x81,0x0b,0x3e,
   0xe9,0x8b,0xdf,0x04,0x00,0x00,0x05,
   0x00}},
 {{0x5f,0x4f,0x82,0x55,0x81,0x0b,0x3e,
   0xe9,0x8b,0xdf,0x04,0x00,0x00,0x05,
   0x00}},
 {{0x5f,0x4f,0x82,0x55,0x81,0x0b,0x3e,
   0xe9,0x8b,0xdf,0x04,0x00,0x00,0x05,
   0x00}},
 {{0x5f,0x4f,0x82,0x55,0x81,0x0b,0x3e,
   0xe9,0x8b,0xdf,0x04,0x00,0x00,0x05,
   0x00}},
 {{0x7f,0x63,0x83,0x6c,0x1c,0x72,0xf0,
   0x58,0x8c,0x57,0x73,0x20,0x00,0x06,
   0x01}},
 {{0x2d,0x27,0x90,0x2c,0x80,0x0b,0x3e,
   0xe9,0x8b,0xe7,0x04,0x00,0x00,0x00,
   0x00}}
};

static const struct SiS_LVDSCRT1Data SiS_LVDSCRT1320x240_3_H[] =
{
 {{0x65,0x4f,0x89,0x56,0x83,0xaa,0x1f,
   0x90,0x85,0x8f,0xab,0x30,0x00,0x05,
   0x00}},
 {{0x65,0x4f,0x89,0x56,0x83,0x83,0x1f,
   0x5e,0x83,0x5d,0x79,0x10,0x00,0x05,
   0x00}},
 {{0x65,0x4f,0x89,0x54,0x9f,0xc4,0x1f,
   0x92,0x89,0x8f,0xb5,0x30,0x00,0x01,
   0x00}},
 {{0x65,0x4f,0x89,0x56,0x83,0x83,0x1f,
   0x5e,0x83,0x5d,0x79,0x10,0x00,0x05,
   0x00}},
 {{0x65,0x4f,0x89,0x56,0x83,0x04,0x3e,
   0xe0,0x85,0xdf,0xfb,0x10,0x00,0x05,
   0x00}},
 {{0x7f,0x63,0x83,0x6c,0x1c,0x72,0xf0,
   0x58,0x8c,0x57,0x73,0x20,0x00,0x06,
   0x01}},
 {{0x2d,0x27,0x90,0x2c,0x80,0x0b,0x3e,
   0xe9,0x8b,0xe7,0x04,0x00,0x00,0x00,
   0x00}}
};

static const struct SiS_LVDSCRT1Data SiS_LVDSCRT1640x480_1[] =
{
 {{0x5f,0x4f,0x82,0x55,0x81,0x0b,0x3e,
   0xe9,0x8b,0xdf,0x04,0x30,0x00,0x05,
   0x00}},
 {{0x5f,0x4f,0x82,0x55,0x81,0x0b,0x3e,
   0xe9,0x8b,0xdf,0x04,0x30,0x00,0x05,
   0x00}},
 {{0x5f,0x4f,0x82,0x55,0x81,0x0b,0x3e,
   0xe9,0x8b,0xdf,0x04,0x30,0x00,0x05,
   0x00}},
 {{0x5f,0x4f,0x82,0x55,0x81,0x0b,0x3e,
   0xe9,0x8b,0xdf,0x04,0x30,0x00,0x05,
   0x00}},
 {{0x5f,0x4f,0x82,0x55,0x81,0x0b,0x3e,
   0xe9,0x8b,0xdf,0x04,0x30,0x00,0x05,
   0x00}},
 {{0x7f,0x63,0x83,0x6c,0x1c,0x72,0xf0,
   0x58,0x8c,0x57,0x73,0x20,0x00,0x06,
   0x01}}
};

static const struct SiS_LVDSCRT1Data SiS_LVDSCRT1640x480_1_H[] =
{
 {{0x2d,0x28,0x90,0x2b,0xa0,0xbf,0x1f,
   0x9c,0x8e,0x96,0xb9,0x00,0x00,0x00,
   0x00}},
 {{0x2d,0x28,0x90,0x2b,0xa0,0xbf,0x1f,
   0x83,0x85,0x63,0xba,0x00,0x00,0x00,
   0x00}},
 {{0x2d,0x28,0x90,0x2b,0xa0,0xbf,0x1f,
   0x9c,0x8e,0x96,0xb9,0x00,0x00,0x00,
   0x00}},
 {{0x2d,0x28,0x90,0x2b,0xa0,0xbf,0x1f,
   0x83,0x85,0x63,0xba,0x00,0x00,0x00,
   0x00}},
 {{0x2d,0x28,0x90,0x2c,0x80,0x0b,0x3e,
   0xe9,0x8b,0xe7,0x04,0x00,0x00,0x00,
   0x00}}
};

BOOLEAN		SiSInitPtr(struct SiS_Private *SiS_Pr);
#ifdef SIS_XORG_XF86
unsigned short	SiS_GetModeID(int VGAEngine, unsigned int VBFlags, int HDisplay, int VDisplay,
				int Depth, BOOLEAN FSTN, int LCDwith, int LCDheight);
#endif
unsigned short	SiS_GetModeID_LCD(int VGAEngine, unsigned int VBFlags, int HDisplay,
				int VDisplay, int Depth, BOOLEAN FSTN,
				unsigned short CustomT, int LCDwith, int LCDheight,
				unsigned int VBFlags2);
unsigned short	SiS_GetModeID_TV(int VGAEngine, unsigned int VBFlags, int HDisplay,
				int VDisplay, int Depth, unsigned int VBFlags2);
unsigned short	SiS_GetModeID_VGA2(int VGAEngine, unsigned int VBFlags, int HDisplay,
				int VDisplay, int Depth, unsigned int VBFlags2);

void		SiS_SetReg(SISIOADDRESS port, unsigned short index, unsigned short data);
void		SiS_SetRegByte(SISIOADDRESS port, unsigned short data);
void		SiS_SetRegShort(SISIOADDRESS port, unsigned short data);
void		SiS_SetRegLong(SISIOADDRESS port, unsigned int data);
unsigned char	SiS_GetReg(SISIOADDRESS port, unsigned short index);
unsigned char	SiS_GetRegByte(SISIOADDRESS port);
unsigned short	SiS_GetRegShort(SISIOADDRESS port);
unsigned int	SiS_GetRegLong(SISIOADDRESS port);
void		SiS_SetRegANDOR(SISIOADDRESS Port, unsigned short Index, unsigned short DataAND,
				unsigned short DataOR);
void		SiS_SetRegAND(SISIOADDRESS Port,unsigned short Index, unsigned short DataAND);
void		SiS_SetRegOR(SISIOADDRESS Port,unsigned short Index, unsigned short DataOR);

void		SiS_DisplayOn(struct SiS_Private *SiS_Pr);
void		SiS_DisplayOff(struct SiS_Private *SiS_Pr);
void		SiSRegInit(struct SiS_Private *SiS_Pr, SISIOADDRESS BaseAddr);
#ifndef SIS_LINUX_KERNEL
void		SiSSetLVDSetc(struct SiS_Private *SiS_Pr);
#endif
void		SiS_SetEnableDstn(struct SiS_Private *SiS_Pr, int enable);
void		SiS_SetEnableFstn(struct SiS_Private *SiS_Pr, int enable);
unsigned short	SiS_GetModeFlag(struct SiS_Private *SiS_Pr, unsigned short ModeNo,
				unsigned short ModeIdIndex);
BOOLEAN		SiSDetermineROMLayout661(struct SiS_Private *SiS_Pr);
#ifndef SIS_LINUX_KERNEL
void		SiS_GetVBType(struct SiS_Private *SiS_Pr);
#endif

BOOLEAN		SiS_SearchModeID(struct SiS_Private *SiS_Pr, unsigned short *ModeNo,
				unsigned short *ModeIdIndex);
unsigned short	SiS_GetModePtr(struct SiS_Private *SiS_Pr, unsigned short ModeNo,
				unsigned short ModeIdIndex);
unsigned short  SiS_GetRefCRTVCLK(struct SiS_Private *SiS_Pr, unsigned short Index, int UseWide);
unsigned short  SiS_GetRefCRT1CRTC(struct SiS_Private *SiS_Pr, unsigned short Index, int UseWide);
unsigned short	SiS_GetColorDepth(struct SiS_Private *SiS_Pr, unsigned short ModeNo,
				unsigned short ModeIdIndex);
unsigned short	SiS_GetOffset(struct SiS_Private *SiS_Pr,unsigned short ModeNo,
				unsigned short ModeIdIndex, unsigned short RRTI);
#ifdef SIS300
void		SiS_GetFIFOThresholdIndex300(struct SiS_Private *SiS_Pr, unsigned short *idx1,
				unsigned short *idx2);
unsigned short	SiS_GetFIFOThresholdB300(unsigned short idx1, unsigned short idx2);
unsigned short	SiS_GetLatencyFactor630(struct SiS_Private *SiS_Pr, unsigned short index);
#endif
void		SiS_LoadDAC(struct SiS_Private *SiS_Pr, unsigned short ModeNo, unsigned short ModeIdIndex);
#ifdef SIS_XORG_XF86
BOOLEAN		SiSSetMode(struct SiS_Private *SiS_Pr, ScrnInfoPtr pScrn, unsigned short ModeNo,
				BOOLEAN dosetpitch);
BOOLEAN		SiSBIOSSetMode(struct SiS_Private *SiS_Pr, ScrnInfoPtr pScrn,
				DisplayModePtr mode, BOOLEAN IsCustom);
BOOLEAN		SiSBIOSSetModeCRT2(struct SiS_Private *SiS_Pr, ScrnInfoPtr pScrn,
				DisplayModePtr mode, BOOLEAN IsCustom);
BOOLEAN		SiSBIOSSetModeCRT1(struct SiS_Private *SiS_Pr, ScrnInfoPtr pScrn,
				DisplayModePtr mode, BOOLEAN IsCustom);
#endif
#ifdef SIS_LINUX_KERNEL
BOOLEAN		SiSSetMode(struct SiS_Private *SiS_Pr, unsigned short ModeNo);
#endif
void		SiS_CalcCRRegisters(struct SiS_Private *SiS_Pr, int depth);
void		SiS_CalcLCDACRT1Timing(struct SiS_Private *SiS_Pr, unsigned short ModeNo,
				unsigned short ModeIdIndex);
#ifdef SIS_XORG_XF86
void		SiS_Generic_ConvertCRData(struct SiS_Private *SiS_Pr, unsigned char *crdata, int xres,
				int yres, DisplayModePtr current);
#endif
#ifdef SIS_LINUX_KERNEL
void		SiS_Generic_ConvertCRData(struct SiS_Private *SiS_Pr, unsigned char *crdata, int xres,
				int yres, struct fb_var_screeninfo *var, BOOLEAN writeres);
#endif

/* From init301.c: */
extern void		SiS_GetVBInfo(struct SiS_Private *SiS_Pr, unsigned short ModeNo,
				unsigned short ModeIdIndex, int chkcrt2mode);
extern void		SiS_GetLCDResInfo(struct SiS_Private *SiS_Pr, unsigned short ModeNo,
				unsigned short ModeIdIndex);
extern void		SiS_SetYPbPr(struct SiS_Private *SiS_Pr);
extern void		SiS_SetTVMode(struct SiS_Private *SiS_Pr, unsigned short ModeNo,
				unsigned short ModeIdIndex);
extern void		SiS_UnLockCRT2(struct SiS_Private *SiS_Pr);
extern void		SiS_DisableBridge(struct SiS_Private *);
extern BOOLEAN		SiS_SetCRT2Group(struct SiS_Private *, unsigned short);
extern unsigned short	SiS_GetRatePtr(struct SiS_Private *SiS_Pr, unsigned short ModeNo,
				unsigned short ModeIdIndex);
extern void		SiS_WaitRetrace1(struct SiS_Private *SiS_Pr);
extern unsigned short	SiS_GetResInfo(struct SiS_Private *SiS_Pr, unsigned short ModeNo,
				unsigned short ModeIdIndex);
extern unsigned short	SiS_GetCH700x(struct SiS_Private *SiS_Pr, unsigned short tempax);
extern unsigned short	SiS_GetVCLK2Ptr(struct SiS_Private *SiS_Pr, unsigned short ModeNo,
				unsigned short ModeIdIndex, unsigned short RRTI);
extern BOOLEAN		SiS_IsVAMode(struct SiS_Private *);
extern BOOLEAN		SiS_IsDualEdge(struct SiS_Private *);

#ifdef SIS_XORG_XF86
/* From other modules: */
extern unsigned short	SiS_CheckBuildCustomMode(ScrnInfoPtr pScrn, DisplayModePtr mode,
				unsigned int VBFlags);
extern unsigned char	SiS_GetSetBIOSScratch(ScrnInfoPtr pScrn, unsigned short offset,
				unsigned char value);
extern unsigned char	SiS_GetSetModeID(ScrnInfoPtr pScrn, unsigned char id);
extern unsigned short 	SiS_GetModeNumber(ScrnInfoPtr pScrn, DisplayModePtr mode,
				unsigned int VBFlags);
#endif

#ifdef SIS_LINUX_KERNEL
#ifdef SIS300
extern unsigned int	sisfb_read_nbridge_pci_dword(struct SiS_Private *SiS_Pr, int reg);
extern void		sisfb_write_nbridge_pci_dword(struct SiS_Private *SiS_Pr, int reg,
				unsigned int val);
#endif
#ifdef SIS315H
extern void		sisfb_write_nbridge_pci_byte(struct SiS_Private *SiS_Pr, int reg,
				unsigned char val);
extern unsigned int	sisfb_read_mio_pci_word(struct SiS_Private *SiS_Pr, int reg);
#endif
#endif

#endif

