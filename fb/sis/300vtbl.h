/* $XFree86$ */
/* $XdotOrg$ */
/*
 * Register settings for SiS 300 series
 *
 * Copyright (C) 2001-2005 by <PERSON>, Vienna, Austria
 *
 * If distributed as part of the Linux kernel, the following license terms
 * apply:
 *
 * * This program is free software; you can redistribute it and/or modify
 * * it under the terms of the GNU General Public License as published by
 * * the Free Software Foundation; either version 2 of the named License,
 * * or any later version.
 * *
 * * This program is distributed in the hope that it will be useful,
 * * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * * GNU General Public License for more details.
 * *
 * * You should have received a copy of the GNU General Public License
 * * along with this program; if not, write to the Free Software
 * * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-1307, USA
 *
 * Otherwise, the following license terms apply:
 *
 * * Redistribution and use in source and binary forms, with or without
 * * modification, are permitted provided that the following conditions
 * * are met:
 * * 1) Redistributions of source code must retain the above copyright
 * *    notice, this list of conditions and the following disclaimer.
 * * 2) Redistributions in binary form must reproduce the above copyright
 * *    notice, this list of conditions and the following disclaimer in the
 * *    documentation and/or other materials provided with the distribution.
 * * 3) The name of the author may not be used to endorse or promote products
 * *    derived from this software without specific prior written permission.
 * *
 * * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
 * * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * * IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
 * * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
 * * NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
 * * THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * Author: 	Thomas Winischhofer <<EMAIL>>
 *
 */

static const struct SiS_Ext SiS300_EModeIDTable[] =
{
	{0x6a,0x2212,0x0102,SIS_RI_800x600,  0x00,0x00,0x00,0x00,0x00,-1},  /* 800x600x? */
	{0x2e,0x0a1b,0x0101,SIS_RI_640x480,  0x00,0x00,0x00,0x00,0x08,-1},
	{0x2f,0x021b,0x0100,SIS_RI_640x400,  0x00,0x00,0x00,0x00,0x10,-1},  /* 640x400x8 */
	{0x30,0x2a1b,0x0103,SIS_RI_800x600,  0x00,0x00,0x00,0x00,0x00,-1},
	{0x31,0x4a1b,0x0000,SIS_RI_720x480,  0x00,0x00,0x00,0x00,0x11,-1},  /* 720x480x8 */
	{0x32,0x6a1b,0x0000,SIS_RI_720x576,  0x00,0x00,0x00,0x00,0x12,-1},  /* 720x576x8 */
	{0x33,0x4a1d,0x0000,SIS_RI_720x480,  0x00,0x00,0x00,0x00,0x11,-1},  /* 720x480x16 */
	{0x34,0x6a1d,0x0000,SIS_RI_720x576,  0x00,0x00,0x00,0x00,0x12,-1},  /* 720x576x16 */
	{0x35,0x4a1f,0x0000,SIS_RI_720x480,  0x00,0x00,0x00,0x00,0x11,-1},  /* 720x480x32 */
	{0x36,0x6a1f,0x0000,SIS_RI_720x576,  0x00,0x00,0x00,0x00,0x12,-1},  /* 720x576x32 */
	{0x37,0x0212,0x0104,SIS_RI_1024x768, 0x00,0x00,0x00,0x00,0x13,-1},  /* 1024x768x? */
	{0x38,0x0a1b,0x0105,SIS_RI_1024x768, 0x00,0x00,0x00,0x00,0x13,-1},  /* 1024x768x8 */
	{0x3a,0x0e3b,0x0107,SIS_RI_1280x1024,0x00,0x00,0x00,0x00,0x1a,-1},  /* 1280x1024x8 */
	{0x3c,0x063b,0x0130,SIS_RI_1600x1200,0x00,0x00,0x00,0x00,0x1e,-1},
	{0x3d,0x067d,0x0131,SIS_RI_1600x1200,0x00,0x00,0x00,0x00,0x1e,-1},
	{0x40,0x921c,0x010d,SIS_RI_320x200,  0x00,0x00,0x00,0x00,0x23,-1},  /* 320x200x15 */
	{0x41,0x921d,0x010e,SIS_RI_320x200,  0x00,0x00,0x00,0x00,0x23,-1},  /* 320x200x16 */
	{0x43,0x0a1c,0x0110,SIS_RI_640x480,  0x00,0x00,0x00,0x00,0x08,-1},
	{0x44,0x0a1d,0x0111,SIS_RI_640x480,  0x00,0x00,0x00,0x00,0x08,-1},
	{0x46,0x2a1c,0x0113,SIS_RI_800x600,  0x00,0x00,0x00,0x00,0x00,-1},  /* 800x600x15 */
	{0x47,0x2a1d,0x0114,SIS_RI_800x600,  0x00,0x00,0x00,0x00,0x00,-1},  /* 800x600x16 */
	{0x49,0x0a3c,0x0116,SIS_RI_1024x768, 0x00,0x00,0x00,0x00,0x13,-1},
	{0x4a,0x0a3d,0x0117,SIS_RI_1024x768, 0x00,0x00,0x00,0x00,0x13,-1},
	{0x4c,0x0e7c,0x0119,SIS_RI_1280x1024,0x00,0x00,0x00,0x00,0x1a,-1},
	{0x4d,0x0e7d,0x011a,SIS_RI_1280x1024,0x00,0x00,0x00,0x00,0x1a,-1},
	{0x50,0x921b,0x0132,SIS_RI_320x240,  0x00,0x00,0x00,0x00,0x24,-1},  /* 320x240x8  */
	{0x51,0xb21b,0x0133,SIS_RI_400x300,  0x00,0x00,0x00,0x00,0x25,-1},  /* 400x300x8  */
	{0x52,0x921b,0x0134,SIS_RI_512x384,  0x00,0x00,0x00,0x00,0x26,-1},  /* 512x384x8  */
	{0x56,0x921d,0x0135,SIS_RI_320x240,  0x00,0x00,0x00,0x00,0x24,-1},  /* 320x240x16 */
	{0x57,0xb21d,0x0136,SIS_RI_400x300,  0x00,0x00,0x00,0x00,0x25,-1},  /* 400x300x16 */
	{0x58,0x921d,0x0137,SIS_RI_512x384,  0x00,0x00,0x00,0x00,0x26,-1},  /* 512x384x16 */
	{0x59,0x921b,0x0138,SIS_RI_320x200,  0x00,0x00,0x00,0x00,0x23,-1},  /* 320x200x8  */
	{0x5c,0x921f,0x0000,SIS_RI_512x384,  0x00,0x00,0x00,0x00,0x26,-1},  /* 512x384x32 */
	{0x5d,0x021d,0x0139,SIS_RI_640x400,  0x00,0x00,0x00,0x00,0x10,-1},  /* 640x400x16 */
	{0x5e,0x021f,0x0000,SIS_RI_640x400,  0x00,0x00,0x00,0x00,0x10,-1},  /* 640x400x32 */
	{0x62,0x0a3f,0x013a,SIS_RI_640x480,  0x00,0x00,0x00,0x00,0x08,-1},
	{0x63,0x2a3f,0x013b,SIS_RI_800x600,  0x00,0x00,0x00,0x00,0x00,-1},  /* 800x600x32 */
	{0x64,0x0a7f,0x013c,SIS_RI_1024x768, 0x00,0x00,0x00,0x00,0x13,-1},
	{0x65,0x0eff,0x013d,SIS_RI_1280x1024,0x00,0x00,0x00,0x00,0x1a,-1},
	{0x66,0x06ff,0x013e,SIS_RI_1600x1200,0x00,0x00,0x00,0x00,0x1e,-1},
	{0x68,0x067b,0x013f,SIS_RI_1920x1440,0x00,0x00,0x00,0x00,0x27,-1},
	{0x69,0x06fd,0x0140,SIS_RI_1920x1440,0x00,0x00,0x00,0x00,0x27,-1},
	{0x6b,0x07ff,0x0000,SIS_RI_1920x1440,0x00,0x00,0x00,0x00,0x27,-1},
	{0x6c,0x067b,0x0000,SIS_RI_2048x1536,0x00,0x00,0x00,0x00,0x28,-1},  /* 2048x1536x8 */
	{0x6d,0x06fd,0x0000,SIS_RI_2048x1536,0x00,0x00,0x00,0x00,0x28,-1},  /* 2048x1536x16 */
	{0x70,0x6a1b,0x0000,SIS_RI_800x480,  0x00,0x00,0x07,0x00,0x2d,-1},  /* 800x480x8 */
	{0x71,0x4a1b,0x0000,SIS_RI_1024x576, 0x00,0x00,0x00,0x00,0x30,-1},  /* 1024x576x8 */
	{0x74,0x4a1d,0x0000,SIS_RI_1024x576, 0x00,0x00,0x00,0x00,0x30,-1},  /* 1024x576x16 */
	{0x75,0x0e3d,0x0000,SIS_RI_1280x720, 0x00,0x00,0x00,0x00,0x33,-1},  /* 1280x720x16 */
	{0x76,0x6a1f,0x0000,SIS_RI_800x480,  0x00,0x00,0x07,0x00,0x2d,-1},  /* 800x480x32 */
	{0x77,0x4a3f,0x0000,SIS_RI_1024x576, 0x00,0x00,0x00,0x00,0x30,-1},  /* 1024x576x32 */
	{0x78,0x0eff,0x0000,SIS_RI_1280x720, 0x00,0x00,0x00,0x00,0x33,-1},  /* 1280x720x32 */
	{0x79,0x0e3b,0x0000,SIS_RI_1280x720, 0x00,0x00,0x00,0x00,0x33,-1},  /* 1280x720x8 */
	{0x7a,0x6a1d,0x0000,SIS_RI_800x480,  0x00,0x00,0x07,0x00,0x2d,-1},  /* 800x480x16 */
	{0x7c,0x0a3b,0x0000,SIS_RI_1280x960, 0x00,0x00,0x00,0x00,0x29,-1},  /* 1280x960x8 */
	{0x7d,0x0a7d,0x0000,SIS_RI_1280x960, 0x00,0x00,0x00,0x00,0x29,-1},  /* 1280x960x16 */
	{0x7e,0x0aff,0x0000,SIS_RI_1280x960, 0x00,0x00,0x00,0x00,0x29,-1},  /* 1280x960x32 */
	{0x20,0x4a1b,0x0000,SIS_RI_1024x600, 0x00,0x00,0x00,0x00,0x2b,-1},  /* 1024x600 */
	{0x21,0x4a3d,0x0000,SIS_RI_1024x600, 0x00,0x00,0x00,0x00,0x2b,-1},
	{0x22,0x4a7f,0x0000,SIS_RI_1024x600, 0x00,0x00,0x00,0x00,0x2b,-1},
	{0x23,0x4a1b,0x0000,SIS_RI_1152x768, 0x00,0x00,0x00,0x00,0x2c,-1},  /* 1152x768 */
	{0x24,0x4a3d,0x0000,SIS_RI_1152x768, 0x00,0x00,0x00,0x00,0x2c,-1},
	{0x25,0x4a7f,0x0000,SIS_RI_1152x768, 0x00,0x00,0x00,0x00,0x2c,-1},
	{0x29,0x4e1b,0x0000,SIS_RI_1152x864, 0x00,0x00,0x00,0x00,0x36,-1},  /* 1152x864 */
	{0x2a,0x4e3d,0x0000,SIS_RI_1152x864, 0x00,0x00,0x00,0x00,0x36,-1},
	{0x2b,0x4e7f,0x0000,SIS_RI_1152x864, 0x00,0x00,0x00,0x00,0x36,-1},
	{0x39,0x6a1b,0x0000,SIS_RI_848x480,  0x00,0x00,0x00,0x00,0x39,-1},  /* 848x480 */
	{0x3b,0x6a3d,0x0000,SIS_RI_848x480,  0x00,0x00,0x00,0x00,0x39,-1},
	{0x3e,0x6a7f,0x0000,SIS_RI_848x480,  0x00,0x00,0x00,0x00,0x39,-1},
	{0x3f,0x6a1b,0x0000,SIS_RI_856x480,  0x00,0x00,0x00,0x00,0x3b,-1},  /* 856x480 */
	{0x42,0x6a3d,0x0000,SIS_RI_856x480,  0x00,0x00,0x00,0x00,0x3b,-1},
	{0x45,0x6a7f,0x0000,SIS_RI_856x480,  0x00,0x00,0x00,0x00,0x3b,-1},
	{0x48,0x6a3b,0x0000,SIS_RI_1360x768, 0x00,0x00,0x00,0x00,0x3d,-1},  /* 1360x768 */
	{0x4b,0x6a7d,0x0000,SIS_RI_1360x768, 0x00,0x00,0x00,0x00,0x3d,-1},
	{0x4e,0x6aff,0x0000,SIS_RI_1360x768, 0x00,0x00,0x00,0x00,0x3d,-1},
	{0x4f,0x921f,0x0000,SIS_RI_320x200,  0x00,0x00,0x00,0x00,0x23,-1},  /* 320x200x32 */
	{0x53,0x921f,0x0000,SIS_RI_320x240,  0x00,0x00,0x00,0x00,0x24,-1},  /* 320x240x32 */
	{0x54,0xb21f,0x0000,SIS_RI_400x300,  0x00,0x00,0x00,0x00,0x25,-1},  /* 400x300x32 */
	{0x55,0x2e3b,0x0000,SIS_RI_1280x768, 0x00,0x00,0x00,0x00,0x3e,-1},  /* 1280x768   */
	{0x5a,0x2e7d,0x0000,SIS_RI_1280x768, 0x00,0x00,0x00,0x00,0x3e,-1},
	{0x5b,0x2eff,0x0000,SIS_RI_1280x768, 0x00,0x00,0x00,0x00,0x3e,-1},
	{0x5f,0x6a1b,0x0000,SIS_RI_768x576,  0x00,0x00,0x00,0x00,0x3f,-1},  /* 768x576x8 */
	{0x60,0x6a1d,0x0000,SIS_RI_768x576,  0x00,0x00,0x00,0x00,0x3f,-1},  /* 768x576x16 */
	{0x61,0x6a1f,0x0000,SIS_RI_768x576,  0x00,0x00,0x00,0x00,0x3f,-1},  /* 768x576x32 */
	{0x67,0x6e3b,0x0000,SIS_RI_1360x1024,0x00,0x00,0x00,0x00,0x40,-1},  /* 1360x1024x8 (BARCO) */
	{0x6f,0x6e7d,0x0000,SIS_RI_1360x1024,0x00,0x00,0x00,0x00,0x40,-1},  /* 1360x1024x16 (BARCO) */
	{0x72,0x6eff,0x0000,SIS_RI_1360x1024,0x00,0x00,0x00,0x00,0x40,-1},  /* 1360x1024x32 (BARCO) */
	{0xff,0x0000,0xffff,0,               0x00,0x00,0x00,0x00,0x00}
};

static const struct SiS_Ext2 SiS300_RefIndex[] =
{
	{0x085f,0x0d,0x03,0x05,0x05,0x6a, 800, 600, 0, 0x00, 0x00}, /* 00 */
	{0x0467,0x0e,0x04,0x05,0x05,0x6a, 800, 600, 0, 0x00, 0x00}, /* 01 */
	{0x0067,0x0f,0x07,0x48,0x05,0x6a, 800, 600, 0, 0x00, 0x00}, /* 02 - CRT1CRTC was 0x4f */
	{0x0067,0x10,0x06,0x8b,0x05,0x6a, 800, 600, 0, 0x00, 0x00}, /* 03 */
	{0x0147,0x11,0x08,0x00,0x05,0x6a, 800, 600, 0, 0x00, 0x00}, /* 04 */
	{0x0147,0x12,0x0c,0x00,0x05,0x6a, 800, 600, 0, 0x00, 0x00}, /* 05 */
	{0x0047,0x11,0x0e,0x00,0x05,0x6a, 800, 600, 0, 0x00, 0x00}, /* 06 - CRT1CRTC was 0x51 */
	{0x0047,0x11,0x13,0x00,0x05,0x6a, 800, 600, 0, 0x00, 0x00}, /* 07 */
	{0xc85f,0x05,0x00,0x04,0x04,0x2e, 640, 480, 0, 0x00, 0x00}, /* 08 */
	{0xc067,0x06,0x02,0x04,0x04,0x2e, 640, 480, 0, 0x00, 0x00}, /* 09 */
	{0xc067,0x07,0x02,0x47,0x04,0x2e, 640, 480, 0, 0x00, 0x00}, /* 0a */
	{0xc067,0x08,0x03,0x8a,0x04,0x2e, 640, 480, 0, 0x00, 0x00}, /* 0b */
	{0xc047,0x09,0x05,0x00,0x04,0x2e, 640, 480, 0, 0x00, 0x00}, /* 0c */
	{0xc047,0x0a,0x08,0x00,0x04,0x2e, 640, 480, 0, 0x00, 0x00}, /* 0d */
	{0xc047,0x0b,0x0a,0x00,0x04,0x2e, 640, 480, 0, 0x00, 0x00}, /* 0e */
	{0xc047,0x0c,0x10,0x00,0x04,0x2e, 640, 480, 0, 0x00, 0x00}, /* 0f */
	{0x487f,0x04,0x00,0x00,0x00,0x2f, 640, 400, 0, 0x4a, 0x49}, /* 10 */
	{0xc06f,0x31,0x01,0x06,0x13,0x31, 720, 480, 0, 0x00, 0x00}, /* 11 */
	{0x006f,0x32,0x4a,0x06,0x14,0x32, 720, 576, 0, 0x00, 0x00}, /* 12 */ /* 4a was 03 */
	{0x0187,0x15,0x05,0x00,0x06,0x37,1024, 768, 0, 0x00, 0x00}, /* 13 */
	{0xc877,0x16,0x09,0x06,0x06,0x37,1024, 768, 0, 0x00, 0x00}, /* 14 */
	{0xc067,0x17,0x0b,0x49,0x06,0x37,1024, 768, 0, 0x00, 0x00}, /* 15 - CRT1CRTC was 0x97 */
	{0x0267,0x18,0x0d,0x00,0x06,0x37,1024, 768, 0, 0x00, 0x00}, /* 16 */
	{0x0047,0x19,0x11,0x8c,0x06,0x37,1024, 768, 0, 0x00, 0x00}, /* 17 - CRT1CRTC was 0x59 */
	{0x0047,0x1a,0x12,0x00,0x06,0x37,1024, 768, 0, 0x00, 0x00}, /* 18 */
	{0x0007,0x1b,0x16,0x00,0x06,0x37,1024, 768, 0, 0x00, 0x00}, /* 19 - CRT1CRTC was 0x5b */
	{0x0387,0x1c,0x0d,0x00,0x07,0x3a,1280,1024, 0, 0x00, 0x00}, /* 1a - CRT1CRTC was 0x5c */
	{0x0077,0x1d,0x14,0x07,0x07,0x3a,1280,1024, 0, 0x00, 0x00}, /* 1b */
	{0x0047,0x1e,0x17,0x00,0x07,0x3a,1280,1024, 0, 0x00, 0x00}, /* 1c */
	{0x0007,0x1f,0x18,0x00,0x07,0x3a,1280,1024, 0, 0x00, 0x00}, /* 1d */
	{0x0007,0x20,0x19,0x00,0x00,0x3c,1600,1200, 0, 0x00, 0x00}, /* 1e - CRT1CRTC was 0x60 */
	{0x0007,0x21,0x1a,0x00,0x00,0x3c,1600,1200, 0, 0x00, 0x00}, /* 1f */
	{0x0007,0x22,0x1b,0x00,0x00,0x3c,1600,1200, 0, 0x00, 0x00}, /* 20 */
	{0x0007,0x23,0x1d,0x00,0x00,0x3c,1600,1200, 0, 0x00, 0x00}, /* 21 - CRT1CRTC was 0x63 */
	{0x0007,0x24,0x1e,0x00,0x00,0x3c,1600,1200, 0, 0x00, 0x00}, /* 22 */
	{0x407f,0x00,0x00,0x00,0x00,0x40, 320, 200, 0, 0x4b, 0x4b}, /* 23 */
	{0xc07f,0x01,0x00,0x04,0x04,0x50, 320, 240, 0, 0x00, 0x00}, /* 24 */
	{0x0077,0x02,0x04,0x05,0x05,0x51, 400, 300, 0, 0x00, 0x00}, /* 25 */
	{0xc877,0x03,0x09,0x06,0x06,0x52, 512, 384, 0, 0x00, 0x00}, /* 26 */  /* was c077 */
	{0x8207,0x25,0x1f,0x00,0x00,0x68,1920,1440, 0, 0x00, 0x00}, /* 27 */
	{0x0007,0x26,0x20,0x00,0x00,0x6c,2048,1536, 0, 0x00, 0x00}, /* 28 */
	{0x0067,0x27,0x14,0x08,0x0a,0x6e,1280, 960, 0, 0x00, 0x00}, /* 29 - 1280x960-60 */
	{0x0027,0x45,0x3c,0x08,0x0a,0x6e,1280, 960, 0, 0x00, 0x00}, /* 2a - 1280x960-85 */
	{0xc077,0x33,0x09,0x06,0x00,0x20,1024, 600, 0, 0x00, 0x00}, /* 2b */
	{0xc077,0x34,0x0b,0x06,0x00,0x23,1152, 768, 0, 0x00, 0x00}, /* 2c */	/* VCLK 0x09 */
	{0x0077,0x35,0x27,0x08,0x18,0x70, 800, 480, 0, 0x00, 0x00}, /* 2d */
	{0x0047,0x36,0x37,0x08,0x18,0x70, 800, 480, 0, 0x00, 0x00}, /* 2e */
	{0x0047,0x37,0x08,0x08,0x18,0x70, 800, 480, 0, 0x00, 0x00}, /* 2f */
	{0x0077,0x38,0x09,0x09,0x19,0x71,1024, 576, 0, 0x00, 0x00}, /* 30 */
	{0x0047,0x39,0x38,0x09,0x19,0x71,1024, 576, 0, 0x00, 0x00}, /* 31 */
	{0x0047,0x3a,0x11,0x09,0x19,0x71,1024, 576, 0, 0x00, 0x00}, /* 32 */
	{0x0077,0x3b,0x39,0x0a,0x0c,0x75,1280, 720, 0, 0x00, 0x00}, /* 33 */
	{0x0047,0x3c,0x3a,0x0a,0x0c,0x75,1280, 720, 0, 0x00, 0x00}, /* 34 */
	{0x0007,0x3d,0x3b,0x0a,0x0c,0x75,1280, 720, 0, 0x00, 0x00}, /* 35 */
	{0x0067,0x49,0x35,0x06,0x1a,0x29,1152, 864, 0, 0x00, 0x00}, /* 36 1152x864-60Hz  */
	{0x0067,0x3e,0x34,0x06,0x1a,0x29,1152, 864, 0, 0x00, 0x00}, /* 37 1152x864-75Hz */
	{0x0047,0x44,0x3a,0x06,0x1a,0x29,1152, 864, 0, 0x00, 0x00}, /* 38 1152x864-85Hz */
	{0x00c7,0x3f,0x28,0x00,0x16,0x39, 848, 480, 0, 0x00, 0x00}, /* 39 848x480-38Hzi */
	{0xc067,0x40,0x3d,0x0b,0x16,0x39, 848, 480, 0, 0x00, 0x00}, /* 3a 848x480-60Hz  */
	{0x00c7,0x41,0x28,0x00,0x17,0x3f, 856, 480, 0, 0x00, 0x00}, /* 3b 856x480-38Hzi */
	{0xc067,0x42,0x28,0x0c,0x17,0x3f, 856, 480, 0, 0x00, 0x00}, /* 3c 856x480-60Hz  */
	{0x0067,0x43,0x3e,0x0d,0x1b,0x48,1360, 768, 0, 0x00, 0x00}, /* 3d 1360x768-60Hz */
	{0x0077,0x46,0x3f,0x08,0x08,0x55,1280, 768, 0, 0x00, 0x00}, /* 3e 1280x768-60Hz */
	{0x006f,0x47,0x4c,0x06,0x15,0x5f, 768, 576, 0, 0x00, 0x00}, /* 3f 768x576 */
	{0x0027,0x48,0x13,0x08,0x00,0x67,1360,1024, 0, 0x00, 0x00}, /* 40 1360x1024-59Hz (BARCO1366 only) */
	{0xffff,   0,   0,   0,   0,   0,   0,   0, 0, 0x00, 0x00}
};

static const struct SiS_VBMode SiS300_VBModeIDTable[] =
{
	{0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00},
	{0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x01},
	{0x01,0x00,0x00,0x00,0x01,0x00,0x01,0x02},
	{0x03,0x00,0x00,0x00,0x02,0x00,0x02,0x00},
	{0x03,0x00,0x00,0x00,0x02,0x00,0x02,0x01},
	{0x03,0x00,0x00,0x00,0x03,0x00,0x03,0x02},
	{0x05,0x00,0x00,0x01,0x04,0x00,0x00,0x00},
	{0x06,0x00,0x00,0x01,0x05,0x00,0x02,0x00},
	{0x07,0x00,0x00,0x00,0x03,0x00,0x03,0x01},
	{0x07,0x00,0x00,0x00,0x03,0x00,0x03,0x02},
	{0x0d,0x00,0x00,0x01,0x04,0x00,0x00,0x00},
	{0x0e,0x00,0x00,0x01,0x05,0x00,0x02,0x00},
	{0x0f,0x00,0x00,0x01,0x05,0x00,0x02,0x01},
	{0x10,0x00,0x00,0x01,0x05,0x00,0x02,0x01},
	{0x11,0x00,0x00,0x01,0x05,0x00,0x02,0x03},
	{0x12,0x00,0x00,0x01,0x05,0x00,0x02,0x03},
	{0x13,0x00,0x00,0x01,0x04,0x00,0x04,0x00},
	{0x6a,0x00,0x00,0x01,0x07,0x00,0x08,0x0a},
	{0x2e,0x00,0x00,0x01,0x05,0x00,0x06,0x08},
	{0x2f,0x00,0x00,0x01,0x05,0x00,0x06,0x06},
	{0x30,0x00,0x00,0x01,0x07,0x00,0x08,0x0a},
	{0x31,0x00,0x00,0x01,0x06,0x00,0x00,0x00},
	{0x32,0x00,0x00,0x01,0x06,0x00,0x00,0x00},
	{0x33,0x00,0x00,0x01,0x06,0x00,0x00,0x00},
	{0x34,0x00,0x00,0x01,0x06,0x00,0x00,0x00},
	{0x35,0x00,0x00,0x01,0x06,0x00,0x00,0x00},
	{0x36,0x00,0x00,0x01,0x06,0x00,0x00,0x00},
	{0x37,0x00,0x00,0x01,0x00,0x00,0x0a,0x0c},
	{0x38,0x00,0x00,0x01,0x00,0x00,0x0a,0x0c},
	{0x3a,0x00,0x00,0x01,0x00,0x00,0x0b,0x0d},
	{0x40,0x00,0x00,0x01,0x04,0x00,0x05,0x05},
	{0x41,0x00,0x00,0x01,0x04,0x00,0x05,0x05},
	{0x43,0x00,0x00,0x01,0x05,0x00,0x06,0x08},
	{0x44,0x00,0x00,0x01,0x05,0x00,0x06,0x08},
	{0x46,0x00,0x00,0x01,0x07,0x00,0x08,0x0a},
	{0x47,0x00,0x00,0x01,0x07,0x00,0x08,0x0a},
	{0x49,0x00,0x00,0x01,0x00,0x00,0x0a,0x0c},
	{0x4a,0x00,0x00,0x01,0x00,0x00,0x0a,0x0c},
	{0x4c,0x00,0x00,0x01,0x00,0x00,0x0b,0x0d},
	{0x4d,0x00,0x00,0x01,0x00,0x00,0x0b,0x0d},
	{0x4f,0x00,0x00,0x01,0x04,0x00,0x05,0x05},
	{0x50,0x00,0x00,0x01,0x04,0x00,0x05,0x07},
	{0x51,0x00,0x00,0x01,0x07,0x00,0x07,0x09},
	{0x52,0x00,0x00,0x01,0x00,0x00,0x09,0x0b},
	{0x53,0x00,0x00,0x01,0x04,0x00,0x05,0x07},
	{0x54,0x00,0x00,0x01,0x07,0x00,0x07,0x09},
	{0x56,0x00,0x00,0x01,0x04,0x00,0x05,0x07},
	{0x57,0x00,0x00,0x01,0x07,0x00,0x07,0x09},
	{0x58,0x00,0x00,0x01,0x00,0x00,0x09,0x0b},
	{0x59,0x00,0x00,0x01,0x04,0x00,0x05,0x05},
	{0x5c,0x00,0x00,0x01,0x00,0x00,0x09,0x0b},
	{0x5d,0x00,0x00,0x01,0x05,0x00,0x06,0x06},
	{0x5e,0x00,0x00,0x01,0x05,0x00,0x06,0x06},
	{0x5f,0x00,0x00,0x01,0x06,0x00,0x00,0x00},
	{0x60,0x00,0x00,0x01,0x06,0x00,0x00,0x00},
	{0x61,0x00,0x00,0x01,0x06,0x00,0x00,0x00},
	{0x62,0x00,0x00,0x01,0x05,0x00,0x06,0x08},
	{0x63,0x00,0x00,0x01,0x07,0x00,0x08,0x0a},
	{0x64,0x00,0x00,0x01,0x00,0x00,0x0a,0x0c},
	{0x65,0x00,0x00,0x01,0x00,0x00,0x0b,0x0d},
	{0x6c,0x00,0x00,0x01,0x00,0x00,0x0b,0x0d},
	{0x6d,0x00,0x00,0x01,0x00,0x00,0x0b,0x0d},
	{0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00}
};

static const struct SiS_CRT1Table SiS300_CRT1Table[] =
{
 {{0x2d,0x27,0x28,0x90,0x2c,0x80,0xbf,0x1f,    /* 0x00 - 320x200 */
  0x9c,0x8e,0x8f,0x96,0xb9,0x30,0x00,0x00,     /* HRE [4],[15] is invalid - but correcting it does not work */
  0x00}},
 {{0x2d,0x27,0x28,0x90,0x2c,0x80,0x0b,0x3e,    /* 0x01 */
  0xe9,0x8b,0xdf,0xe7,0x04,0x00,0x00,0x00,     /* HRE [4],[15] is invalid - but correcting it does not work */
  0x00}},
 {{0x3d,0x31,0x31,0x81,0x37,0x1f,0x72,0xf0,    /* 0x02 */
  0x58,0x8c,0x57,0x57,0x73,0x20,0x00,0x05,
  0x01}},
 {{0x4f,0x3f,0x3f,0x93,0x45,0x0d,0x24,0xf5,
  0x02,0x88,0xff,0xff,0x25,0x10,0x00,0x01,
  0x01}},
 {{0x5f,0x4f,0x50,0x82,0x55,0x81,0xbf,0x1f,
  0x9c,0x8e,0x8f,0x96,0xb9,0x30,0x00,0x05,
  0x00}},
 {{0x5f,0x4f,0x4f,0x83,0x55,0x81,0x0b,0x3e,    /* 0x05 - corrected 640x480-60 */
  0xe9,0x8b,0xdf,0xe8,0x0c,0x00,0x00,0x05,
  0x00}},
 {{0x63,0x4f,0x4f,0x87,0x56,0x9b,0x06,0x3e,    /* 0x06 - corrected 640x480-72 */
  0xe8,0x8a,0xdf,0xe7,0x07,0x00,0x00,0x01,
  0x00}},
 {{0x64,0x4f,0x4f,0x88,0x55,0x9d,0xf2,0x1f,
  0xe0,0x83,0xdf,0xdf,0xf3,0x10,0x00,0x01,
  0x00}},
 {{0x63,0x4f,0x4f,0x87,0x5a,0x81,0xfb,0x1f,
  0xe0,0x83,0xdf,0xdf,0xfc,0x10,0x00,0x05,
  0x00}},
 {{0x67,0x4f,0x4f,0x8b,0x57,0x83,0x10,0x3e,    /* 0x09 - corrected 640x480-100 */
  0xe7,0x8d,0xdf,0xe6,0x11,0x00,0x00,0x05,
  0x00}},
 {{0x67,0x4f,0x4f,0x8b,0x57,0x83,0x10,0x3e,    /* 0x0a - corrected 640x480-120 */
  0xe7,0x8d,0xdf,0xe6,0x11,0x00,0x00,0x05,
  0x00}},
 {{0x63,0x4f,0x4f,0x87,0x56,0x9d,0xfb,0x1f,
  0xe0,0x83,0xdf,0xdf,0xfc,0x10,0x00,0x01,
  0x00}},
 {{0x65,0x4f,0x4f,0x89,0x57,0x9f,0xfb,0x1f,
  0xe6,0x8a,0xdf,0xdf,0xfc,0x10,0x00,0x01,    /* Corrected VDE, VBE */
  0x00}},
 {{0x7b,0x63,0x63,0x9f,0x6a,0x93,0x6f,0xf0,
  0x58,0x8a,0x57,0x57,0x70,0x20,0x00,0x05,
  0x01}},
 {{0x7f,0x63,0x63,0x83,0x6c,0x1c,0x72,0xf0,
  0x58,0x8c,0x57,0x57,0x73,0x20,0x00,0x06,
  0x01}},
 {{0x7d,0x63,0x63,0x81,0x6e,0x1d,0x98,0xf0,
  0x7c,0x82,0x57,0x57,0x99,0x00,0x00,0x06,
  0x01}},
 {{0x7f,0x63,0x63,0x83,0x69,0x13,0x6f,0xf0,
  0x58,0x8b,0x57,0x57,0x70,0x20,0x00,0x06,
  0x01}},
 {{0x7e,0x63,0x63,0x82,0x6b,0x13,0x75,0xf0,
  0x58,0x8b,0x57,0x57,0x76,0x20,0x00,0x06,
  0x01}},
 {{0x8c,0x63,0x63,0x87,0x72,0x16,0x7e,0xf0,
  0x59,0x8d,0x57,0x57,0x7f,0x00,0x00,0x06,
  0x01}},
 {{0x7e,0x63,0x63,0x82,0x6c,0x14,0x75,0xe0,
  0x58,0x0b,0x57,0x57,0x76,0x20,0x00,0x06,
  0x01}},
 {{0x7e,0x63,0x63,0x82,0x6c,0x14,0x75,0xe0,   /* 0x14 */
  0x58,0x0b,0x57,0x57,0x76,0x20,0x00,0x06,
  0x01}},
 {{0x99,0x7f,0x7f,0x9d,0x84,0x1a,0x96,0x1f,
  0x7f,0x83,0x7f,0x7f,0x97,0x10,0x00,0x02,
  0x00}},
 {{0xa3,0x7f,0x7f,0x87,0x86,0x97,0x24,0xf5,
  0x02,0x88,0xff,0xff,0x25,0x10,0x00,0x02,
  0x01}},
 {{0xa1,0x7f,0x7f,0x85,0x86,0x97,0x24,0xf5,
  0x02,0x88,0xff,0xff,0x25,0x10,0x00,0x02,
  0x01}},
 {{0x9f,0x7f,0x7f,0x83,0x85,0x91,0x1e,0xf5,
  0x00,0x83,0xff,0xff,0x1f,0x10,0x00,0x02,
  0x01}},
 {{0xa7,0x7f,0x7f,0x8b,0x89,0x95,0x26,0xf5,
  0x00,0x83,0xff,0xff,0x27,0x10,0x00,0x02,
  0x01}},
 {{0x9f,0x7f,0x7f,0x83,0x83,0x93,0x1e,0xf5,  /* 0x1a */
  0x00,0x84,0xff,0xff,0x1f,0x10,0x00,0x02,
  0x01}},
 {{0xa2,0x7f,0x7f,0x86,0x84,0x94,0x37,0xf5,
  0x0b,0x82,0xff,0xff,0x38,0x10,0x00,0x02,
  0x01}},
 {{0xcf,0x9f,0x9f,0x93,0xb2,0x01,0x14,0xba,
  0x00,0x83,0xff,0xff,0x15,0x00,0x00,0x03,
  0x00}},
 {{0xce,0x9f,0x9f,0x92,0xa9,0x17,0x28,0x5a,
  0x00,0x83,0xff,0xff,0x29,0x09,0x00,0x07,
  0x01}},
 {{0xce,0x9f,0x9f,0x92,0xa5,0x17,0x28,0x5a,  /* 0x1e */
  0x00,0x83,0xff,0xff,0x29,0x09,0x00,0x07,
  0x01}},
 {{0xd3,0x9f,0x9f,0x97,0xab,0x1f,0x2e,0x5a,
  0x00,0x83,0xff,0xff,0x2f,0x09,0x00,0x07,
  0x01}},
 {{0x09,0xc7,0xc7,0x8d,0xd3,0x0b,0xe0,0x10,
  0xb0,0x83,0xaf,0xaf,0xe1,0x2f,0x01,0x04,
  0x00}},
 {{0x09,0xc7,0xc7,0x8d,0xd3,0x0b,0xe0,0x10,
  0xb0,0x83,0xaf,0xaf,0xe1,0x2f,0x01,0x04,
  0x00}},
 {{0x09,0xc7,0xc7,0x8d,0xd3,0x0b,0xe0,0x10,
  0xb0,0x83,0xaf,0xaf,0xe1,0x2f,0x01,0x04,
  0x00}},
 {{0x09,0xc7,0xc7,0x8d,0xd3,0x0b,0xe0,0x10,
  0xb0,0x83,0xaf,0xaf,0xe1,0x2f,0x01,0x04,
  0x00}},
 {{0x09,0xc7,0xc7,0x8d,0xd3,0x0b,0xe0,0x10,  /* 36: 1600x1200x85Hz */
  0xb0,0x83,0xaf,0xaf,0xe1,0x2f,0x01,0x04,
  0x00}},
 {{0x3f,0xef,0xef,0x83,0xfd,0x1a,0xda,0x1f,  /* 37: 1920x1440x60Hz */
  0xa0,0x84,0x9f,0x9f,0xdb,0x1f,0x01,0x01,
  0x00}},
 {{0x55,0xff,0xff,0x99,0x0d,0x0c,0x3e,0xba,
  0x00,0x84,0xff,0xff,0x3f,0x0f,0x41,0x05,
  0x00}},
 {{0xdc,0x9f,0x9f,0x80,0xaf,0x9d,0xe6,0xff,  /* 0x27: 1280x960-60 - correct */
  0xc0,0x83,0xbf,0xbf,0xe7,0x10,0x00,0x07,
  0x01}},
 {{0x7f,0x63,0x63,0x83,0x6c,0x1c,0x72,0xba,  /* 0x28 */
  0x27,0x8b,0xdf,0xdf,0x73,0x00,0x00,0x06,
  0x01}},
 {{0x7f,0x63,0x63,0x83,0x69,0x13,0x6f,0xba,
  0x26,0x89,0xdf,0xdf,0x6f,0x00,0x00,0x06,
  0x01}},
 {{0x7f,0x63,0x63,0x82,0x6b,0x13,0x75,0xba,
  0x29,0x8c,0xdf,0xdf,0x75,0x00,0x00,0x06,
  0x01}},
 {{0xa3,0x7f,0x7f,0x87,0x86,0x97,0x24,0xf1,
  0xaf,0x85,0x3f,0x3f,0x25,0x30,0x00,0x02,
  0x01}},
 {{0x9f,0x7f,0x7f,0x83,0x85,0x91,0x1e,0xf1,
  0xad,0x81,0x3f,0x3f,0x1f,0x30,0x00,0x02,
  0x01}},
 {{0xa7,0x7f,0x7f,0x88,0x89,0x15,0x26,0xf1,
  0xb1,0x85,0x3f,0x3f,0x27,0x30,0x00,0x02,
  0x01}},
 {{0xce,0x9f,0x9f,0x92,0xa9,0x17,0x28,0xc4,
  0x7a,0x8e,0xcf,0xcf,0x29,0x21,0x00,0x07,
  0x01}},
 {{0xce,0x9f,0x9f,0x92,0xa5,0x17,0x28,0xd4,
  0x7a,0x8e,0xcf,0xcf,0x29,0x21,0x00,0x07,
  0x01}},
 {{0xd3,0x9f,0x9f,0x97,0xab,0x1f,0x2e,0xd4,
  0x7d,0x81,0xcf,0xcf,0x2f,0x21,0x00,0x07,
  0x01}},
 {{0x6b,0x59,0x59,0x8f,0x5e,0x8c,0x0b,0x3e,
  0xe9,0x8b,0xdf,0xe7,0x04,0x00,0x00,0x05,
  0x00}},
 {{0x6d,0x59,0x59,0x91,0x60,0x89,0x53,0xf0,  /* 0x32: 720x576, corrected to 60Hz */
  0x41,0x84,0x3f,0x3f,0x54,0x00,0x00,0x05,
  0x41}},
 {{0xa3,0x7f,0x7f,0x87,0x86,0x97,0x1e,0xf1,  /* 0x33 - 1024x600 */
  0xae,0x85,0x57,0x57,0x1f,0x30,0x00,0x02,
  0x01}},
 {{0xa3,0x8f,0x8f,0x97,0x96,0x97,0x24,0xf5,  /* 0x34 - 1152x768 - corrected */
  0x02,0x88,0xff,0xff,0x25,0x10,0x00,0x02,
  0x01}},
 {{0x7f,0x63,0x63,0x83,0x6c,0x1c,0x72,0xba,  /* 0x35 */
   0x27,0x8b,0xdf,0xdf,0x73,0x00,0x00,0x06,
   0x01}}, /* 0x35 */
 {{0x7f,0x63,0x63,0x83,0x69,0x13,0x6f,0xba,
   0x26,0x89,0xdf,0xdf,0x6f,0x00,0x00,0x06,
   0x01}}, /* 0x36 */
 {{0x7f,0x63,0x63,0x82,0x6b,0x13,0x75,0xba,
   0x29,0x8c,0xdf,0xdf,0x75,0x00,0x00,0x06,
   0x01}}, /* 0x37 */
 {{0xa3,0x7f,0x7f,0x87,0x86,0x97,0x24,0xf1,
   0xaf,0x85,0x3f,0x3f,0x25,0x30,0x00,0x02,
   0x01}}, /* 0x38 */
 {{0x9f,0x7f,0x7f,0x83,0x85,0x91,0x1e,0xf1,
   0xad,0x81,0x3f,0x3f,0x1f,0x30,0x00,0x02,
   0x01}}, /* 0x39 */
 {{0xa7,0x7f,0x7f,0x88,0x89,0x95,0x26,0xf1,  /* 95 was 15 - illegal HBE! */
   0xb1,0x85,0x3f,0x3f,0x27,0x30,0x00,0x02,
   0x01}}, /* 0x3a */
 {{0xce,0x9f,0x9f,0x92,0xa9,0x17,0x28,0xc4,
   0x7a,0x8e,0xcf,0xcf,0x29,0x21,0x00,0x07,
   0x01}}, /* 0x3b */
 {{0xce,0x9f,0x9f,0x92,0xa5,0x17,0x28,0xd4,
   0x7a,0x8e,0xcf,0xcf,0x29,0x21,0x00,0x07,
   0x01}}, /* 0x3c */
 {{0xd3,0x9f,0x9f,0x97,0xab,0x1f,0x2e,0xd4,
   0x7d,0x81,0xcf,0xcf,0x2f,0x21,0x00,0x07,
   0x01}}, /* 0x3d */
 {{0xc3,0x8f,0x8f,0x87,0x9b,0x0b,0x82,0xef,  /* 1152x864-75 */
   0x60,0x83,0x5f,0x5f,0x83,0x10,0x00,0x07,
   0x01}},  /* 0x3e */
 {{0x86,0x69,0x69,0x8A,0x74,0x06,0x8C,0x15,  /* 848x480-38i */
   0x4F,0x83,0xEF,0xEF,0x8D,0x30,0x00,0x02,
   0x00}}, /* 0x3f */
 {{0x83,0x69,0x69,0x87,0x6f,0x1d,0x03,0x3E,  /* 848x480-60  */
   0xE5,0x8d,0xDF,0xe4,0x04,0x00,0x00,0x06,
   0x00}}, /* 0x40 */
 {{0x86,0x6A,0x6A,0x8A,0x74,0x06,0x8C,0x15,  /* 856x480-38i */
   0x4F,0x83,0xEF,0xEF,0x8D,0x30,0x00,0x02,
   0x00}}, /* 0x41 */
 {{0x81,0x6A,0x6A,0x85,0x70,0x00,0x0F,0x3E,  /* 856x480-60  */
   0xEB,0x8E,0xDF,0xDF,0x10,0x00,0x00,0x02,
   0x00}}, /* 0x42 */
 {{0xdd,0xa9,0xa9,0x81,0xb4,0x97,0x26,0xfd,  /* 1360x768-60 */
   0x01,0x8d,0xff,0x00,0x27,0x10,0x00,0x03,
   0x01}}, /* 0x43 */
 {{0xd9,0x8f,0x8f,0x9d,0xba,0x0a,0x8a,0xff,  /* 1152x864-84 */
   0x60,0x8b,0x5f,0x5f,0x8b,0x10,0x00,0x03,
   0x01}}, /* 0x44 */
 {{0xd3,0x9f,0x9f,0x97,0xab,0x1f,0xf1,0xff,  /* 1280x960-85 */
   0xc0,0x83,0xbf,0xbf,0xf2,0x10,0x00,0x07,
   0x01}}, /* 0x45 */
 {{0xce,0x9f,0x9f,0x92,0xa9,0x17,0x20,0xf5,  /* 1280x768-60 */
   0x03,0x88,0xff,0xff,0x21,0x10,0x00,0x07,
   0x01}}, /* 0x46 */
 {{0x75,0x5f,0x5f,0x99,0x66,0x90,0x53,0xf0,  /* 768x576, corrected to 60Hz */
   0x41,0x84,0x3f,0x3f,0x54,0x00,0x00,0x05,
   0x41}}, /* 0x47 */
 {{0xce,0xa9,0xa9,0x92,0xb1,0x07,0x28,0x52,  /* 1360x1024 (Barco iQ Pro R300) */
   0x02,0x8e,0xff,0x00,0x29,0x0d,0x00,0x03,
   0x00}}, /* 0x48 */
 {{0xcd,0x8f,0x8f,0x91,0x9b,0x1b,0x7a,0xff,  /* 1152x864-60 */
   0x64,0x8c,0x5f,0x62,0x7b,0x10,0x00,0x07,
   0x41}}, /* 0x49 */
 {{0x5c,0x4f,0x4f,0x80,0x57,0x80,0xa3,0x1f, /* fake 640x400@60Hz (for LCD and TV, not actually used) */
   0x98,0x8c,0x8f,0x96,0xa4,0x30,0x00,0x05,
   0x40}}, /* 0x4a */
 {{0x2c,0x27,0x27,0x90,0x2d,0x92,0xa4,0x1f, /* fake 320x200@60Hz (for LCD and TV, not actually used) */
   0x98,0x8c,0x8f,0x96,0xa5,0x30,0x00,0x04,
   0x00}}  /* 0x4b */
};

static const struct SiS_MCLKData SiS300_MCLKData_630[] =
{
	{ 0x5a,0x64,0x80, 66},
	{ 0xb3,0x45,0x80, 83},
	{ 0x37,0x61,0x80,100},
	{ 0x37,0x22,0x80,133},
	{ 0x37,0x61,0x80,100},
	{ 0x37,0x61,0x80,100},
	{ 0x37,0x61,0x80,100},
	{ 0x37,0x61,0x80,100}
};

static const struct SiS_MCLKData SiS300_MCLKData_300[] =
{
	{ 0x68,0x43,0x80,125},
	{ 0x68,0x43,0x80,125},
	{ 0x68,0x43,0x80,125},
	{ 0x37,0x61,0x80,100},
	{ 0x37,0x61,0x80,100},
	{ 0x37,0x61,0x80,100},
	{ 0x37,0x61,0x80,100},
	{ 0x37,0x61,0x80,100}
};

static struct SiS_VCLKData SiS300_VCLKData[] =
{
	{ 0x1b,0xe1, 25}, /* 0x00 */
	{ 0x4e,0xe4, 28}, /* 0x01 */
	{ 0x57,0xe4, 32}, /* 0x02 */
	{ 0xc3,0xc8, 36}, /* 0x03 */
	{ 0x42,0xc3, 40}, /* 0x04 */
	{ 0x5d,0xc4, 45}, /* 0x05 */
	{ 0x52,0x65, 50}, /* 0x06 */
	{ 0x53,0x65, 50}, /* 0x07 */
	{ 0x6d,0x66, 56}, /* 0x08 */
	{ 0x5a,0x64, 65}, /* 0x09 */
	{ 0x46,0x44, 68}, /* 0x0a */
	{ 0x3e,0x43, 75}, /* 0x0b */
	{ 0x6d,0x46, 76}, /* 0x0c */  /* 800x600 | LVDS_2(CH), MITAC(CH);  - 730, A901(301B): 0xb1,0x46, 76 */
	{ 0x41,0x43, 79}, /* 0x0d */
	{ 0x31,0x42, 79}, /* 0x0e */
	{ 0x46,0x25, 85}, /* 0x0f */
	{ 0x78,0x29, 87}, /* 0x10 */
	{ 0x62,0x44, 95}, /* 0x11 */
	{ 0x2b,0x22,105}, /* 0x12 */
	{ 0x49,0x24,106}, /* 0x13 */
	{ 0xc3,0x28,108}, /* 0x14 */
	{ 0x3c,0x23,109}, /* 0x15 */
	{ 0xf7,0x2c,132}, /* 0x16 */
	{ 0xd4,0x28,136}, /* 0x17 */
	{ 0x41,0x05,158}, /* 0x18 */
	{ 0x43,0x05,162}, /* 0x19 */
	{ 0xe1,0x0f,175}, /* 0x1a */
	{ 0xfc,0x12,189}, /* 0x1b */
	{ 0xde,0x26,194}, /* 0x1c */
	{ 0x54,0x05,203}, /* 0x1d */
	{ 0x3f,0x03,230}, /* 0x1e */
	{ 0x30,0x02,234}, /* 0x1f */
	{ 0x24,0x01,266}, /* 0x20 */
	{ 0x52,0x2a, 54}, /* 0x21 */  /* 301 TV */
	{ 0x52,0x6a, 27}, /* 0x22 */  /* 301 TV */
	{ 0x62,0x24, 70}, /* 0x23 */  /* 301 TV */
	{ 0x62,0x64, 70}, /* 0x24 */  /* 301 TV */
	{ 0xa8,0x4c, 30}, /* 0x25 */  /* 301 TV */
	{ 0x20,0x26, 33}, /* 0x26 */  /* 301 TV */
	{ 0x31,0xc2, 39}, /* 0x27 */
	{ 0xbf,0xc8, 35}, /* 0x28 */  /* 856x480 */
	{ 0x60,0x36, 30}, /* 0x29 */  /* CH/UNTSC TEXT | LVDS_2(CH) - 730, A901(301B), Mitac(CH): 0xe0, 0xb6, 30 */
	{ 0x40,0x4a, 28}, /* 0x2a */  /* CH-TV */
	{ 0x9f,0x46, 44}, /* 0x2b */  /* CH-TV */
	{ 0x97,0x2c, 26}, /* 0x2c */  /* CH-TV */
	{ 0x44,0xe4, 25}, /* 0x2d */  /* CH-TV */
	{ 0x7e,0x32, 47}, /* 0x2e */  /* CH-TV */
	{ 0x8a,0x24, 31}, /* 0x2f */  /* CH/PAL TEXT | LVDS_2(CH), Mitac(CH) -  730, A901(301B): 0x57, 0xe4, 31 */
	{ 0x97,0x2c, 26}, /* 0x30 */  /* CH-TV */
	{ 0xce,0x3c, 39}, /* 0x31 */  /* CH-TV */
	{ 0x52,0x4a, 36}, /* 0x32 */  /* CH/PAL 800x600 5/6 */
	{ 0x34,0x61, 95}, /* 0x33 */
	{ 0x78,0x27,108}, /* 0x34 */  /* Replacement for index 0x14 for 630 (?) */
	{ 0x70,0x28, 90}, /* 0x35 */  /* 1152x864@60 */
	{ 0x45,0x6b, 21}, /* 0x36 */  /* Chrontel SuperOverscan */
	{ 0x52,0xe2, 49}, /* 0x37 */  /* 16:9 modes  */
	{ 0x2b,0x61, 78}, /* 0x38 */  /* 16:9 modes  */
	{ 0x70,0x44,108}, /* 0x39 */  /* 16:9 modes  */
	{ 0x54,0x42,135}, /* 0x3a */  /* 16:9 modes  */
	{ 0x41,0x22,157}, /* 0x3b */  /* 16:9 modes  */
	{ 0x52,0x07,149}, /* 0x3c */  /* 1280x960-85 */
	{ 0x62,0xc6, 34}, /* 0x3d */  /* 848x480-60  */
	{ 0x30,0x23, 88}, /* 0x3e */  /* 1360x768-60 */
        { 0x70,0x29, 81}, /* 0x3f */  /* 1280x768-60 */
	{ 0x72,0x2a, 76}, /* 0x40 */  /* test for SiS730 --- LIMIT for table (&0x3f) */
	{ 0x15,0x21, 79}, /* 0x41 */  /* test for SiS730 */
	{ 0xa1,0x42,108}, /* 0x42 */  /* 1280x960 LCD */
	{ 0x37,0x61,100}, /* 0x43 */  /* 1280x960 LCD */
	{ 0xe3,0x9a,106}, /* 0x44 */  /* 1360x1024 - special for Barco iQ R300 */
	{ 0xe2,0x46,135}, /* 0x45 */  /* 1280x1024-75, better clock for VGA2 */
	{ 0x70,0x29, 81}, /* 0x46 */  /* unused */
	{    0,   0,  0}, /* 0x47 custom (will be filled out) */
	{ 0xce,0x25,189}, /* 0x48 */  /* Replacement for index 0x1b for 730 (and 540?) */
	{ 0x15,0xe1, 20}, /* 0x49 */  /* 640x400@60 (fake, not actually used) */
	{ 0x5f,0xc6, 33}, /* 0x4a */  /* 720x576@60 */
	{ 0x37,0x5a, 10}, /* 0x4b */  /* 320x200@60 (fake, not actually used) */
	{ 0x2b,0xc2, 35}  /* 0x4c */  /* 768@576@60 */
};

static const unsigned char SiS300_SR15[4 * 8] =
{
	0x01,0x09,0xa3,0x00,
	0x43,0x43,0x43,0x00,
	0x1e,0x1e,0x1e,0x00,
	0x2a,0x2a,0x2a,0x00,
	0x06,0x06,0x06,0x00,
	0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00
};

static const struct SiS_PanelDelayTbl SiS300_PanelDelayTbl[] =
{
	{{0x05,0xaa}},
	{{0x05,0x14}},
	{{0x05,0x36}},
	{{0x05,0x14}},
	{{0x05,0x14}},
	{{0x05,0x14}},
	{{0x05,0x90}},
	{{0x05,0x90}},
	{{0x05,0x14}},
	{{0x05,0x14}},
	{{0x05,0x14}},
	{{0x05,0x14}},
	{{0x20,0x80}},
	{{0x05,0x14}},
	{{0x05,0x40}},
	{{0x05,0x60}}
};

/**************************************************************/
/* SIS VIDEO BRIDGE ----------------------------------------- */
/**************************************************************/

static const struct SiS_LCDData SiS300_St2LCD1024x768Data[] =
{
	{   62,  25, 800, 546,1344, 806},
	{   32,  15, 930, 546,1344, 806},
	{   32,  15, 930, 546,1344, 806},
	{  104,  45, 945, 496,1344, 806},
	{   62,  25, 800, 546,1344, 806},
	{   31,  18,1008, 624,1344, 806},
	{    1,   1,1344, 806,1344, 806}
};

static const struct SiS_LCDData SiS300_ExtLCD1024x768Data[] =
{
	{   12,   5, 896, 512,1344, 806},
	{   12,   5, 896, 510,1344, 806},
	{   32,  15,1008, 505,1344, 806},
	{   32,  15,1008, 514,1344, 806},
	{   12,   5, 896, 500,1344, 806},
	{   42,  25,1024, 625,1344, 806},
	{    1,   1,1344, 806,1344, 806},
	{   12,   5, 896, 500,1344, 806},
	{   42,  25,1024, 625,1344, 806},
	{    1,   1,1344, 806,1344, 806},
	{   12,   5, 896, 500,1344, 806},
	{   42,  25,1024, 625,1344, 806},
	{    1,   1,1344, 806,1344, 806}
};

static const struct SiS_LCDData SiS300_St2LCD1280x1024Data[] =
{
	{   22,   5, 800, 510,1650,1088},
	{   22,   5, 800, 510,1650,1088},
	{  176,  45, 900, 510,1650,1088},
	{  176,  45, 900, 510,1650,1088},
	{   22,   5, 800, 510,1650,1088},
	{   13,   5,1024, 675,1560,1152},
	{   16,   9,1266, 804,1688,1072},
	{    1,   1,1688,1066,1688,1066}
};

static const struct SiS_LCDData SiS300_ExtLCD1280x1024Data[] =
{
	{  211,  60,1024, 501,1688,1066},
	{  211,  60,1024, 508,1688,1066},
	{  211,  60,1024, 501,1688,1066},
	{  211,  60,1024, 508,1688,1066},
	{  211,  60,1024, 500,1688,1066},
	{  211,  75,1024, 625,1688,1066},
	{  211, 120,1280, 798,1688,1066},
	{    1,   1,1688,1066,1688,1066}
};

static const struct SiS_Part2PortTbl SiS300_CRT2Part2_1024x768_1[] =
{ /* VESA Timing */
	{{0x21,0x12,0xbf,0xe4,0xc0,0x21,0x45,0x09,0x00,0xa9,0x09,0x04}},
	{{0x2c,0x12,0x9a,0xae,0x88,0x21,0x45,0x09,0x00,0xa9,0x09,0x04}},
	{{0x21,0x12,0xbf,0xe4,0xc0,0x21,0x45,0x09,0x00,0xa9,0x09,0x04}},
	{{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00}},
	{{0x22,0x13,0xfe,0x25,0xff,0x21,0x45,0x0a,0x00,0xa9,0x0d,0x04}},
	{{0x22,0x13,0xfe,0x25,0xff,0x21,0x45,0x0a,0x00,0xa9,0x0d,0x04}},
	{{0x22,0x13,0xfe,0x25,0xff,0x21,0x45,0x0a,0x00,0xa9,0x0d,0x04}}
};

static const struct SiS_Part2PortTbl SiS300_CRT2Part2_1024x768_2[] =
{  /* Non-VESA */
	{{0x28,0x12,0xa3,0xd0,0xaa,0x5a,0x45,0x0a,0x07,0xfa,0x0a,0x24}},
	{{0x2c,0x12,0x9a,0xae,0x88,0x5a,0x45,0x0a,0x07,0xfa,0x0a,0x24}},
	{{0x28,0x12,0xa3,0xd0,0xaa,0x5a,0x45,0x0a,0x07,0xfa,0x0a,0x24}},
	{{0x2c,0x12,0x9a,0xae,0x88,0x5a,0x45,0x0a,0x07,0xfa,0x0a,0x24}},
	{{0x28,0x13,0xe7,0x0b,0xe8,0x5a,0x45,0x0a,0x07,0xfa,0x0a,0x24}},
	{{0x38,0x18,0x16,0x00,0x00,0x5a,0x45,0x0a,0x07,0xfa,0x0a,0x24}},
	{{0x36,0x13,0x13,0x25,0xff,0x5a,0x45,0x0a,0x07,0xfa,0x0a,0x24}}
};

static const struct SiS_Part2PortTbl SiS300_CRT2Part2_1024x768_3[] =
{
	{{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00}}
};

/**************************************************************/
/* LVDS/Chrontel -------------------------------------------- */
/**************************************************************/

/* Custom data for Barco iQ R series */
static const struct SiS_LVDSData SiS300_LVDSBARCO1366Data_1[]=
{
	{ 832, 438,1331, 806},
	{ 832, 388,1331, 806},
	{ 832, 438,1331, 806},
	{ 832, 388,1331, 806},
	{ 832, 518,1331, 806},
	{1050, 638,1344, 806},
	{1344, 806,1344, 806},
	{1688,1066,1688,1066},
	{1688,1066,1688,1066}   /* 1360x1024 */
};

/* Custom data for Barco iQ R series */
static const struct SiS_LVDSData SiS300_LVDSBARCO1366Data_2[]=
{
	{1344, 806,1344, 806},
	{1344, 806,1344, 806},
	{1344, 806,1344, 806},
	{1344, 806,1344, 806},
	{1344, 806,1344, 806},
	{1344, 806,1344, 806},
	{1344, 806,1344, 806},
	{1688,1066,1688,1066},
	{1688,1066,1688,1066}   /* 1360x1024 */
};

/* Custom data for Barco iQ G series */
static const struct SiS_LVDSData SiS300_LVDSBARCO1024Data_1[]=
{
	{ 832, 438,1331, 806},
	{ 832, 409,1331, 806},
	{ 832, 438,1331, 806},
	{ 832, 409,1331, 806},
	{ 832, 518,1331, 806},   /* 640x480 */
	{1050, 638,1344, 806},   /* 800x600 */
	{1344, 806,1344, 806},   /* 1024x768 */
};

/* Custom data for 848x480 and 856x480 parallel LVDS panels */
static const struct SiS_LVDSData SiS300_LVDS848x480Data_1[]=
{
	{   0,   0,   0,   0},
	{   0,   0,   0,   0},
	{   0,   0,   0,   0},
	{   0,   0,   0,   0},
	{1088, 525,1088, 525},  /* 640x480 TODO */
	{1088, 525,1088, 525},  /* 800x600 TODO */
	{1088, 525,1088, 525},  /* 1024x768 TODO */
	{   0,   0,   0,   0},
	{   0,   0,   0,   0},
	{   0,   0,   0,   0},
	{   0,   0,   0,   0},
	{1088, 525,1088, 525},  /* 848x480 */
	{1088, 525,1088, 525},  /* 856x480 */
	{1088, 525,1088, 525}   /* 1360x768 TODO */
};

/* Custom data for 848x480 parallel panel */
static const struct SiS_LVDSData SiS300_LVDS848x480Data_2[]=
{
	{   0,   0,   0,   0},
	{   0,   0,   0,   0},
	{   0,   0,   0,   0},
	{   0,   0,   0,   0},
	{1088, 525,1088, 525},  /*  640x480 */
	{1088, 525,1088, 525},  /*  800x600 */
	{1088, 525,1088, 525},  /* 1024x768 */
	{   0,   0,   0,   0},
	{   0,   0,   0,   0},
	{   0,   0,   0,   0},
	{   0,   0,   0,   0},
	{1088, 525,1088, 525},  /* 848x480 */
	{1088, 525,1088, 525},  /* 856x480 */
	{1088, 525,1088, 525}	/* 1360x768 TODO */
};

static const struct SiS_LVDSData SiS300_CHTVUPALData[] =
{
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{ 840, 750, 840, 750},
	{ 936, 836, 936, 836}
};

static const struct SiS_LVDSData SiS300_CHTVOPALData[] =
{
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{ 840, 625, 840, 625},
	{ 960, 750, 960, 750}
};

static const struct SiS_LVDSData SiS300_CHTVSOPALData[] =
{
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{ 840, 500, 840, 500},
	{ 944, 625, 944, 625}
};

/* Custom des data for Barco iQ R200/300/400 (BIOS 2.00.07) */
static const struct SiS_LVDSDes SiS300_PanelType04_1a[] =	/* 1280x1024 (1366x1024) */
{
	{1330, 798},  /* 320x200 */
	{1330, 794},
	{1330, 798},
	{1330, 794},
	{1330,   0},  /* 640x480 / 320x240  */
	{1343,   0},  /* 800x600 / 400x300  */
	{   0, 805},  /* 1024x768 / 512x384 */
	{1688,1066},  /* 1280x1024          */
	{   0,   0}   /* 1360x1024          */
};

static const struct SiS_LVDSDes SiS300_PanelType04_2a[] =
{
	{1152, 622},
	{1152, 597},
	{1152, 622},
	{1152, 597},
	{1152, 662},
	{1232, 722},
	{   0, 805},
	{1688,1066},
	{   0,   0}
};

/* Custom des data for Barco iQ G200/300/400 (BIOS 2.00.07) */
static const struct SiS_LVDSDes SiS300_PanelType04_1b[] =	/* 1024x768 */
{
	{1330, 798},  /* 320x200 */
	{1330, 794},
	{1330, 798},
	{1330, 794},
	{1330,   0},  /* 640x480 / 320x240  */
	{1343,   0},  /* 800x600 / 400x300  */
	{   0, 805}   /* 1024x768 / 512x384 */
};

static const struct SiS_LVDSDes SiS300_PanelType04_2b[] =
{
	{1152, 622},
	{1152, 597},
	{1152, 622},
	{1152, 597},
	{1152, 662},
	{1232, 722},
	{   0, 805}
};

/* CRT1 CRTC for slave modes */

static const struct SiS_LVDSCRT1Data SiS300_CHTVCRT1UNTSC[] =
{
	{{0x64,0x4f,0x88,0x56,0x9f,0x56,0x3e,
	  0xe8,0x84,0x8f,0x57,0x20,0x00,0x01,
	  0x00 }},
	{{0x64,0x4f,0x88,0x56,0x9f,0x56,0x3e,
	  0xd0,0x82,0x5d,0x57,0x00,0x00,0x01,
	  0x00 }},
	{{0x64,0x4f,0x88,0x56,0x9f,0x56,0x3e,
	  0xe8,0x84,0x8f,0x57,0x20,0x00,0x01,
	  0x00 }},
	{{0x64,0x4f,0x88,0x56,0x9f,0x56,0x3e,
	  0xd0,0x82,0x5d,0x57,0x00,0x00,0x01,
	  0x00 }},
	{{0x5d,0x4f,0x81,0x53,0x9c,0x56,0xba,
	  0x18,0x84,0xdf,0x57,0x00,0x00,0x01,
	  0x00 }},
	{{0x80,0x63,0x84,0x6c,0x17,0xec,0xf0,
	  0x90,0x8c,0x57,0xed,0x20,0x00,0x06,
	  0x01 }}
};

static const struct SiS_LVDSCRT1Data SiS300_CHTVCRT1ONTSC[] =
{
	{{0x64,0x4f,0x88,0x5a,0x9f,0x0b,0x3e,
	  0xc0,0x84,0x8f,0x0c,0x20,0x00,0x01,
	  0x00 }},
	{{0x64,0x4f,0x88,0x5a,0x9f,0x0b,0x3e,
	  0xb0,0x8d,0x5d,0x0c,0x00,0x00,0x01,
	  0x00 }},
	{{0x64,0x4f,0x88,0x5a,0x9f,0x0b,0x3e,
	  0xc0,0x84,0x8f,0x0c,0x20,0x00,0x01,
	  0x00 }},
	{{0x64,0x4f,0x88,0x5a,0x9f,0x0b,0x3e,
	  0xb0,0x8d,0x5d,0x0c,0x00,0x00,0x01,
	  0x00 }},
	{{0x5d,0x4f,0x81,0x56,0x9c,0x0b,0x3e,
	  0xe8,0x84,0xdf,0x0c,0x00,0x00,0x01,
	  0x00 }},
	{{0x7d,0x63,0x81,0x6a,0x16,0xba,0xf0,
	  0x7f,0x86,0x57,0xbb,0x00,0x00,0x06,
	  0x01 }}
};

static const struct SiS_LVDSCRT1Data SiS300_CHTVCRT1UPAL[] =
{
	{{0x79,0x4f,0x9d,0x5a,0x90,0x6f,0x3e,
	  0xf8,0x83,0x8f,0x70,0x20,0x00,0x05,
	  0x00 }},
	{{0x79,0x4f,0x9d,0x5a,0x90,0x6f,0x3e,
	  0xde,0x81,0x5d,0x70,0x00,0x00,0x05,
	  0x00 }},
	{{0x79,0x4f,0x9d,0x5a,0x90,0x6f,0x3e,
	  0xf8,0x83,0x8f,0x70,0x20,0x00,0x05,
	  0x00 }},
	{{0x79,0x4f,0x9d,0x5a,0x90,0x6f,0x3e,
	  0xde,0x81,0x5d,0x70,0x00,0x00,0x05,
	  0x00 }},
	{{0x64,0x4f,0x88,0x55,0x80,0xec,0xba,
	  0x50,0x84,0xdf,0xed,0x00,0x00,0x05,
	  0x00 }},
	{{0x70,0x63,0x94,0x68,0x8d,0x42,0xf1,
	  0xc8,0x8c,0x57,0xe9,0x20,0x00,0x05,
	  0x01 }}
};

static const struct SiS_LVDSCRT1Data SiS300_CHTVCRT1OPAL[] =
{
	{{0x79,0x4f,0x9d,0x5a,0x90,0x6f,0x3e,
	  0xf0,0x83,0x8f,0x70,0x20,0x00,0x05,
	  0x00 }},
	{{0x79,0x4f,0x9d,0x5a,0x90,0x6f,0x3e,
	  0xde,0x81,0x5d,0x70,0x00,0x00,0x05,
	  0x00 }},
	{{0x79,0x4f,0x9d,0x5a,0x90,0x6f,0x3e,
	  0xf0,0x83,0x8f,0x70,0x20,0x00,0x05,
	  0x00 }},
	{{0x79,0x4f,0x9d,0x5a,0x90,0x6f,0x3e,
	  0xde,0x81,0x5d,0x70,0x00,0x00,0x05,
	  0x00 }},
	{{0x64,0x4f,0x88,0x55,0x80,0x6f,0xba,
	  0x20,0x83,0xdf,0x70,0x00,0x00,0x05,
	  0x00 }},
	{{0x73,0x63,0x97,0x69,0x8e,0xec,0xf0,
	  0x90,0x8c,0x57,0xed,0x20,0x00,0x05,
	  0x01 }}
};

static const struct SiS_LVDSCRT1Data SiS300_CHTVCRT1SOPAL[] =
{
	{{0x79,0x4f,0x9d,0x5a,0x90,0x6f,0x3e,
	  0xf0,0x83,0x8f,0x70,0x20,0x00,0x05,
	  0x00 }},
	{{0x79,0x4f,0x9d,0x5a,0x90,0x6f,0x3e,
	  0xde,0x81,0x5d,0x70,0x00,0x00,0x05,
	  0x00 }},
	{{0x79,0x4f,0x9d,0x5a,0x90,0x6f,0x3e,
	  0xf0,0x83,0x8f,0x70,0x20,0x00,0x05,
	  0x00 }},
	{{0x79,0x4f,0x9d,0x5a,0x90,0x6f,0x3e,
	  0xde,0x81,0x5d,0x70,0x00,0x00,0x05,
	  0x00 }},
	{{0x64,0x4f,0x88,0x55,0x80,0x6f,0xba,  /* TODO */
	  0x20,0x83,0xdf,0x70,0x00,0x00,0x05,
	  0x00 }},
	{{0x73,0x63,0x97,0x69,0x8e,0xec,0xf0,  /* TODO */
	  0x90,0x8c,0x57,0xed,0x20,0x00,0x05,
	  0x01 }}
};

static const struct SiS_CHTVRegData SiS300_CHTVReg_UNTSC[] =
{
	{{0x4a,0x94,0x00,0x48,0xfe,0,0,0,0,0,0,0,0,0,0,0}},
	{{0x4a,0x94,0x00,0x48,0xfe,0,0,0,0,0,0,0,0,0,0,0}},
	{{0x4a,0x94,0x00,0x48,0xfe,0,0,0,0,0,0,0,0,0,0,0}},
	{{0x4a,0x94,0x00,0x48,0xfe,0,0,0,0,0,0,0,0,0,0,0}},
	{{0x6a,0x6a,0x00,0x2d,0xfa,0,0,0,0,0,0,0,0,0,0,0}}, /* Mode 17: 640x480 NTSC 7/8  */
	{{0x8d,0xc4,0x00,0x3b,0xfb,0,0,0,0,0,0,0,0,0,0,0}}  /* Mode 24: 800x600 NTSC 7/10 */
};

static const struct SiS_CHTVRegData SiS300_CHTVReg_ONTSC[] =
{
	{{0x49,0x94,0x00,0x34,0xfe,0,0,0,0,0,0,0,0,0,0,0}},
	{{0x49,0x94,0x00,0x34,0xfe,0,0,0,0,0,0,0,0,0,0,0}},
	{{0x49,0x94,0x00,0x34,0xfe,0,0,0,0,0,0,0,0,0,0,0}},
	{{0x49,0x94,0x00,0x34,0xfe,0,0,0,0,0,0,0,0,0,0,0}},
	{{0x69,0x6a,0x00,0x1e,0xfd,0,0,0,0,0,0,0,0,0,0,0}}, /* Mode 16: 640x480 NTSC 1/1 */
	{{0x8c,0xb4,0x00,0x32,0xf9,0,0,0,0,0,0,0,0,0,0,0}}  /* Mode 23: 800x600 NTSC 3/4 */
};

static const struct SiS_CHTVRegData SiS300_CHTVReg_UPAL[] =
{
	{{0x41,0x12,0x01,0x50,0x34,0,0,0,0,0,0,0,0,0,0,0}},
	{{0x41,0x12,0x00,0x50,0x00,0,0,0,0,0,0,0,0,0,0,0}},
	{{0x41,0x12,0x01,0x50,0x34,0,0,0,0,0,0,0,0,0,0,0}},
	{{0x41,0x12,0x00,0x50,0x00,0,0,0,0,0,0,0,0,0,0,0}},
	{{0x63,0x94,0x01,0x50,0x30,0,0,0,0,0,0,0,0,0,0,0}}, /* Mode 15: 640x480 PAL 5/6 */
	{{0x84,0x64,0x01,0x4e,0x2f,0,0,0,0,0,0,0,0,0,0,0}}  /* Mode 21: 800x600 PAL 3/4 */

};

static const struct SiS_CHTVRegData SiS300_CHTVReg_OPAL[] =
{
	{{0x41,0x12,0x01,0x50,0x34,0,0,0,0,0,0,0,0,0,0,0}}, /* Mode 9: 640x400 PAL 1/1 */
	{{0x41,0x12,0x00,0x50,0x00,0,0,0,0,0,0,0,0,0,0,0}},
	{{0x41,0x12,0x01,0x50,0x34,0,0,0,0,0,0,0,0,0,0,0}},
	{{0x41,0x12,0x00,0x50,0x00,0,0,0,0,0,0,0,0,0,0,0}},
	{{0x61,0x94,0x01,0x36,0x30,0,0,0,0,0,0,0,0,0,0,0}}, /* Mode 14: 640x480 PAL 1/1 */
	{{0x83,0x76,0x01,0x40,0x31,0,0,0,0,0,0,0,0,0,0,0}}  /* Mode 20: 800x600 PAL 5/6 */

};

static const struct SiS_CHTVRegData SiS300_CHTVReg_SOPAL[] =
{
	{{0x41,0x12,0x01,0x50,0x34,0,0,0,0,0,0,0,0,0,0,0}}, /* Mode 9: 640x400 PAL 1/1 */
	{{0x41,0x12,0x00,0x50,0x00,0,0,0,0,0,0,0,0,0,0,0}},
	{{0x41,0x12,0x01,0x50,0x34,0,0,0,0,0,0,0,0,0,0,0}},
	{{0x41,0x12,0x00,0x50,0x00,0,0,0,0,0,0,0,0,0,0,0}},
	{{0x60,0x30,0x00,0x10,0x00,0,0,0,0,0,0,0,0,0,0,0}}, /* Mode 13: 640x480 PAL 5/4 */
	{{0x81,0x50,0x00,0x1b,0x00,0,0,0,0,0,0,0,0,0,0,0}}  /* Mode 19: 800x600 PAL 1/1 */
};

static const unsigned char SiS300_CHTVVCLKUNTSC[]  = { 0x29,0x29,0x29,0x29,0x2a,0x2e };

static const unsigned char SiS300_CHTVVCLKONTSC[]  = { 0x2c,0x2c,0x2c,0x2c,0x2d,0x2b };

static const unsigned char SiS300_CHTVVCLKSONTSC[] = { 0x2c,0x2c,0x2c,0x2c,0x2d,0x2b };

static const unsigned char SiS300_CHTVVCLKUPAL[]   = { 0x2f,0x2f,0x2f,0x2f,0x2f,0x31 };

static const unsigned char SiS300_CHTVVCLKOPAL[]   = { 0x2f,0x2f,0x2f,0x2f,0x30,0x32 };

static const unsigned char SiS300_CHTVVCLKSOPAL[]  = { 0x2f,0x2f,0x2f,0x2f,0x36,0x29 };


