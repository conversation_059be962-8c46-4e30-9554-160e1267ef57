/* $XFree86$ */
/* $XdotOrg$ */
/*
 * Register settings for SiS 315/330/340 series
 *
 * Copyright (C) 2001-2005 by <PERSON>, Vienna, Austria
 *
 * If distributed as part of the Linux kernel, the following license terms
 * apply:
 *
 * * This program is free software; you can redistribute it and/or modify
 * * it under the terms of the GNU General Public License as published by
 * * the Free Software Foundation; either version 2 of the named License,
 * * or any later version.
 * *
 * * This program is distributed in the hope that it will be useful,
 * * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * * GNU General Public License for more details.
 * *
 * * You should have received a copy of the GNU General Public License
 * * along with this program; if not, write to the Free Software
 * * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA 02111-1307, USA
 *
 * Otherwise, the following license terms apply:
 *
 * * Redistribution and use in source and binary forms, with or without
 * * modification, are permitted provided that the following conditions
 * * are met:
 * * 1) Redistributions of source code must retain the above copyright
 * *    notice, this list of conditions and the following disclaimer.
 * * 2) Redistributions in binary form must reproduce the above copyright
 * *    notice, this list of conditions and the following disclaimer in the
 * *    documentation and/or other materials provided with the distribution.
 * * 3) The name of the author may not be used to endorse or promote products
 * *    derived from this software without specific prior written permission.
 * *
 * * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
 * * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * * IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
 * * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
 * * NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
 * * THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * Author: 	Thomas Winischhofer <<EMAIL>>
 *
 */

static const struct SiS_Ext SiS310_EModeIDTable[] =
{
	{0x6a,0x2212,0x0102,SIS_RI_800x600,  0x00,0x00,0x07,0x06,0x00, 3}, /* 800x600x? */
	{0x2e,0x0a1b,0x0101,SIS_RI_640x480,  0x00,0x00,0x05,0x05,0x08, 2}, /* 640x480x8 */
	{0x2f,0x0a1b,0x0100,SIS_RI_640x400,  0x00,0x00,0x05,0x05,0x10, 0}, /* 640x400x8 */
	{0x30,0x2a1b,0x0103,SIS_RI_800x600,  0x00,0x00,0x07,0x06,0x00, 3}, /* 800x600x8 */
	{0x31,0x4a1b,0x0000,SIS_RI_720x480,  0x00,0x00,0x06,0x06,0x11,-1}, /* 720x480x8 */
	{0x32,0x4a1b,0x0000,SIS_RI_720x576,  0x00,0x00,0x06,0x06,0x12,-1}, /* 720x576x8 */
	{0x33,0x4a1d,0x0000,SIS_RI_720x480,  0x00,0x00,0x06,0x06,0x11,-1}, /* 720x480x16 */
	{0x34,0x6a1d,0x0000,SIS_RI_720x576,  0x00,0x00,0x06,0x06,0x12,-1}, /* 720x576x16 */
	{0x35,0x4a1f,0x0000,SIS_RI_720x480,  0x00,0x00,0x06,0x06,0x11,-1}, /* 720x480x32 */
	{0x36,0x6a1f,0x0000,SIS_RI_720x576,  0x00,0x00,0x06,0x06,0x12,-1}, /* 720x576x32 */
	{0x37,0x0212,0x0104,SIS_RI_1024x768, 0x00,0x00,0x08,0x07,0x13, 4}, /* 1024x768x? */
	{0x38,0x0a1b,0x0105,SIS_RI_1024x768, 0x00,0x00,0x08,0x07,0x13, 4}, /* 1024x768x8 */
	{0x3a,0x0e3b,0x0107,SIS_RI_1280x1024,0x00,0x00,0x00,0x00,0x1a, 8}, /* 1280x1024x8 */
	{0x3c,0x0e3b,0x0130,SIS_RI_1600x1200,0x00,0x00,0x00,0x00,0x1e,10}, /* 1600x1200x8 */
	{0x3d,0x0e7d,0x0131,SIS_RI_1600x1200,0x00,0x00,0x00,0x00,0x1e,10}, /* 1600x1200x16 */
	{0x40,0x9a1c,0x010d,SIS_RI_320x200,  0x00,0x00,0x04,0x04,0x25, 0}, /* 320x200x15 */
	{0x41,0x9a1d,0x010e,SIS_RI_320x200,  0x00,0x00,0x04,0x04,0x25, 0}, /* 320x200x16 */
	{0x43,0x0a1c,0x0110,SIS_RI_640x480,  0x00,0x00,0x05,0x05,0x08, 2},
	{0x44,0x0a1d,0x0111,SIS_RI_640x480,  0x00,0x00,0x05,0x05,0x08, 2}, /* 640x480x16 */
	{0x46,0x2a1c,0x0113,SIS_RI_800x600,  0x00,0x00,0x07,0x06,0x00, 3},
	{0x47,0x2a1d,0x0114,SIS_RI_800x600,  0x00,0x00,0x07,0x06,0x00, 3}, /* 800x600x16 */
	{0x49,0x0a3c,0x0116,SIS_RI_1024x768, 0x00,0x00,0x00,0x07,0x13, 4},
	{0x4a,0x0a3d,0x0117,SIS_RI_1024x768, 0x00,0x00,0x08,0x07,0x13, 4}, /* 1024x768x16 */
	{0x4c,0x0e7c,0x0119,SIS_RI_1280x1024,0x00,0x00,0x00,0x00,0x1a, 8},
	{0x4d,0x0e7d,0x011a,SIS_RI_1280x1024,0x00,0x00,0x00,0x00,0x1a, 8}, /* 1280x1024x16 */
	{0x50,0x9a1b,0x0132,SIS_RI_320x240,  0x00,0x00,0x04,0x04,0x26, 2}, /* 320x240x8  */
	{0x51,0xba1b,0x0133,SIS_RI_400x300,  0x00,0x00,0x07,0x07,0x27, 3}, /* 400x300x8  */
	{0x52,0xba1b,0x0134,SIS_RI_512x384,  0x00,0x00,0x00,0x00,0x28, 4}, /* 512x384x8  */
	{0x56,0x9a1d,0x0135,SIS_RI_320x240,  0x00,0x00,0x04,0x04,0x26, 2}, /* 320x240x16 */
	{0x57,0xba1d,0x0136,SIS_RI_400x300,  0x00,0x00,0x07,0x07,0x27, 3}, /* 400x300x16 */
	{0x58,0xba1d,0x0137,SIS_RI_512x384,  0x00,0x00,0x00,0x00,0x28, 4}, /* 512x384x16 */
	{0x59,0x9a1b,0x0138,SIS_RI_320x200,  0x00,0x00,0x04,0x04,0x25, 0}, /* 320x200x8  */
	{0x5a,0x021b,0x0138,SIS_RI_320x240,  0x00,0x00,0x00,0x00,0x3f, 2}, /* 320x240x8  fstn */
	{0x5b,0x0a1d,0x0135,SIS_RI_320x240,  0x00,0x00,0x00,0x00,0x3f, 2}, /* 320x240x16 fstn */
	{0x5c,0xba1f,0x0000,SIS_RI_512x384,  0x00,0x00,0x00,0x00,0x28, 4}, /* 512x384x32 */
	{0x5d,0x0a1d,0x0139,SIS_RI_640x400,  0x00,0x00,0x05,0x07,0x10, 0},
	{0x5e,0x0a1f,0x0000,SIS_RI_640x400,  0x00,0x00,0x05,0x07,0x10, 0}, /* 640x400x32 */
	{0x62,0x0a3f,0x013a,SIS_RI_640x480,  0x00,0x00,0x05,0x05,0x08, 2}, /* 640x480x32 */
	{0x63,0x2a3f,0x013b,SIS_RI_800x600,  0x00,0x00,0x07,0x06,0x00, 3}, /* 800x600x32 */
	{0x64,0x0a7f,0x013c,SIS_RI_1024x768, 0x00,0x00,0x08,0x07,0x13, 4}, /* 1024x768x32 */
	{0x65,0x0eff,0x013d,SIS_RI_1280x1024,0x00,0x00,0x00,0x00,0x1a, 8}, /* 1280x1024x32 */
	{0x66,0x0eff,0x013e,SIS_RI_1600x1200,0x00,0x00,0x00,0x00,0x1e,10}, /* 1600x1200x32 */
	{0x68,0x067b,0x013f,SIS_RI_1920x1440,0x00,0x00,0x00,0x00,0x29,-1}, /* 1920x1440x8 */
	{0x69,0x06fd,0x0140,SIS_RI_1920x1440,0x00,0x00,0x00,0x00,0x29,-1}, /* 1920x1440x16 */
	{0x6b,0x07ff,0x0141,SIS_RI_1920x1440,0x00,0x00,0x00,0x00,0x29,-1}, /* 1920x1440x32 */
	{0x6c,0x067b,0x0000,SIS_RI_2048x1536,0x00,0x00,0x00,0x00,0x2f,-1}, /* 2048x1536x8 */
	{0x6d,0x06fd,0x0000,SIS_RI_2048x1536,0x00,0x00,0x00,0x00,0x2f,-1}, /* 2048x1536x16 */
	{0x6e,0x07ff,0x0000,SIS_RI_2048x1536,0x00,0x00,0x00,0x00,0x2f,-1}, /* 2048x1536x32 */
	{0x70,0x6a1b,0x0000,SIS_RI_800x480,  0x00,0x00,0x07,0x07,0x34,-1}, /* 800x480x8 */
	{0x71,0x4a1b,0x0000,SIS_RI_1024x576, 0x00,0x00,0x00,0x00,0x37,-1}, /* 1024x576x8 */
	{0x74,0x4a1d,0x0000,SIS_RI_1024x576, 0x00,0x00,0x00,0x00,0x37,-1}, /* 1024x576x16 */
	{0x75,0x0a3d,0x0000,SIS_RI_1280x720, 0x00,0x00,0x00,0x00,0x3a, 5}, /* 1280x720x16 */
	{0x76,0x6a1f,0x0000,SIS_RI_800x480,  0x00,0x00,0x07,0x07,0x34,-1}, /* 800x480x32 */
	{0x77,0x4a1f,0x0000,SIS_RI_1024x576, 0x00,0x00,0x00,0x00,0x37,-1}, /* 1024x576x32 */
	{0x78,0x0a3f,0x0000,SIS_RI_1280x720, 0x00,0x00,0x00,0x00,0x3a, 5}, /* 1280x720x32 */
	{0x79,0x0a3b,0x0000,SIS_RI_1280x720, 0x00,0x00,0x00,0x00,0x3a, 5}, /* 1280x720x8 */
	{0x7a,0x6a1d,0x0000,SIS_RI_800x480,  0x00,0x00,0x07,0x07,0x34,-1}, /* 800x480x16 */
	{0x7c,0x0e3b,0x0000,SIS_RI_1280x960, 0x00,0x00,0x00,0x00,0x3d,-1}, /* 1280x960x8 */
	{0x7d,0x0e7d,0x0000,SIS_RI_1280x960, 0x00,0x00,0x00,0x00,0x3d,-1}, /* 1280x960x16 */
	{0x7e,0x0eff,0x0000,SIS_RI_1280x960, 0x00,0x00,0x00,0x00,0x3d,-1}, /* 1280x960x32 */
	{0x23,0x0e3b,0x0000,SIS_RI_1280x768, 0x00,0x00,0x00,0x00,0x40, 6}, /* 1280x768x8 */
	{0x24,0x0e7d,0x0000,SIS_RI_1280x768, 0x00,0x00,0x00,0x00,0x40, 6}, /* 1280x768x16 */
	{0x25,0x0eff,0x0000,SIS_RI_1280x768, 0x00,0x00,0x00,0x00,0x40, 6}, /* 1280x768x32 */
	{0x26,0x0e3b,0x0000,SIS_RI_1400x1050,0x00,0x00,0x00,0x00,0x43, 9}, /* 1400x1050x8 */
	{0x27,0x0e7d,0x0000,SIS_RI_1400x1050,0x00,0x00,0x00,0x00,0x43, 9}, /* 1400x1050x16 */
	{0x28,0x0eff,0x0000,SIS_RI_1400x1050,0x00,0x00,0x00,0x00,0x43, 9}, /* 1400x1050x32*/
	{0x29,0x4e1b,0x0000,SIS_RI_1152x864, 0x00,0x00,0x00,0x00,0x45,-1}, /* 1152x864 */
	{0x2a,0x4e3d,0x0000,SIS_RI_1152x864, 0x00,0x00,0x00,0x00,0x45,-1},
	{0x2b,0x4e7f,0x0000,SIS_RI_1152x864, 0x00,0x00,0x00,0x00,0x45,-1},
	{0x39,0x6a1b,0x0000,SIS_RI_848x480,  0x00,0x00,0x00,0x00,0x48,-1}, /* 848x480 */
	{0x3b,0x6a3d,0x0000,SIS_RI_848x480,  0x00,0x00,0x00,0x00,0x48,-1},
	{0x3e,0x6a7f,0x0000,SIS_RI_848x480,  0x00,0x00,0x00,0x00,0x48,-1},
	{0x3f,0x6a1b,0x0000,SIS_RI_856x480,  0x00,0x00,0x00,0x00,0x4a,-1}, /* 856x480 */
	{0x42,0x6a3d,0x0000,SIS_RI_856x480,  0x00,0x00,0x00,0x00,0x4a,-1},
	{0x45,0x6a7f,0x0000,SIS_RI_856x480,  0x00,0x00,0x00,0x00,0x4a,-1},
	{0x48,0x6a3b,0x0000,SIS_RI_1360x768, 0x00,0x00,0x00,0x00,0x4c,-1}, /* 1360x768 */
	{0x4b,0x6a7d,0x0000,SIS_RI_1360x768, 0x00,0x00,0x00,0x00,0x4c,-1},
	{0x4e,0x6aff,0x0000,SIS_RI_1360x768, 0x00,0x00,0x00,0x00,0x4c,-1},
	{0x4f,0x9a1f,0x0000,SIS_RI_320x200,  0x00,0x00,0x04,0x04,0x25, 0}, /* 320x200x32 */
	{0x53,0x9a1f,0x0000,SIS_RI_320x240,  0x00,0x00,0x04,0x04,0x26, 2}, /* 320x240x32 */
	{0x54,0xba1f,0x0000,SIS_RI_400x300,  0x00,0x00,0x07,0x07,0x27, 3}, /* 400x300x32 */
	{0x5f,0x6a1b,0x0000,SIS_RI_768x576,  0x00,0x00,0x06,0x06,0x4d,-1}, /* 768x576 */
	{0x60,0x6a1d,0x0000,SIS_RI_768x576,  0x00,0x00,0x06,0x06,0x4d,-1},
	{0x61,0x6a3f,0x0000,SIS_RI_768x576,  0x00,0x00,0x06,0x06,0x4d,-1},
	{0x14,0x0e3b,0x0000,SIS_RI_1280x800, 0x00,0x00,0x00,0x00,0x4e, 7}, /* 1280x800 */
	{0x15,0x0e7d,0x0000,SIS_RI_1280x800, 0x00,0x00,0x00,0x00,0x4e, 7},
	{0x16,0x0eff,0x0000,SIS_RI_1280x800, 0x00,0x00,0x00,0x00,0x4e, 7},
	{0x17,0x0e3b,0x0000,SIS_RI_1680x1050,0x00,0x00,0x00,0x00,0x51, 9}, /* 1680x1050 */
	{0x18,0x0e7d,0x0000,SIS_RI_1680x1050,0x00,0x00,0x00,0x00,0x51, 9},
	{0x19,0x0eff,0x0000,SIS_RI_1680x1050,0x00,0x00,0x00,0x00,0x51, 9},
	{0x2c,0x267b,0x0000,SIS_RI_1920x1080,0x00,0x00,0x00,0x00,0x52,-1}, /* 1920x1080(i) */
	{0x2d,0x26fd,0x0000,SIS_RI_1920x1080,0x00,0x00,0x00,0x00,0x52,-1},
	{0x73,0x27ff,0x0000,SIS_RI_1920x1080,0x00,0x00,0x00,0x00,0x52,-1},
	{0x1d,0x6a1b,0x0000,SIS_RI_960x540,  0x00,0x00,0x00,0x00,0x53,-1}, /* 960x540 */
	{0x1e,0x6a3d,0x0000,SIS_RI_960x540,  0x00,0x00,0x00,0x00,0x53,-1},
	{0x1f,0x6a7f,0x0000,SIS_RI_960x540,  0x00,0x00,0x00,0x00,0x53,-1},
	{0x20,0x6a1b,0x0000,SIS_RI_960x600,  0x00,0x00,0x00,0x00,0x54,-1}, /* 960x600 */
	{0x21,0x6a3d,0x0000,SIS_RI_960x600,  0x00,0x00,0x00,0x00,0x54,-1},
	{0x22,0x6a7f,0x0000,SIS_RI_960x600,  0x00,0x00,0x00,0x00,0x54,-1},
	{0x1a,0x0e3b,0x0000,SIS_RI_1280x854, 0x00,0x00,0x00,0x00,0x55, 8}, /* 1280x854 */
	{0x1b,0x0e7d,0x0000,SIS_RI_1280x854, 0x00,0x00,0x00,0x00,0x55, 8},
	{0x1c,0x0eff,0x0000,SIS_RI_1280x854, 0x00,0x00,0x00,0x00,0x55, 8},
	{0xff,0x0000,0x0000,0,               0x00,0x00,0x00,0x00,0x00,-1}
};

static const struct SiS_Ext2 SiS310_RefIndex[] =
{
	{0x085f,0x0d,0x03,0x05,0x05,0x6a, 800, 600, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x0 */
	{0x0067,0x0e,0x04,0x05,0x05,0x6a, 800, 600, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x1 */
	{0x0067,0x0f,0x08,0x48,0x05,0x6a, 800, 600, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x2 */
	{0x0067,0x10,0x07,0x8b,0x05,0x6a, 800, 600, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x3 */
	{0x0047,0x11,0x0a,0x00,0x05,0x6a, 800, 600, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x4 */
	{0x0047,0x12,0x0d,0x00,0x05,0x6a, 800, 600, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x5 */
	{0x0047,0x13,0x13,0x00,0x05,0x6a, 800, 600, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x6 */
	{0x0107,0x14,0x1c,0x00,0x05,0x6a, 800, 600, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x7 */
	{0xc85f,0x05,0x00,0x04,0x04,0x2e, 640, 480, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x8 */
	{0xc067,0x06,0x02,0x04,0x04,0x2e, 640, 480, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x9 */
	{0xc067,0x07,0x02,0x47,0x04,0x2e, 640, 480, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0xa */
	{0xc067,0x08,0x03,0x8a,0x04,0x2e, 640, 480, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0xb */
	{0xc047,0x09,0x05,0x00,0x04,0x2e, 640, 480, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0xc */
	{0xc047,0x0a,0x09,0x00,0x04,0x2e, 640, 480, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0xd */
	{0xc047,0x0b,0x0e,0x00,0x04,0x2e, 640, 480, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0xe */
	{0xc047,0x0c,0x15,0x00,0x04,0x2e, 640, 480, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0xf */
	{0x487f,0x04,0x00,0x00,0x00,0x2f, 640, 400, 0x30, 0x55, 0x6e, 0x00, 0x00, 0x00, 0x00}, /* 0x10 */
	{0xc06f,0x3c,0x01,0x06,0x13,0x31, 720, 480, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x11 */
	{0x006f,0x3d,0x6f,0x06,0x14,0x32, 720, 576, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x12 (6f was 03) */
	{0x0087,0x15,0x06,0x00,0x06,0x37,1024, 768, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x13 */
	{0xc877,0x16,0x0b,0x06,0x06,0x37,1024, 768, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x14 */
	{0xc067,0x17,0x0f,0x49,0x06,0x37,1024, 768, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x15 */
	{0x0067,0x18,0x11,0x00,0x06,0x37,1024, 768, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x16 */
	{0x0047,0x19,0x16,0x8c,0x06,0x37,1024, 768, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x17 */
	{0x0107,0x1a,0x1b,0x00,0x06,0x37,1024, 768, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x18 */
	{0x0107,0x1b,0x1f,0x00,0x06,0x37,1024, 768, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x19 */
	{0x0087,0x1c,0x11,0x00,0x07,0x3a,1280,1024, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x1a */
	{0x0137,0x1d,0x19,0x07,0x07,0x3a,1280,1024, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x1b */
	{0x0107,0x1e,0x1e,0x00,0x07,0x3a,1280,1024, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x1c */
	{0x0207,0x1f,0x20,0x00,0x07,0x3a,1280,1024, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x1d */
	{0x0227,0x20,0x21,0x09,0x09,0x3c,1600,1200, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x1e */
	{0x0407,0x21,0x22,0x00,0x09,0x3c,1600,1200, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x1f */
	{0x0407,0x22,0x23,0x00,0x09,0x3c,1600,1200, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x20 */
	{0x0407,0x23,0x25,0x00,0x09,0x3c,1600,1200, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x21 */
	{0x0007,0x24,0x26,0x00,0x09,0x3c,1600,1200, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x22 */
	{0x0007,0x25,0x2c,0x00,0x09,0x3c,1600,1200, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x23 */
	{0x0007,0x26,0x34,0x00,0x09,0x3c,1600,1200, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x24 */
	{0x407f,0x00,0x00,0x00,0x00,0x40, 320, 200, 0x30, 0x56, 0x4e, 0x00, 0x00, 0x00, 0x00}, /* 0x25 */
	{0xc07f,0x01,0x00,0x04,0x04,0x50, 320, 240, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x26 */
	{0x007f,0x02,0x04,0x05,0x05,0x51, 400, 300, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x27 */
	{0xc077,0x03,0x0b,0x06,0x06,0x52, 512, 384, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x28 */
	{0x8007,0x27,0x27,0x00,0x00,0x68,1920,1440, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x29 */
	{0x4007,0x28,0x29,0x00,0x00,0x68,1920,1440, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x2a */
	{0x4007,0x29,0x2e,0x00,0x00,0x68,1920,1440, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x2b */
	{0x4007,0x2a,0x30,0x00,0x00,0x68,1920,1440, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x2c */
	{0x4007,0x2b,0x35,0x00,0x00,0x68,1920,1440, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x2d */
	{0x4005,0x2c,0x39,0x00,0x00,0x68,1920,1440, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x2e */
	{0x4007,0x2d,0x2b,0x00,0x00,0x6c,2048,1536, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x2f */
	{0x4007,0x2e,0x31,0x00,0x00,0x6c,2048,1536, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x30 */
	{0x4007,0x2f,0x33,0x00,0x00,0x6c,2048,1536, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x31 */
	{0x4007,0x30,0x37,0x00,0x00,0x6c,2048,1536, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x32 */
	{0x4005,0x31,0x38,0x00,0x00,0x6c,2048,1536, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x33 */
	{0x2077,0x32,0x40,0x08,0x18,0x70, 800, 480, 0x30, 0x00, 0x00, 0x32, 0x40, 0x5e, 0x73}, /* 0x34 */
	{0x2047,0x33,0x07,0x08,0x18,0x70, 800, 480, 0x30, 0x00, 0x00, 0x33, 0x07, 0xff, 0xff}, /* 0x35 */
	{0x2047,0x34,0x0a,0x08,0x18,0x70, 800, 480, 0x30, 0x00, 0x00, 0x34, 0x0a, 0xff, 0xff}, /* 0x36 */
	{0x2077,0x35,0x0b,0x09,0x19,0x71,1024, 576, 0x30, 0x00, 0x00, 0x35, 0x0b, 0x5f, 0x74}, /* 0x37 */
	{0x2047,0x36,0x11,0x09,0x19,0x71,1024, 576, 0x30, 0x00, 0x00, 0x36, 0x11, 0xff, 0xff}, /* 0x38 */
	{0x2047,0x37,0x16,0x09,0x19,0x71,1024, 576, 0x30, 0x00, 0x00, 0x37, 0x16, 0xff, 0xff}, /* 0x39 */
	{0x3137,0x38,0x19,0x0a,0x0c,0x75,1280, 720, 0x30, 0x00, 0x00, 0x38, 0x19, 0x60, 0x75}, /* 0x3a */
	{0x3107,0x39,0x1e,0x0a,0x0c,0x75,1280, 720, 0x30, 0x00, 0x00, 0x39, 0x1e, 0xff, 0xff}, /* 0x3b */
	{0x3307,0x3a,0x20,0x0a,0x0c,0x75,1280, 720, 0x30, 0x00, 0x00, 0x3a, 0x20, 0xff, 0xff}, /* 0x3c */
	{0x0127,0x3b,0x19,0x08,0x0a,0x7c,1280, 960, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x3d */
	{0x0227,0x4c,0x59,0x08,0x0a,0x7c,1280, 960, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x3e */
	{0xc07f,0x4e,0x00,0x06,0x04,0x5a, 320, 240, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x3f */    /* FSTN 320x240 */
	{0x2077,0x42,0x5b,0x08,0x11,0x23,1280, 768, 0x30, 0x00, 0x00, 0x58, 0x19, 0x42, 0x5b}, /* 0x40 */    /* 0x5b was 0x12 */
	{0x2077,0x42,0x5b,0x08,0x11,0x23,1280, 768, 0x30, 0x00, 0x00, 0x59, 0x1e, 0xff, 0xff}, /* 0x41 */
	{0x2077,0x42,0x5b,0x08,0x11,0x23,1280, 768, 0x30, 0x00, 0x00, 0x5a, 0x20, 0xff, 0xff}, /* 0x42 */
	{0x0127,0x43,0x4d,0x08,0x0b,0x26,1400,1050, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x43 */
	{0x0207,0x4b,0x5a,0x08,0x0b,0x26,1400,1050, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x44 1400x1050-75Hz */
	{0x0127,0x54,0x6d,0x00,0x1a,0x29,1152, 864, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x45 1152x864-60Hz  */
	{0x0127,0x44,0x19,0x00,0x1a,0x29,1152, 864, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x46 1152x864-75Hz  */
	{0x0127,0x4a,0x1e,0x00,0x1a,0x29,1152, 864, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x47 1152x864-85Hz  */
	{0x0087,0x45,0x57,0x00,0x16,0x39, 848, 480, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x48 848x480-38Hzi  */
	{0xc067,0x46,0x55,0x0b,0x16,0x39, 848, 480, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x49 848x480-60Hz   */
	{0x0087,0x47,0x57,0x00,0x17,0x3f, 856, 480, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x4a 856x480-38Hzi  */
	{0xc067,0x48,0x57,0x00,0x17,0x3f, 856, 480, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x4b 856x480-60Hz   */
	{0x0067,0x49,0x58,0x0c,0x1b,0x48,1360, 768, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x4c 1360x768-60Hz  */
	{0x006f,0x4d,0x71,0x06,0x15,0x5f, 768, 576, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x4d 768x576-56Hz   */
	{0x2067,0x4f,0x5c,0x08,0x0d,0x14,1280, 800, 0x30, 0x00, 0x00, 0x5b, 0x19, 0x4f, 0x5c}, /* 0x4e 1280x800-60Hz  */
	{0x2067,0x4f,0x5c,0x08,0x0d,0x14,1280, 800, 0x30, 0x00, 0x00, 0x5c, 0x1e, 0xff, 0xff}, /* 0x4f 1280x800-75Hz  */
	{0x2067,0x4f,0x5c,0x08,0x0d,0x14,1280, 800, 0x30, 0x00, 0x00, 0x5d, 0x20, 0xff, 0xff}, /* 0x50 1280x800-85Hz  */
	{0x0067,0x50,0x5d,0x0c,0x0e,0x17,1680,1050, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x51 1680x1050-60Hz */
	{0x0087,0x51,0x69,0x00,0x00,0x2c,1920,1080, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x52 1920x1080 60Hzi */
	{0x0067,0x52,0x6a,0x00,0x1c,0x1d, 960, 540, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x53 960x540 60Hz */
	{0x0077,0x53,0x6b,0x0b,0x1d,0x20, 960, 600, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* 0x54 960x600 60Hz */
	{0x2067,0x61,0x76,0x0d,0x22,0x1a,1280, 854, 0x30, 0x00, 0x00, 0x62, 0x19, 0x61, 0x76}, /* 0x55 1280x854-60Hz  */
	{0x2067,0x61,0x76,0x0d,0x22,0x1a,1280, 854, 0x30, 0x00, 0x00, 0x63, 0x1e, 0xff, 0xff}, /* 0x56 1280x854-75Hz  */
	{0x2067,0x61,0x76,0x0d,0x22,0x1a,1280, 854, 0x30, 0x00, 0x00, 0x64, 0x20, 0xff, 0xff}, /* 0x57 1280x854-85Hz  */
	{0xffff,0x00,0x00,0x00,0x00,0x00,   0,   0,    0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}
};

static const struct SiS_CRT1Table SiS310_CRT1Table[] =
{
 {{0x2d,0x27,0x28,0x90,0x2c,0x80,0xbf,0x1f,
   0x9c,0x8e,0x8f,0x96,0xb9,0x30,0x00,0x00,
   0x00}},  /* 0x0 */
 {{0x2d,0x27,0x28,0x90,0x2c,0x80,0x0b,0x3e,
   0xe9,0x8b,0xdf,0xe7,0x04,0x00,0x00,0x00,
   0x00}},  /* 0x1 */
 {{0x3d,0x31,0x31,0x81,0x37,0x1f,0x72,0xf0,
   0x58,0x8c,0x57,0x57,0x73,0x20,0x00,0x05,
   0x01}},  /* 0x2 */
 {{0x4f,0x3f,0x3f,0x93,0x45,0x0d,0x24,0xf5,
   0x02,0x88,0xff,0xff,0x25,0x10,0x00,0x01,
   0x01}},  /* 0x3 */
 {{0x5f,0x4f,0x50,0x82,0x55,0x81,0xbf,0x1f,
   0x9c,0x8e,0x8f,0x96,0xb9,0x30,0x00,0x05,
   0x00}},  /* 0x4 */
 {{0x5f,0x4f,0x4f,0x83,0x55,0x81,0x0b,0x3e,    /* corrected 640x480-60 */
   0xe9,0x8b,0xdf,0xe8,0x0c,0x00,0x00,0x05,
   0x00}},  /* 0x5 */
 {{0x63,0x4f,0x4f,0x87,0x56,0x9b,0x06,0x3e,    /* corrected 640x480-72 */
   0xe8,0x8a,0xdf,0xe7,0x07,0x00,0x00,0x01,
   0x00}},  /* 0x6 */
 {{0x64,0x4f,0x4f,0x88,0x55,0x9d,0xf2,0x1f,
   0xe0,0x83,0xdf,0xdf,0xf3,0x10,0x00,0x01,
   0x00}},  /* 0x7 */
 {{0x63,0x4f,0x4f,0x87,0x5a,0x81,0xfb,0x1f,
   0xe0,0x83,0xdf,0xdf,0xfc,0x10,0x00,0x05,
   0x00}},  /* 0x8 */
 {{0x65,0x4f,0x4f,0x89,0x58,0x80,0xfb,0x1f,
   0xe0,0x83,0xdf,0xdf,0xfc,0x10,0x00,0x05,  /* Corrected VBE */
   0x61}},  /* 0x9 */
 {{0x65,0x4f,0x4f,0x89,0x58,0x80,0x01,0x3e,
   0xe0,0x83,0xdf,0xdf,0x02,0x00,0x00,0x05,
   0x61}},  /* 0xa */
 {{0x67,0x4f,0x4f,0x8b,0x58,0x81,0x0d,0x3e,
   0xe0,0x83,0xdf,0xdf,0x0e,0x00,0x00,0x05,  /* Corrected VBE */
   0x61}},  /* 0xb */
 {{0x65,0x4f,0x4f,0x89,0x57,0x9f,0xfb,0x1f,
   0xe6,0x8a,0xdf,0xdf,0xfc,0x10,0x00,0x01,  /* Corrected VDE, VBE */
   0x00}},  /* 0xc */
 {{0x7b,0x63,0x63,0x9f,0x6a,0x93,0x6f,0xf0,
   0x58,0x8a,0x57,0x57,0x70,0x20,0x00,0x05,
   0x01}},  /* 0xd */
 {{0x7f,0x63,0x63,0x83,0x6c,0x1c,0x72,0xf0,
   0x58,0x8c,0x57,0x57,0x73,0x20,0x00,0x06,
   0x01}},  /* 0xe */
 {{0x7d,0x63,0x63,0x81,0x6e,0x1d,0x98,0xf0,
   0x7c,0x82,0x57,0x57,0x99,0x00,0x00,0x06,
   0x01}},  /* 0xf */
 {{0x7f,0x63,0x63,0x83,0x69,0x13,0x6f,0xf0,
   0x58,0x8b,0x57,0x57,0x70,0x20,0x00,0x06,
   0x01}},  /* 0x10 */
 {{0x7e,0x63,0x63,0x82,0x6b,0x13,0x75,0xf0,
   0x58,0x8b,0x57,0x57,0x76,0x20,0x00,0x06,
   0x01}},  /* 0x11 */
 {{0x81,0x63,0x63,0x85,0x6d,0x18,0x7a,0xf0,
   0x58,0x8b,0x57,0x57,0x7b,0x20,0x00,0x06,
   0x61}},  /* 0x12 */
 {{0x83,0x63,0x63,0x87,0x6e,0x19,0x81,0xf0,
   0x58,0x8b,0x57,0x57,0x82,0x20,0x00,0x06,
   0x61}},  /* 0x13 */
 {{0x85,0x63,0x63,0x89,0x6f,0x1a,0x91,0xf0,
   0x58,0x8b,0x57,0x57,0x92,0x20,0x00,0x06,
   0x61}},  /* 0x14 */
 {{0x99,0x7f,0x7f,0x9d,0x84,0x1a,0x96,0x1f,
   0x7f,0x83,0x7f,0x7f,0x97,0x10,0x00,0x02,
   0x00}},  /* 0x15 */
 {{0xa3,0x7f,0x7f,0x87,0x86,0x97,0x24,0xf5,
   0x02,0x88,0xff,0xff,0x25,0x10,0x00,0x02,
   0x01}},  /* 0x16 */
 {{0xa1,0x7f,0x7f,0x85,0x86,0x97,0x24,0xf5,
   0x02,0x88,0xff,0xff,0x25,0x10,0x00,0x02,
   0x01}},  /* 0x17 */
 {{0x9f,0x7f,0x7f,0x83,0x85,0x91,0x1e,0xf5,
   0x00,0x83,0xff,0xff,0x1f,0x10,0x00,0x02,
   0x01}},  /* 0x18 */
 {{0xa7,0x7f,0x7f,0x8b,0x89,0x95,0x26,0xf5,
   0x00,0x83,0xff,0xff,0x27,0x10,0x00,0x02,
   0x01}},  /* 0x19 */
 {{0xa9,0x7f,0x7f,0x8d,0x8c,0x9a,0x2c,0xf5,
   0x00,0x83,0xff,0xff,0x2d,0x14,0x00,0x02,
   0x62}},  /* 0x1a */
 {{0xab,0x7f,0x7f,0x8f,0x8d,0x9b,0x35,0xf5,
   0x00,0x83,0xff,0xff,0x36,0x14,0x00,0x02,
   0x62}},  /* 0x1b */
 {{0xcf,0x9f,0x9f,0x93,0xb2,0x01,0x14,0xba,
   0x00,0x83,0xff,0xff,0x15,0x00,0x00,0x03,
   0x00}},  /* 0x1c */
 {{0xce,0x9f,0x9f,0x92,0xa9,0x17,0x28,0x5a,
   0x00,0x83,0xff,0xff,0x29,0x09,0x00,0x07,
   0x01}},  /* 0x1d */
 {{0xce,0x9f,0x9f,0x92,0xa5,0x17,0x28,0x5a,
   0x00,0x83,0xff,0xff,0x29,0x09,0x00,0x07,
   0x01}},  /* 0x1e */
 {{0xd3,0x9f,0x9f,0x97,0xab,0x1f,0x2e,0x5a,
   0x00,0x83,0xff,0xff,0x2f,0x09,0x00,0x07,
   0x01}},  /* 0x1f */
 {{0x09,0xc7,0xc7,0x8d,0xd3,0x0b,0xe0,0x10,
   0xb0,0x83,0xaf,0xaf,0xe1,0x2f,0x01,0x04,
   0x00}},  /* 0x20 */
 {{0x09,0xc7,0xc7,0x8d,0xd3,0x0b,0xe0,0x10,
   0xb0,0x83,0xaf,0xaf,0xe1,0x2f,0x01,0x04,
   0x00}},  /* 0x21 */
 {{0x09,0xc7,0xc7,0x8d,0xd3,0x0b,0xe0,0x10,
   0xb0,0x83,0xaf,0xaf,0xe1,0x2f,0x01,0x04,
   0x00}},  /* 0x22 */
 {{0x09,0xc7,0xc7,0x8d,0xd3,0x0b,0xe0,0x10,
   0xb0,0x83,0xaf,0xaf,0xe1,0x2f,0x01,0x04,
   0x00}},  /* 0x23 */
 {{0x09,0xc7,0xc7,0x8d,0xd3,0x0b,0xe0,0x10,
   0xb0,0x83,0xaf,0xaf,0xe1,0x2f,0x01,0x04,
   0x00}},  /* 0x24 */
 {{0x09,0xc7,0xc7,0x8d,0xd3,0x0b,0xe0,0x10,
   0xb0,0x83,0xaf,0xaf,0xe1,0x2f,0x01,0x04,
   0x00}},  /* 0x25 */
 {{0x09,0xc7,0xc7,0x8d,0xd3,0x0b,0xe0,0x10,
   0xb0,0x83,0xaf,0xaf,0xe1,0x2f,0x01,0x04,
   0x00}},  /* 0x26 */
 {{0x40,0xef,0xef,0x84,0x03,0x1d,0xda,0x1f,
   0xa0,0x83,0x9f,0x9f,0xdb,0x1f,0x41,0x01,
   0x00}},  /* 0x27 */
 {{0x43,0xef,0xef,0x87,0x06,0x00,0xd4,0x1f,
   0xa0,0x83,0x9f,0x9f,0xd5,0x1f,0x41,0x05,
   0x63}},  /* 0x28 */
 {{0x45,0xef,0xef,0x89,0x07,0x01,0xd9,0x1f,
   0xa0,0x83,0x9f,0x9f,0xda,0x1f,0x41,0x05,
   0x63}},  /* 0x29 */
 {{0x40,0xef,0xef,0x84,0x03,0x1d,0xda,0x1f,
   0xa0,0x83,0x9f,0x9f,0xdb,0x1f,0x41,0x01,
   0x00}},  /* 0x2a */
 {{0x40,0xef,0xef,0x84,0x03,0x1d,0xda,0x1f,
   0xa0,0x83,0x9f,0x9f,0xdb,0x1f,0x41,0x01,
   0x00}},  /* 0x2b */
 {{0x40,0xef,0xef,0x84,0x03,0x1d,0xda,0x1f,
   0xa0,0x83,0x9f,0x9f,0xdb,0x1f,0x41,0x01,
   0x00}},  /* 0x2c */
 {{0x59,0xff,0xff,0x9d,0x17,0x13,0x33,0xba,
   0x00,0x83,0xff,0xff,0x34,0x0f,0x41,0x05,
   0x44}},  /* 0x2d */
 {{0x5b,0xff,0xff,0x9f,0x18,0x14,0x38,0xba,
   0x00,0x83,0xff,0xff,0x39,0x0f,0x41,0x05,
   0x44}},  /* 0x2e */
 {{0x5b,0xff,0xff,0x9f,0x18,0x14,0x3d,0xba,
   0x00,0x83,0xff,0xff,0x3e,0x0f,0x41,0x05,
   0x44}},  /* 0x2f */
 {{0x5d,0xff,0xff,0x81,0x19,0x95,0x41,0xba,
   0x00,0x84,0xff,0xff,0x42,0x0f,0x41,0x05,
   0x44}},  /* 0x30 */
 {{0x55,0xff,0xff,0x99,0x0d,0x0c,0x3e,0xba,
   0x00,0x84,0xff,0xff,0x3f,0x0f,0x41,0x05,
   0x00}},  /* 0x31 */
 {{0x7f,0x63,0x63,0x83,0x6c,0x1c,0x72,0xba,
   0x27,0x8b,0xdf,0xdf,0x73,0x00,0x00,0x06,
   0x01}},  /* 0x32 */
 {{0x7f,0x63,0x63,0x83,0x69,0x13,0x6f,0xba,
   0x26,0x89,0xdf,0xdf,0x6f,0x00,0x00,0x06,
   0x01}},  /* 0x33 */
 {{0x7f,0x63,0x63,0x82,0x6b,0x13,0x75,0xba,
   0x29,0x8c,0xdf,0xdf,0x75,0x00,0x00,0x06,
   0x01}},  /* 0x34 */
 {{0xa3,0x7f,0x7f,0x87,0x86,0x97,0x24,0xf1,
   0xaf,0x85,0x3f,0x3f,0x25,0x30,0x00,0x02,
   0x01}},  /* 0x35 */
 {{0x9f,0x7f,0x7f,0x83,0x85,0x91,0x1e,0xf1,
   0xad,0x81,0x3f,0x3f,0x1f,0x30,0x00,0x02,
   0x01}},  /* 0x36 */
 {{0xa7,0x7f,0x7f,0x88,0x89,0x95,0x26,0xf1,   /* 95 was 15 - illegal HBE! */
   0xb1,0x85,0x3f,0x3f,0x27,0x30,0x00,0x02,
   0x01}},  /* 0x37 */
 {{0xce,0x9f,0x9f,0x92,0xa9,0x17,0x28,0xc4,
   0x7a,0x8e,0xcf,0xcf,0x29,0x21,0x00,0x07,
   0x01}},  /* 0x38 */
 {{0xce,0x9f,0x9f,0x92,0xa5,0x17,0x28,0xd4,
   0x7a,0x8e,0xcf,0xcf,0x29,0x21,0x00,0x07,
   0x01}},  /* 0x39 */
 {{0xd3,0x9f,0x9f,0x97,0xab,0x1f,0x2e,0xd4,
   0x7d,0x81,0xcf,0xcf,0x2f,0x21,0x00,0x07,
   0x01}},  /* 0x3a */
 {{0xdc,0x9f,0x9f,0x80,0xaf,0x9d,0xe6,0xff,	/* 1280x960-60 - corrected */
   0xc0,0x83,0xbf,0xbf,0xe7,0x10,0x00,0x07,
   0x01}},  /* 0x3b */
 {{0x6b,0x59,0x59,0x8f,0x5e,0x8c,0x0b,0x3e,
   0xe9,0x8b,0xdf,0xe7,0x04,0x00,0x00,0x05,
   0x00}},  /* 0x3c */
 {{0x6d,0x59,0x59,0x91,0x60,0x89,0x53,0xf0,	/* 720x576, corrected to 60Hz */
   0x41,0x84,0x3f,0x3f,0x54,0x00,0x00,0x05,
   0x41}},  /* 0x3d */
 {{0x86,0x6a,0x6a,0x8a,0x74,0x06,0x8c,0x15,
   0x4f,0x83,0xef,0xef,0x8d,0x30,0x00,0x02,
   0x00}},  /* 0x3e */
 {{0x81,0x6a,0x6a,0x85,0x70,0x00,0x0f,0x3e,
   0xeb,0x8e,0xdf,0xdf,0x10,0x00,0x00,0x02,
   0x00}},  /* 0x3f */
 {{0xa3,0x7f,0x7f,0x87,0x86,0x97,0x1e,0xf1,
   0xae,0x85,0x57,0x57,0x1f,0x30,0x00,0x02,
   0x01}},  /* 0x40 */
 {{0xa3,0x7f,0x7f,0x87,0x86,0x97,0x24,0xf5,
   0x02,0x88,0xff,0xff,0x25,0x10,0x00,0x02,
   0x01}},  /* 0x41 */
 {{0xce,0x9f,0x9f,0x92,0xa9,0x17,0x20,0xf5,
   0x03,0x88,0xff,0xff,0x21,0x10,0x00,0x07,
   0x01}},  /* 0x42 */
 {{0xe6,0xae,0xae,0x8a,0xbd,0x90,0x3d,0x10,
   0x1a,0x8d,0x19,0x19,0x3e,0x2f,0x00,0x03,
   0x00}},  /* 0x43 */
 {{0xc3,0x8f,0x8f,0x87,0x9b,0x0b,0x82,0xef, /* 1152x864-75 */
   0x60,0x83,0x5f,0x5f,0x83,0x10,0x00,0x07,
   0x01}},  /* 0x44 */
 {{0x86,0x69,0x69,0x8A,0x74,0x06,0x8C,0x15, /* 848x480-38i */
   0x4F,0x83,0xEF,0xEF,0x8D,0x30,0x00,0x02,
   0x00}},  /* 0x45 */
 {{0x83,0x69,0x69,0x87,0x6f,0x1d,0x03,0x3E, /* 848x480-60 */
   0xE5,0x8d,0xDF,0xe4,0x04,0x00,0x00,0x06,
   0x00}},  /* 0x46 */
 {{0x86,0x6A,0x6A,0x8A,0x74,0x06,0x8C,0x15, /* 856x480-38i */
   0x4F,0x83,0xEF,0xEF,0x8D,0x30,0x00,0x02,
   0x00}},  /* 0x47 */
 {{0x81,0x6A,0x6A,0x85,0x70,0x00,0x0F,0x3E, /* 856x480-60 */
   0xEB,0x8E,0xDF,0xDF,0x10,0x00,0x00,0x02,
   0x00}},  /* 0x48 */
 {{0xdd,0xa9,0xa9,0x81,0xb4,0x97,0x26,0xfd, /* 1360x768-60 */
   0x01,0x8d,0xff,0x00,0x27,0x10,0x00,0x03,
   0x01}},  /* 0x49 */
 {{0xd9,0x8f,0x8f,0x9d,0xba,0x0a,0x8a,0xff, /* 1152x864-84  */
   0x60,0x8b,0x5f,0x5f,0x8b,0x10,0x00,0x03,
   0x01}},  /* 0x4a */
 {{0xea,0xae,0xae,0x8e,0xba,0x82,0x40,0x10, /* 1400x1050-75  */
   0x1b,0x87,0x19,0x1a,0x41,0x0f,0x00,0x03,
   0x00}},  /* 0x4b */
 {{0xd3,0x9f,0x9f,0x97,0xab,0x1f,0xf1,0xff, /* 1280x960-85 */
   0xc0,0x83,0xbf,0xbf,0xf2,0x10,0x00,0x07,
   0x01}},  /* 0x4c */
 {{0x75,0x5f,0x5f,0x99,0x66,0x90,0x53,0xf0, /* 768x576, corrected to 60Hz */
   0x41,0x84,0x3f,0x3f,0x54,0x00,0x00,0x05,
   0x41}},  /* 0x4d */
 {{0x5f,0x27,0x4f,0x83,0x55,0x81,0x0b,0x3e, /* FSTN 320x240 (working) */
   0xe9,0x8b,0xdf,0xe8,0x0c,0x00,0x00,0x05,
   0x00}},  /* 0x4e */
 {{0xcd,0x9f,0x9f,0x91,0xab,0x1c,0x3a,0xff, /* 1280x800-60 */
   0x20,0x83,0x1f,0x1f,0x3b,0x10,0x00,0x07,
   0x21}},  /* 0x4f */
 {{0x15,0xd1,0xd1,0x99,0xe2,0x19,0x3d,0x10, /* 1680x1050-60 */
   0x1a,0x8d,0x19,0x19,0x3e,0x2f,0x01,0x0c,
   0x20}},  /* 0x50 */
 {{0x0e,0xef,0xef,0x92,0xfe,0x03,0x30,0xf0, /* 1920x1080-60i */
   0x1e,0x83,0x1b,0x1c,0x31,0x00,0x01,0x00,
   0x61}},  /* 0x51 */
 {{0x85,0x77,0x77,0x89,0x7d,0x01,0x31,0xf0, /* 960x540-60 */
   0x1e,0x84,0x1b,0x1c,0x32,0x00,0x00,0x02,
   0x41}},  /* 0x52 */
 {{0x87,0x77,0x77,0x8b,0x81,0x0b,0x68,0xf0, /* 960x600-60 */
   0x5a,0x80,0x57,0x57,0x69,0x00,0x00,0x02,
   0x01}},  /* 0x53 */
 {{0xcd,0x8f,0x8f,0x91,0x9b,0x1b,0x7a,0xff, /* 1152x864-60 */
   0x64,0x8c,0x5f,0x62,0x7b,0x10,0x00,0x07,
   0x41}},  /* 0x54 */
 {{0x5c,0x4f,0x4f,0x80,0x57,0x80,0xa3,0x1f, /* fake 640x400@60Hz (for LCD and TV, not actually used) */
   0x98,0x8c,0x8f,0x96,0xa4,0x30,0x00,0x05,
   0x40}},  /* 0x55 */
 {{0x2c,0x27,0x27,0x90,0x2d,0x92,0xa4,0x1f, /* fake 320x200@60Hz (for LCD and TV, not actually used) */
   0x98,0x8c,0x8f,0x96,0xa5,0x30,0x00,0x04,
   0x00}},  /* 0x56 */
 {{0xd7,0xc7,0xc7,0x9b,0xd1,0x15,0xd1,0x10, /* 1600x1200 for LCDA */
   0xb2,0x86,0xaf,0xb0,0xd2,0x2f,0x00,0x03,
   0x00}},  /* 0x57 */
 {{0xce,0x9f,0x9f,0x92,0xa9,0x17,0x28,0xdc, /* 1280x768 (1280x1024) 60 Hz */
   0x92,0x86,0xff,0x91,0x29,0x21,0x00,0x07,
   0x01}},  /* 0x58 */
 {{0xce,0x9f,0x9f,0x92,0xa5,0x17,0x28,0xdc, /* 1280x768 (1280x1024) 75 Hz */
   0x92,0x86,0xff,0x91,0x29,0x21,0x00,0x07,
   0x01}},  /* 0x59 */
 {{0xd3,0x9f,0x9f,0x97,0xab,0x1f,0x2e,0xdc, /* 1280x768 (1280x1024) 85 Hz */
   0x95,0x89,0xff,0x94,0x2f,0x21,0x00,0x07,
   0x01}},  /* 0x5a */
 {{0xce,0x9f,0x9f,0x92,0xa9,0x17,0x28,0xde, /* 1280x800 (1280x1024) 60 Hz */
   0xa2,0x86,0x1f,0xa1,0x29,0x01,0x00,0x07,
   0x01}},  /* 0x5b */
 {{0xce,0x9f,0x9f,0x92,0xa5,0x17,0x28,0xde, /* 1280x800 (1280x1024) 75 Hz */
   0xa2,0x86,0x1f,0xa1,0x29,0x01,0x00,0x07,
   0x01}},  /* 0x5c */
 {{0xd3,0x9f,0x9f,0x97,0xab,0x1f,0x2e,0xde, /* 1280x800 (1280x1024) 85 Hz */
   0xa5,0x89,0x1f,0xa4,0x2f,0x01,0x00,0x07,
   0x01}},  /* 0x5d */
 {{0x7f,0x63,0x63,0x83,0x6d,0x1d,0x0b,0x3e, /* 800x480 (wide) 60 Hz */
   0xe9,0x8b,0xdf,0xe8,0x0c,0x00,0x00,0x06,
   0x00}},  /* 0x5e */
 {{0xa0,0x7f,0x7f,0x84,0x85,0x97,0x52,0xf0, /* 1024x576 (wide) 60 Hz */
   0x41,0x85,0x3f,0x40,0x53,0x00,0x00,0x02,
   0x01}},  /* 0x5f */
 {{0xc9,0x9f,0x9f,0x8d,0xb0,0x15,0xec,0xf0, /* 1280x720 (wide) 60 Hz */
   0xd4,0x89,0xcf,0xd3,0xed,0x20,0x00,0x07,
   0x01}},  /* 0x60 */
 {{0xcb,0x9f,0x9f,0x8f,0xa5,0x13,0x5b,0xff, /* 1280x854-60 wide */
   0x56,0x89,0x55,0x55,0x5c,0x30,0x00,0x07,
   0x01}},  /* 0x61 */
 {{0xce,0x9f,0x9f,0x92,0xa9,0x17,0x28,0xde, /* 1280x854 (1280x1024) 60 Hz */
   0xbd,0x81,0x55,0xbc,0x29,0x01,0x00,0x07,
   0x41}},  /* 0x62 */
 {{0xce,0x9f,0x9f,0x92,0xa5,0x17,0x28,0xde, /* 1280x854 (1280x1024) 75 Hz */
   0xbd,0x81,0x55,0xbc,0x29,0x01,0x00,0x07,
   0x41}},  /* 0x63 */
 {{0xd3,0x9f,0x9f,0x97,0xab,0x1f,0x2e,0xde, /* 1280x854 (1280x1024) 85 Hz */
   0xc0,0x84,0x55,0xbf,0x2f,0x01,0x00,0x07,
   0x41}}   /* 0x64 */
};

static const struct SiS_MCLKData SiS310_MCLKData_0_315[] =
{
	{ 0x3b,0x22,0x01,143},
	{ 0x5c,0x23,0x01,166},
	{ 0x5c,0x23,0x01,166},
	{ 0x5c,0x23,0x01,166},
	{ 0x5c,0x23,0x01,166},
	{ 0x5c,0x23,0x01,166},
	{ 0x5c,0x23,0x01,166},
	{ 0x5c,0x23,0x01,166}
};

static const struct SiS_MCLKData SiS310_MCLKData_0_650[] =
{
	{ 0x5a,0x64,0x82, 66},
	{ 0xb3,0x45,0x82, 83},
	{ 0x37,0x61,0x82,100},
	{ 0x37,0x22,0x82,133},
	{ 0x37,0x61,0x82,100},
	{ 0x37,0x22,0x82,133},
	{ 0x37,0x22,0x82,133},
	{ 0x37,0x22,0x82,133}
};

static const struct SiS_MCLKData SiS310_MCLKData_0_330[] =
{
	{ 0x5c,0x23,0x01,166},
	{ 0x5c,0x23,0x01,166},
	{ 0x7c,0x08,0x01,200},
	{ 0x79,0x06,0x01,250},
	{ 0x7c,0x08,0x01,200},
	{ 0x7c,0x08,0x01,200},
	{ 0x7c,0x08,0x01,200},
	{ 0x79,0x06,0x01,250}
};

static const struct SiS_MCLKData SiS310_MCLKData_0_660[] =
{
	{ 0x5c,0x23,0x82,166},
	{ 0x5c,0x23,0x82,166},
	{ 0x37,0x21,0x82,200},
	{ 0x37,0x22,0x82,133},
	{ 0x29,0x21,0x82,150},
	{ 0x5c,0x23,0x82,166},
	{ 0x65,0x23,0x82,183},
	{ 0x37,0x21,0x82,200}
};

static const struct SiS_MCLKData SiS310_MCLKData_0_760[] =
{
	{ 0x37,0x22,0x82,133},
	{ 0x5c,0x23,0x82,166},
	{ 0x65,0x23,0x82,183},
	{ 0x7c,0x08,0x82,200},
	{ 0x29,0x21,0x82,150},
	{ 0x5c,0x23,0x82,166},
	{ 0x65,0x23,0x82,183},
	{ 0x37,0x21,0x82,200}
};

static const struct SiS_MCLKData SiS310_MCLKData_0_761[] =
{
	{ 0x37,0x22,0x82,133},  /* Preliminary */
	{ 0x5c,0x23,0x82,166},
	{ 0x65,0x23,0x82,183},
	{ 0x7c,0x08,0x82,200},
	{ 0x29,0x21,0x82,150},
	{ 0x5c,0x23,0x82,166},
	{ 0x65,0x23,0x82,183},
	{ 0x37,0x21,0x82,200}
};

static const struct SiS_MCLKData SiS310_MCLKData_0_340[] =
{
	{ 0x79,0x06,0x01,250},
	{ 0x7c,0x08,0x01,200},
	{ 0x7c,0x08,0x80,200},
	{ 0x79,0x06,0x80,250},
	{ 0x29,0x01,0x81,300},
	{ 0x29,0x01,0x81,300},
	{ 0x29,0x01,0x81,300},
	{ 0x29,0x01,0x81,300}
};

static const struct SiS_MCLKData SiS310_MCLKData_1[] = /* ECLK */
{
	{ 0x29,0x21,0x82,150},
	{ 0x5c,0x23,0x82,166},
	{ 0x65,0x23,0x82,183},
	{ 0x37,0x21,0x82,200},
	{ 0x37,0x22,0x82,133},
	{ 0x37,0x22,0x82,133},
	{ 0x37,0x22,0x82,133},
	{ 0x37,0x22,0x82,133}
};

static const struct SiS_MCLKData SiS310_MCLKData_1_340[] =
{
	{ 0x7c,0x08,0x01,200},
	{ 0x7c,0x08,0x01,200},
	{ 0x7c,0x08,0x80,200},
	{ 0x79,0x06,0x80,250},
	{ 0x29,0x01,0x81,300},
	{ 0x29,0x01,0x81,300},
	{ 0x29,0x01,0x81,300},
	{ 0x29,0x01,0x81,300}
};

static struct SiS_VCLKData SiS310_VCLKData[] =
{
	{ 0x1b,0xe1, 25}, /* 0x00 */
	{ 0x4e,0xe4, 28}, /* 0x01 */
	{ 0x57,0xe4, 31}, /* 0x02 */
	{ 0xc3,0xc8, 36}, /* 0x03 */
	{ 0x42,0xe2, 40}, /* 0x04 */
	{ 0xfe,0xcd, 43}, /* 0x05 */
	{ 0x5d,0xc4, 44}, /* 0x06 */
	{ 0x52,0xe2, 49}, /* 0x07 */
	{ 0x53,0xe2, 50}, /* 0x08 */
	{ 0x74,0x67, 52}, /* 0x09 */
	{ 0x6d,0x66, 56}, /* 0x0a */
	{ 0x5a,0x64, 65}, /* 0x0b */  /* was 6c c3 - WRONG */
	{ 0x46,0x44, 67}, /* 0x0c */
	{ 0xb1,0x46, 68}, /* 0x0d */
	{ 0xd3,0x4a, 72}, /* 0x0e */
	{ 0x29,0x61, 75}, /* 0x0f */
	{ 0x6e,0x46, 76}, /* 0x10 */
	{ 0x2b,0x61, 78}, /* 0x11 */
	{ 0x31,0x42, 79}, /* 0x12 */
	{ 0xab,0x44, 83}, /* 0x13 */
	{ 0x46,0x25, 84}, /* 0x14 */
	{ 0x78,0x29, 86}, /* 0x15 */
	{ 0x62,0x44, 94}, /* 0x16 */
	{ 0x2b,0x41,104}, /* 0x17 */
	{ 0x3a,0x23,105}, /* 0x18 */
	{ 0x70,0x44,108}, /* 0x19 */
	{ 0x3c,0x23,109}, /* 0x1a */
	{ 0x5e,0x43,113}, /* 0x1b */
	{ 0xbc,0x44,116}, /* 0x1c */
	{ 0xe0,0x46,132}, /* 0x1d */
	{ 0x54,0x42,135}, /* 0x1e */
	{ 0xea,0x2a,139}, /* 0x1f */
	{ 0x41,0x22,157}, /* 0x20 */
	{ 0x70,0x24,162}, /* 0x21 */
	{ 0x30,0x21,175}, /* 0x22 */
	{ 0x4e,0x22,189}, /* 0x23 */
	{ 0xde,0x26,194}, /* 0x24 */
	{ 0x62,0x06,202}, /* 0x25 */
	{ 0x3f,0x03,229}, /* 0x26 */
	{ 0xb8,0x06,234}, /* 0x27 */
	{ 0x34,0x02,253}, /* 0x28 */
	{ 0x58,0x04,255}, /* 0x29 */
	{ 0x24,0x01,265}, /* 0x2a */
	{ 0x9b,0x02,267}, /* 0x2b */
	{ 0x70,0x05,270}, /* 0x2c */
	{ 0x25,0x01,272}, /* 0x2d */
	{ 0x9c,0x02,277}, /* 0x2e */
	{ 0x27,0x01,286}, /* 0x2f */
	{ 0x3c,0x02,291}, /* 0x30 */
	{ 0xef,0x0a,292}, /* 0x31 */
	{ 0xf6,0x0a,310}, /* 0x32 */
	{ 0x95,0x01,315}, /* 0x33 */
	{ 0xf0,0x09,324}, /* 0x34 */
	{ 0xfe,0x0a,331}, /* 0x35 */
	{ 0xf3,0x09,332}, /* 0x36 */
	{ 0xea,0x08,340}, /* 0x37 */
	{ 0xe8,0x07,376}, /* 0x38 */
	{ 0xde,0x06,389}, /* 0x39 */
	{ 0x52,0x2a, 54}, /* 0x3a 301 TV */
	{ 0x52,0x6a, 27}, /* 0x3b 301 TV */
	{ 0x62,0x24, 70}, /* 0x3c 301 TV */
	{ 0x62,0x64, 70}, /* 0x3d 301 TV */
	{ 0xa8,0x4c, 30}, /* 0x3e 301 TV */
	{ 0x20,0x26, 33}, /* 0x3f 301 TV */
	{ 0x31,0xc2, 39}, /* 0x40 */
	{ 0x60,0x36, 30}, /* 0x41 Chrontel */
	{ 0x40,0x4a, 28}, /* 0x42 Chrontel */
	{ 0x9f,0x46, 44}, /* 0x43 Chrontel */
	{ 0x97,0x2c, 26}, /* 0x44 */
	{ 0x44,0xe4, 25}, /* 0x45 Chrontel */
	{ 0x7e,0x32, 47}, /* 0x46 Chrontel */
	{ 0x8a,0x24, 31}, /* 0x47 Chrontel */
	{ 0x97,0x2c, 26}, /* 0x48 Chrontel */
	{ 0xce,0x3c, 39}, /* 0x49 */
	{ 0x52,0x4a, 36}, /* 0x4a Chrontel */
	{ 0x34,0x61, 95}, /* 0x4b */
	{ 0x78,0x27,108}, /* 0x4c - was 102 */
	{ 0x66,0x43,123}, /* 0x4d Modes 0x26-0x28 (1400x1050) */
	{ 0x41,0x4e, 21}, /* 0x4e */
	{ 0xa1,0x4a, 29}, /* 0x4f Chrontel */
	{ 0x19,0x42, 42}, /* 0x50 */
	{ 0x54,0x46, 58}, /* 0x51 Chrontel */
	{ 0x25,0x42, 61}, /* 0x52 */
	{ 0x44,0x44, 66}, /* 0x53 Chrontel */
	{ 0x3a,0x62, 70}, /* 0x54 Chrontel */
	{ 0x62,0xc6, 34}, /* 0x55 848x480-60 */
	{ 0x6a,0xc6, 37}, /* 0x56 848x480-75 - TEMP */
	{ 0xbf,0xc8, 35}, /* 0x57 856x480-38i,60 */
	{ 0x30,0x23, 88}, /* 0x58 1360x768-62 (is 60Hz!) */
	{ 0x52,0x07,149}, /* 0x59 1280x960-85 */
	{ 0x56,0x07,156}, /* 0x5a 1400x1050-75 */
	{ 0x70,0x29, 81}, /* 0x5b 1280x768 LCD */
	{ 0x45,0x25, 83}, /* 0x5c 1280x800  */
	{ 0x70,0x0a,147}, /* 0x5d 1680x1050 */
	{ 0x70,0x24,162}, /* 0x5e 1600x1200 */
	{ 0x5a,0x64, 65}, /* 0x5f 1280x720 - temp */
	{ 0x63,0x46, 68}, /* 0x60 1280x768_2 */
	{ 0x31,0x42, 79}, /* 0x61 1280x768_3 - temp */
	{    0,   0,  0}, /* 0x62 - custom (will be filled out at run-time) */
	{ 0x5a,0x64, 65}, /* 0x63 1280x720 (LCD LVDS) */
	{ 0x70,0x28, 90}, /* 0x64 1152x864@60 */
	{ 0x41,0xc4, 32}, /* 0x65 848x480@60 */
	{ 0x5c,0xc6, 32}, /* 0x66 856x480@60 */
	{ 0x76,0xe7, 27}, /* 0x67 720x480@60 */
	{ 0x5f,0xc6, 33}, /* 0x68 720/768x576@60 */
	{ 0x52,0x27, 75}, /* 0x69 1920x1080i 60Hz interlaced */
	{ 0x7c,0x6b, 38}, /* 0x6a 960x540@60 */
	{ 0xe3,0x56, 41}, /* 0x6b 960x600@60 */
	{ 0x45,0x25, 83}, /* 0x6c 1280x800 */
	{ 0x70,0x28, 90}, /* 0x6d 1152x864@60 */
	{ 0x15,0xe1, 20}, /* 0x6e 640x400@60 (fake, not actually used) */
	{ 0x5f,0xc6, 33}, /* 0x6f 720x576@60 */
	{ 0x37,0x5a, 10}, /* 0x70 320x200@60 (fake, not actually used) */
	{ 0x2b,0xc2, 35}, /* 0x71 768x576@60 */
	{ 0xa8,0x42,131}, /* 0x72 1600x1200@60 for LCDA */
	{ 0x1b,0xc1, 34}, /* 0x73 800x480 60Hz (wide) */
	{ 0x41,0x64, 48}, /* 0x74 1024x576 60Hz (wide) */
	{ 0x52,0x27, 75}, /* 0x75 1280x720 60Hz (wide) */
	{ 0x75,0x13, 84}  /* 0x76 1280x854 60Hz (wide) */
};

static struct SiS_VBVCLKData SiS310_VBVCLKData[] =
{
	{ 0x1b,0xe1, 25}, /* 0x00 */
	{ 0x4e,0xe4, 28}, /* 0x01 */
	{ 0x57,0xe4, 31}, /* 0x02 */
	{ 0xc3,0xc8, 36}, /* 0x03 */
	{ 0x42,0x47, 40}, /* 0x04 */
	{ 0xfe,0xcd, 43}, /* 0x05 */
	{ 0x5d,0xc4, 44}, /* 0x06 */
	{ 0x52,0x47, 49}, /* 0x07 */
	{ 0x53,0x47, 50}, /* 0x08 */
	{ 0x74,0x67, 52}, /* 0x09 */
	{ 0x6d,0x66, 56}, /* 0x0a */
	{ 0x35,0x62, 65}, /* 0x0b */  /* Was 0x5a,0x64 - 650/LVDS+301: 35,62  */
	{ 0x46,0x44, 67}, /* 0x0c */
	{ 0xb1,0x46, 68}, /* 0x0d */
	{ 0xd3,0x4a, 72}, /* 0x0e */
	{ 0x29,0x61, 75}, /* 0x0f */
	{ 0x6d,0x46, 75}, /* 0x10 */
	{ 0x41,0x43, 78}, /* 0x11 */
	{ 0x31,0x42, 79}, /* 0x12 */
	{ 0xab,0x44, 83}, /* 0x13 */
	{ 0x46,0x25, 84}, /* 0x14 */
	{ 0x78,0x29, 86}, /* 0x15 */
	{ 0x62,0x44, 94}, /* 0x16 */
	{ 0x2b,0x22,104}, /* 0x17 */
	{ 0x49,0x24,105}, /* 0x18 */
	{ 0xf8,0x2f,108}, /* 0x19 */  /* 1400x1050 LCD */
	{ 0x3c,0x23,109}, /* 0x1a */
	{ 0x5e,0x43,113}, /* 0x1b */
	{ 0xbc,0x44,116}, /* 0x1c */
	{ 0xe0,0x46,132}, /* 0x1d */
	{ 0xe2,0x46,135}, /* 0x1e */  /* 1280x1024-75, better clock for VGA2 */
	{ 0xe5,0x46,139}, /* 0x1f */  /* 1024x768-120, better clock for VGA2 */
	{ 0x15,0x01,157}, /* 0x20 */  /* 1280x1024-85, better clock for VGA2 */
	{ 0x70,0x09,162}, /* 0x21 */  /* 1600x1200-60, better clock for VGA2 */
	{ 0x30,0x21,175}, /* 0x22 */
	{ 0x4e,0x22,189}, /* 0x23 */
	{ 0xde,0x26,194}, /* 0x24 */
	{ 0x70,0x07,202}, /* 0x25 */
	{ 0x3f,0x03,229}, /* 0x26 */
	{ 0xb8,0x06,234}, /* 0x27 */
	{ 0x34,0x02,253}, /* 0x28 */
	{ 0x58,0x04,255}, /* 0x29 */
	{ 0x24,0x01,265}, /* 0x2a */
	{ 0x9b,0x02,267}, /* 0x2b */
	{ 0x70,0x05,270}, /* 0x2c */
	{ 0x25,0x01,272}, /* 0x2d */
	{ 0x9c,0x02,277}, /* 0x2e */
	{ 0x27,0x01,286}, /* 0x2f */
	{ 0x3c,0x02,291}, /* 0x30 */
	{ 0xef,0x0a,292}, /* 0x31 */
	{ 0xf6,0x0a,310}, /* 0x32 */
	{ 0x95,0x01,315}, /* 0x33 */
	{ 0xf0,0x09,324}, /* 0x34 */
	{ 0xfe,0x0a,331}, /* 0x35 */
	{ 0xf3,0x09,332}, /* 0x36 */
	{ 0xea,0x08,340}, /* 0x37 */
	{ 0xe8,0x07,376}, /* 0x38 */
	{ 0xde,0x06,389}, /* 0x39 */
	{ 0x52,0x2a, 54}, /* 0x3a 301 TV - start */
	{ 0x52,0x6a, 27}, /* 0x3b 301 TV */
	{ 0x62,0x24, 70}, /* 0x3c 301 TV */
	{ 0x62,0x64, 70}, /* 0x3d 301 TV */
	{ 0xa8,0x4c, 30}, /* 0x3e 301 TV */
	{ 0x20,0x26, 33}, /* 0x3f 301 TV */
	{ 0x31,0xc2, 39}, /* 0x40 */
	{ 0x2e,0x48, 25}, /* 0x41 Replacement for LCD on 315 for index 0 */
	{ 0x24,0x46, 25}, /* 0x42 Replacement for LCD on 315 for modes 0x01, 0x03, 0x0f, 0x10, 0x12 */
	{ 0x26,0x64, 28}, /* 0x43 Replacement for LCD on 315 for index 1 */
	{ 0x37,0x64, 40}, /* 0x44 Replacement for LCD on 315 for index 4 */
	{ 0xa1,0x42,108}, /* 0x45 1280x960 LCD */
	{ 0x37,0x61,100}, /* 0x46 1280x960 LCD */
	{ 0x78,0x27,108}, /* 0x47 */
	{ 0x97,0x2c, 26}, /* 0x48 UNUSED */
	{ 0xce,0x3c, 39}, /* 0x49 UNUSED */
	{ 0x52,0x4a, 36}, /* 0x4a UNUSED */
	{ 0x34,0x61, 95}, /* 0x4b UNUSED */
	{ 0x78,0x27,108}, /* 0x4c UNUSED */
	{ 0x66,0x43,123}, /* 0x4d 1400x1050-60 */
	{ 0x41,0x4e, 21}, /* 0x4e */
	{ 0xa1,0x4a, 29}, /* 0x4f UNUSED */
	{ 0x19,0x42, 42}, /* 0x50 UNUSED */
	{ 0x54,0x46, 58}, /* 0x51 UNUSED */
	{ 0x25,0x42, 61}, /* 0x52 UNUSED */
	{ 0x44,0x44, 66}, /* 0x53 UNUSED */
	{ 0x3a,0x62, 70}, /* 0x54 UNUSED */
	{ 0x62,0xc6, 34}, /* 0x55 848x480-60 */
	{ 0x6a,0xc6, 37}, /* 0x56 848x480-75 - TEMP, UNUSED */
	{ 0xbf,0xc8, 35}, /* 0x57 856x480-38i,60  */
	{ 0x30,0x23, 88}, /* 0x58 1360x768-62 (is 60Hz!) TEMP, UNUSED */
	{ 0x52,0x07,149}, /* 0x59 1280x960-85  */
	{ 0x56,0x07,156}, /* 0x5a 1400x1050-75 */
	{ 0x70,0x29, 81}, /* 0x5b 1280x768 LCD (TMDS) */
	{ 0xce,0x1e, 73}, /* 0x5c 1280x800_2 LCD (SiS LVDS) - (CRT1: 45 25 83) */
	{ 0xbe,0x44,121}, /* 0x5d 1680x1050 LCD */
	{ 0x70,0x24,162}, /* 0x5e 1600x1200 LCD */
	{ 0x52,0x27, 75}, /* 0x5f 1280x720 (TMDS + HDTV) (correct) */
	{ 0xc8,0x48, 77}, /* 0x60 1280x768_2 (SiS LVDS) */
	{ 0x31,0x42, 79}, /* 0x61 1280x768_3 (SiS LVDS) - temp */
	{    0,   0,  0}, /* 0x62 - custom (will be filled out at run-time) */
	{ 0x9c,0x62, 69}, /* 0x63 1280x720 (SiS LVDS) */
	{ 0x70,0x28, 90}, /* 0x64 1152x864@60 */
	{ 0x41,0xc4, 32}, /* 0x65 848x480@60 */
	{ 0x5c,0xc6, 32}, /* 0x66 856x480@60 */
	{ 0x76,0xe7, 27}, /* 0x67 720x480@60 */
	{ 0x5f,0xc6, 33}, /* 0x68 720/768x576@60 */
	{ 0x52,0x27, 75}, /* 0x69 1920x1080i 60Hz interlaced (UNUSED) */
	{ 0x7c,0x6b, 38}, /* 0x6a 960x540@60 */
	{ 0xe3,0x56, 41}, /* 0x6b 960x600@60 */
	{ 0x9c,0x62, 69}, /* 0x6c 1280x800 (SiS TMDS) (special) */
	{ 0x70,0x28, 90}, /* 0x6d 1152x864@60 */
	{ 0x15,0xe1, 20}, /* 0x6e 640x400@60 (fake, not actually used) */
	{ 0x5f,0xc6, 33}, /* 0x6f 720x576@60 */
	{ 0x37,0x5a, 10}, /* 0x70 320x200@60 (fake, not actually used) */
	{ 0x2b,0xc2, 35}, /* 0x71 768@576@60 */
	{ 0xa8,0x42,131}, /* 0x72 1600x1200@60 for LCDA */
	{ 0x1b,0xc1, 34}, /* 0x73 800x480 60Hz (wide) */
	{ 0x41,0x64, 48}, /* 0x74 1024x576 60Hz (wide) */
	{ 0x52,0x27, 75}, /* 0x75 1280x720 60Hz (wide) */
	{ 0x75,0x13, 84}  /* 0x76 1280x854 60Hz (SiS LVDS) LCD */
};

static const unsigned char SiS310_SR15[4 * 8] =
{
	0x00,0x04,0x60,0x60,
	0x0f,0x0f,0x0f,0x0f,
	0xba,0xba,0xba,0xba,
	0xa9,0xa9,0xac,0xac,
	0xa0,0xa0,0xa0,0xa8,
	0x00,0x00,0x02,0x02,
	0x30,0x30,0x40,0x40,
	0x00,0xa5,0xfb,0xf6
};

static const struct SiS_PanelDelayTbl SiS310_PanelDelayTbl[] =
{
	{{0x10,0x40}},
	{{0x10,0x40}},
	{{0x10,0x40}},
	{{0x10,0x40}},
	{{0x10,0x40}},
	{{0x10,0x40}},
	{{0x10,0x40}},
	{{0x10,0x40}},
	{{0x10,0x40}},
	{{0x10,0x40}},
	{{0x10,0x40}},
	{{0x10,0x40}},
	{{0x10,0x40}},
	{{0x10,0x40}},
	{{0x10,0x40}},
	{{0x10,0x40}}
};

static const struct SiS_PanelDelayTbl SiS310_PanelDelayTblLVDS[] =
{
	{{0x28,0xc8}},
	{{0x28,0xc8}},
	{{0x28,0xc8}},
	{{0x28,0xc8}},
	{{0x28,0xc8}},
	{{0x28,0xc8}},
	{{0x28,0xc8}},
	{{0x28,0xc8}},
	{{0x28,0xc8}},
	{{0x28,0xc8}},
	{{0x28,0xc8}},
	{{0x28,0xc8}},
	{{0x28,0xc8}},
	{{0x28,0xc8}},
	{{0x28,0xc8}},
	{{0x28,0xc8}}
};

/**************************************************************/
/* SIS VIDEO BRIDGE ----------------------------------------- */
/**************************************************************/

static const struct SiS_LCDData SiS310_St2LCD1024x768Data[] =
{
	{   62,  25, 800, 546,1344, 806},
	{   32,  15, 930, 546,1344, 806},
	{   62,  25, 800, 546,1344, 806},
	{  104,  45, 945, 496,1344, 806},
	{   62,  25, 800, 546,1344, 806},
	{   31,  18,1008, 624,1344, 806},
	{    1,   1,1344, 806,1344, 806}
};

static const struct SiS_LCDData SiS310_ExtLCD1024x768Data[] =
{
	{   42,  25,1536, 419,1344, 806},
	{   48,  25,1536, 369,1344, 806},
	{   42,  25,1536, 419,1344, 806},
	{   48,  25,1536, 369,1344, 806},
	{   12,   5, 896, 500,1344, 806},
	{   42,  25,1024, 625,1344, 806},
	{    1,   1,1344, 806,1344, 806}
};

static const struct SiS_LCDData SiS310_St2LCD1280x1024Data[] =
{
	{   22,   5, 800, 510,1650,1088},
	{   22,   5, 800, 510,1650,1088},
	{  176,  45, 900, 510,1650,1088},
	{  176,  45, 900, 510,1650,1088},
	{   22,   5, 800, 510,1650,1088},
	{   13,   5,1024, 675,1560,1152},
	{   16,   9,1266, 804,1688,1072},
	{    1,   1,1688,1066,1688,1066}
};

static const struct SiS_LCDData SiS310_ExtLCD1280x1024Data[] =
{
	{  211,  60,1024, 501,1688,1066},
	{  211,  60,1024, 508,1688,1066},
	{  211,  60,1024, 501,1688,1066},
	{  211,  60,1024, 508,1688,1066},
	{  211,  60,1024, 500,1688,1066},
	{  211,  75,1024, 625,1688,1066},
	{  211, 120,1280, 798,1688,1066},
	{    1,   1,1688,1066,1688,1066}
};

static const struct SiS_Part2PortTbl SiS310_CRT2Part2_1024x768_1[] =
{
	{{0x25,0x12,0xc9,0xdc,0xb6,0x59,0x45,0x09,0x07,0xf9,0x09,0x24}},
	{{0x2c,0x12,0x9a,0xae,0x88,0x59,0x45,0x09,0x07,0xf9,0x09,0x24}},
	{{0x25,0x12,0xc9,0xdc,0xb6,0x59,0x45,0x09,0x07,0xf9,0x09,0x24}},
	{{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00}},
	{{0x38,0x13,0x16,0x0c,0xe6,0x59,0x45,0x09,0x07,0xf9,0x09,0x24}},
	{{0x38,0x18,0x16,0x00,0x00,0x59,0x45,0x09,0x07,0xf9,0x09,0x24}},
	{{0x36,0x13,0x13,0x25,0xff,0x59,0x45,0x09,0x07,0xf9,0x09,0x24}}
};

/**************************************************************/
/* LVDS, CHRONTEL ------------------------------------------- */
/**************************************************************/

static const struct SiS_LVDSData SiS310_CHTVUPALData[] =
{
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{ 840, 625, 840, 625},
	{ 960, 750, 960, 750},
	{1400,1000,1400,1000}
};

static const struct SiS_LVDSData SiS310_CHTVOPALData[] =
{
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{ 840, 625, 840, 625},
	{ 944, 625, 944, 625},
	{1400, 875,1400, 875}
};

static const struct SiS_LVDSData SiS310_CHTVUPALMData[] =
{
	{ 840, 600, 840, 600},
	{ 840, 600, 840, 600},
	{ 840, 600, 840, 600},
	{ 840, 600, 840, 600},
	{ 784, 600, 784, 600},
	{1064, 750,1064, 750},
	{1160, 945,1160, 945}
};

static const struct SiS_LVDSData SiS310_CHTVOPALMData[] =
{
	{ 840, 525, 840, 525},
	{ 840, 525, 840, 525},
	{ 840, 525, 840, 525},
	{ 840, 525, 840, 525},
	{ 784, 525, 784, 525},
	{1040, 700,1040, 700},
	{1160, 840,1160, 840}
};

static const struct SiS_LVDSData SiS310_CHTVUPALNData[] =
{
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{ 840, 625, 840, 625},
	{ 960, 750, 960, 750},
	{1400,1000,1400,1000}
};

static const struct SiS_LVDSData SiS310_CHTVOPALNData[] =
{
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{ 840, 625, 840, 625},
	{ 944, 625, 944, 625},
	{1400, 875,1400, 875}
};

static const struct SiS_LVDSData SiS310_CHTVSOPALData[] =   /* (super overscan - no effect on 7019) */
{
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{1008, 625,1008, 625},
	{ 840, 625, 840, 625},
	{ 944, 625, 944, 625},
        {1400, 875,1400, 875}
};

/* CRT1 CRTC for Chrontel TV slave modes */

static const struct SiS_LVDSCRT1Data SiS310_CHTVCRT1UNTSC[] =
{
 {{0x64,0x4f,0x88,0x56,0x9f,0x56,0x3e,
   0xe8,0x84,0x8f,0x57,0x20,0x00,0x01,
   0x00 }},
 {{0x64,0x4f,0x88,0x56,0x9f,0x56,0x3e,
   0xd0,0x82,0x5d,0x57,0x00,0x00,0x01,
   0x00 }},
 {{0x64,0x4f,0x88,0x56,0x9f,0x56,0x3e,
   0xe8,0x84,0x8f,0x57,0x20,0x00,0x01,
   0x00 }},
 {{0x64,0x4f,0x88,0x56,0x9f,0x56,0x3e,
   0xd0,0x82,0x5d,0x57,0x00,0x00,0x01,
   0x00 }},
 {{0x5d,0x4f,0x81,0x56,0x99,0x56,0xba,
   0x0a,0x84,0xdf,0x57,0x00,0x00,0x01,
   0x00 }},
 {{0x80,0x63,0x84,0x6d,0x0f,0xec,0xf0,
   0x7a,0x8f,0x57,0xed,0x20,0x00,0x06,
   0x01 }},
 {{0x8c,0x7f,0x90,0x86,0x09,0xaf,0xf5,
   0x36,0x88,0xff,0xb0,0x10,0x00,0x02,
   0x01}}
};

static const struct SiS_LVDSCRT1Data SiS310_CHTVCRT1ONTSC[] =
{
 {{0x63,0x4f,0x87,0x5a,0x9f,0x0b,0x3e,
   0xc0,0x84,0x8f,0x0c,0x20,0x00,0x01,
   0x00 }},
 {{0x63,0x4f,0x87,0x5a,0x9f,0x0b,0x3e,
   0xb0,0x8d,0x5d,0x0c,0x00,0x00,0x01,
   0x00 }},
 {{0x63,0x4f,0x87,0x5a,0x9f,0x0b,0x3e,
   0xc0,0x84,0x8f,0x0c,0x20,0x00,0x01,
   0x00 }},
 {{0x63,0x4f,0x87,0x5a,0x9f,0x0b,0x3e,
   0xb0,0x8d,0x5d,0x0c,0x00,0x00,0x01,
   0x00 }},
 {{0x5d,0x4f,0x81,0x58,0x9d,0x0b,0x3e,
   0xe8,0x84,0xdf,0x0c,0x00,0x00,0x01,
   0x00 }},
 {{0x7d,0x63,0x81,0x68,0x0e,0xba,0xf0,
   0x78,0x8a,0x57,0xbb,0x20,0x00,0x06,
   0x01 }},
 {{0x8c,0x7f,0x90,0x82,0x06,0x46,0xf5,
   0x15,0x88,0xff,0x47,0x70,0x00,0x02,
   0x01 }}
};

static const struct SiS_LVDSCRT1Data SiS310_CHTVCRT1UPAL[] =
{
 {{0x79,0x4f,0x9d,0x5a,0x90,0x6f,0x3e,
   0xf8,0x83,0x8f,0x70,0x20,0x00,0x05,
   0x00 }},
 {{0x79,0x4f,0x9d,0x5a,0x90,0x6f,0x3e,
   0xde,0x81,0x5d,0x70,0x00,0x00,0x05,
   0x00 }},
 {{0x79,0x4f,0x9d,0x5a,0x90,0x6f,0x3e,
   0xf8,0x83,0x8f,0x70,0x20,0x00,0x05,
   0x00 }},
 {{0x79,0x4f,0x9d,0x5a,0x90,0x6f,0x3e,
   0xde,0x81,0x5d,0x70,0x00,0x00,0x05,
   0x00 }},
 {{0x64,0x4f,0x88,0x5a,0x9f,0x6f,0xba,
   0x15,0x83,0xdf,0x70,0x00,0x00,0x01,
   0x00 }},
 {{0x73,0x63,0x97,0x69,0x8b,0xec,0xf0,
   0x90,0x8c,0x57,0xed,0x20,0x00,0x05,
   0x01 }},
 {{0xaa,0x7f,0x8e,0x8e,0x96,0xe6,0xf5,
   0x50,0x88,0xff,0xe7,0x10,0x00,0x02,
   0x01}}
};

static const struct SiS_LVDSCRT1Data SiS310_CHTVCRT1OPAL[] =
{
 {{0x79,0x4f,0x9d,0x5a,0x90,0x6f,0x3e,
   0xf0,0x83,0x8f,0x70,0x20,0x00,0x05,
   0x00 }},
 {{0x79,0x4f,0x9d,0x5a,0x90,0x6f,0x3e,
   0xde,0x81,0x5d,0x70,0x00,0x00,0x05,
   0x00 }},
 {{0x79,0x4f,0x9d,0x5a,0x90,0x6f,0x3e,
   0xf0,0x83,0x8f,0x70,0x20,0x00,0x05,
   0x00 }},
 {{0x79,0x4f,0x9d,0x5a,0x90,0x6f,0x3e,
   0xde,0x81,0x5d,0x70,0x00,0x00,0x05,
   0x00 }},
 {{0x64,0x4f,0x88,0x58,0x9d,0x6f,0xba,
   0x15,0x83,0xdf,0x70,0x00,0x00,0x01,
   0x00 }},
 {{0x71,0x63,0x95,0x69,0x8c,0x6f,0xf0,
   0x5a,0x8b,0x57,0x70,0x20,0x00,0x05,
   0x01 }},
 {{0xaa,0x7f,0x8e,0x8f,0x96,0x69,0xf5,
   0x28,0x88,0xff,0x6a,0x10,0x00,0x02,
   0x01 }}
};

static const struct SiS_CHTVRegData SiS310_CHTVReg_UNTSC[] =
{
 {{0x4a,0x77,0xbb,0x94,0x84,0x48,0xfe,0x50,0x04,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x4a,0x77,0xbb,0x94,0x84,0x48,0xfe,0x50,0x04,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x4a,0x77,0xbb,0x94,0x84,0x48,0xfe,0x50,0x04,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x4a,0x77,0xbb,0x94,0x84,0x48,0xfe,0x50,0x04,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x6a,0x77,0xbb,0x6e,0x84,0x2e,0x02,0x5a,0x04,0x00,0x80,0x20,0x7e,0x80,0x98,0x00}},
 {{0xcf,0x77,0xb7,0xc8,0x84,0x3b,0x02,0x5a,0x04,0x00,0x80,0x19,0x88,0x30,0x7f,0x00}},
 {{0xee,0x77,0xbb,0x66,0x87,0x32,0x01,0x5a,0x04,0x00,0x80,0x1b,0xd3,0xf2,0x36,0x00}}
}; /* WRONG: 0x02: should be 0xfx, because if CIVEnable is clear, this should be set;
             0x07: Blacklevel: NTSC/PAL-M: Should be 131 (0x83), and not 0x50/0x5a
	                       PAL/PAL-N:  110 (0x6e)
			       NTSC-J:     102 (0x66)
	     0x0c-0x0f: CIV is not default as in datasheet
      MISSING: 0x21: Should set D1 to ZERO (for NTSC, PAL-M) or ONE (PAL, NTSC-J)
      Most of this is wrong in all NTSC and PAL register arrays. But I won't correct
      it as long as it works. For NTSC-J, the blacklevel is corrected in init301.c;
      for PAL-M and PAL-N all above is corrected.
    */

static const struct SiS_CHTVRegData SiS310_CHTVReg_ONTSC[] =
{
 {{0x49,0x77,0xbb,0x7b,0x84,0x34,0x00,0x50,0x04,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x49,0x77,0xbb,0x7b,0x84,0x34,0x00,0x50,0x04,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x49,0x77,0xbb,0x7b,0x84,0x34,0x00,0x50,0x04,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x49,0x77,0xbb,0x7b,0x84,0x34,0x00,0x50,0x04,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x69,0x77,0xbb,0x6e,0x84,0x1e,0x00,0x5a,0x04,0x00,0x80,0x25,0x1a,0x43,0x04,0x00}},
 {{0xce,0x77,0xb7,0xb6,0x83,0x2c,0x02,0x5a,0x04,0x00,0x80,0x1c,0x00,0x82,0x97,0x00}},
 {{0xed,0x77,0xbb,0x66,0x8c,0x21,0x02,0x5a,0x04,0x00,0x80,0x1f,0x9f,0xc1,0x0c,0x00}}
};

static const struct SiS_CHTVRegData SiS310_CHTVReg_UPAL[] =
{
 {{0x41,0x7f,0xb7,0x34,0xad,0x50,0x34,0x83,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x41,0x7f,0xb7,0x80,0x85,0x50,0x00,0x83,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x41,0x7f,0xb7,0x34,0xad,0x50,0x34,0x83,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x41,0x7f,0xb7,0x12,0x85,0x50,0x00,0x83,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x61,0x7f,0xb7,0x99,0x84,0x35,0x04,0x5a,0x05,0x00,0x80,0x26,0x2a,0x55,0x5d,0x00}},
 {{0xc3,0x7f,0xb7,0x7a,0x84,0x40,0x02,0x5a,0x05,0x00,0x80,0x1f,0x84,0x3d,0x28,0x00}},
 {{0xe5,0x7f,0xb7,0x1d,0xa7,0x3e,0x04,0x5a,0x05,0x00,0x80,0x20,0x3e,0xe4,0x22,0x00}}
};

static const struct SiS_CHTVRegData SiS310_CHTVReg_OPAL[] =
{
 {{0x41,0x7f,0xb7,0x36,0xad,0x50,0x34,0x83,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x41,0x7f,0xb7,0x86,0x85,0x50,0x00,0x83,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x41,0x7f,0xb7,0x36,0xad,0x50,0x34,0x83,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x41,0x7f,0xb7,0x86,0x85,0x50,0x00,0x83,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x61,0x7f,0xb7,0x99,0x84,0x35,0x04,0x5a,0x05,0x00,0x80,0x26,0x2a,0x55,0x5d,0x00}},
 {{0xc1,0x7f,0xb7,0x4d,0x8c,0x1e,0x31,0x5a,0x05,0x00,0x80,0x26,0x78,0x19,0x34,0x00}},
 {{0xe4,0x7f,0xb7,0x1e,0xaf,0x29,0x37,0x5a,0x05,0x00,0x80,0x25,0x8c,0xb2,0x2a,0x00}}
};

static const struct SiS_CHTVRegData SiS310_CHTVReg_UPALM[] =
{
 {{0x52,0x77,0xbb,0x94,0x84,0x48,0xfe,0x83,0x04,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x52,0x77,0xbb,0x94,0x84,0x48,0xfe,0x83,0x04,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x52,0x77,0xbb,0x94,0x84,0x48,0xfe,0x83,0x04,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x52,0x77,0xbb,0x94,0x84,0x48,0xfe,0x83,0x04,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x72,0x77,0xfb,0x6e,0x84,0x2e,0x02,0x83,0x04,0x00,0x80,0x20,0x76,0xdb,0x6e,0x00}},
 {{0xd7,0x77,0xf7,0xc8,0x84,0x3b,0x02,0x83,0x04,0x00,0x80,0x19,0x84,0x0a,0xc7,0x00}},
 {{0xf6,0x77,0xfb,0x66,0x87,0x32,0x01,0x83,0x04,0x00,0x80,0x1b,0xdc,0xb0,0x8d,0x00}}
#if 0 /* Correct blacklevel and CFRB */
 {{0x72,0x77,0xbb,0x6e,0x84,0x2e,0x02,0x5a,0x04,0x00,0x80,0x20,0x76,0xdb,0x6e,0x00}},
 {{0xd7,0x77,0xb7,0xc8,0x84,0x3b,0x02,0x5a,0x04,0x00,0x80,0x19,0x84,0x0a,0xc7,0x00}},
 {{0xf6,0x77,0xbb,0x66,0x87,0x32,0x01,0x5a,0x04,0x00,0x80,0x1b,0xdc,0xb0,0x8d,0x00}}
#endif
};

static const struct SiS_CHTVRegData SiS310_CHTVReg_OPALM[] =
{
 {{0x51,0x77,0xbb,0x7b,0x84,0x34,0x00,0x83,0x04,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x51,0x77,0xbb,0x7b,0x84,0x34,0x00,0x83,0x04,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x51,0x77,0xbb,0x7b,0x84,0x34,0x00,0x83,0x04,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x51,0x77,0xbb,0x7b,0x84,0x34,0x00,0x83,0x04,0x00,0x80,0x00,0x00,0x00,0x00,0x01}},
 {{0x71,0x77,0xfb,0x6e,0x84,0x1e,0x00,0x83,0x04,0x00,0x80,0x25,0x1a,0x1f,0x59,0x00}},
 {{0xd6,0x77,0xf7,0xb6,0x83,0x2c,0x02,0x83,0x04,0x00,0x80,0x1b,0xf8,0x1f,0x82,0x00}},
 {{0xf5,0x77,0xfb,0x66,0x8c,0x21,0x02,0x83,0x04,0x00,0x80,0x1f,0x58,0x46,0x9f,0x00}}
#if 0 /* Correct blacklevel and CFRB */
 {{0x71,0x77,0xbb,0x6e,0x84,0x1e,0x00,0x5a,0x04,0x00,0x80,0x25,0x1a,0x1f,0x59,0x00}},
 {{0xd6,0x77,0xb7,0xb6,0x83,0x2c,0x02,0x5a,0x04,0x00,0x80,0x1b,0xf8,0x1f,0x82,0x00}},
 {{0xf5,0x77,0xbb,0x66,0x8c,0x21,0x02,0x5a,0x04,0x00,0x80,0x1f,0x58,0x46,0x9f,0x00}}
#endif
};

static const struct SiS_CHTVRegData SiS310_CHTVReg_UPALN[] =
{
 {{0x41,0x7f,0xb7,0x34,0xad,0x50,0x34,0x6e,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x03}},
 {{0x41,0x7f,0xb7,0x80,0x85,0x50,0x00,0x6e,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x03}},
 {{0x41,0x7f,0xb7,0x34,0xad,0x50,0x34,0x6e,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x03}},
 {{0x41,0x7f,0xb7,0x12,0x85,0x50,0x00,0x6e,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x03}},
 {{0x61,0x7f,0xb7,0x99,0x84,0x35,0x04,0x6e,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x03}},
 {{0xc3,0x7f,0xb7,0x7a,0x84,0x40,0x02,0x6e,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x03}},
 {{0xe5,0x7f,0xb7,0x1d,0xa7,0x3e,0x04,0x6e,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x03}}
#if 0 /* Correct blacklevel, CIV and CFRB */
 {{0x61,0x7f,0xb7,0x99,0x84,0x35,0x04,0x5a,0x05,0x00,0x80,0x1f,0x0d,0x54,0x5e,0x00}},
 {{0xc3,0x7f,0xb7,0x7a,0x84,0x40,0x02,0x5a,0x05,0x00,0x80,0x19,0x78,0xef,0x35,0x00}},
 {{0xe5,0x7f,0xb7,0x1d,0xa7,0x3e,0x04,0x5a,0x05,0x00,0x80,0x1a,0x33,0x3f,0x2f,0x00}}
#endif
};

static const struct SiS_CHTVRegData SiS310_CHTVReg_OPALN[] =
{
 {{0x41,0x7f,0xb7,0x36,0xad,0x50,0x34,0x6e,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x03}},
 {{0x41,0x7f,0xb7,0x86,0x85,0x50,0x00,0x6e,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x03}},
 {{0x41,0x7f,0xb7,0x36,0xad,0x50,0x34,0x6e,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x03}},
 {{0x41,0x7f,0xb7,0x86,0x85,0x50,0x00,0x6e,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x03}},
 {{0x61,0x7f,0xb7,0x99,0x84,0x35,0x04,0x6e,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x03}},
 {{0xc1,0x7f,0xb7,0x4d,0x8c,0x1e,0x31,0x6e,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x03}},
 {{0xe4,0x7f,0xb7,0x1e,0xaf,0x29,0x37,0x6e,0x05,0x00,0x80,0x00,0x00,0x00,0x00,0x03}}
#if 0 /* Correct blacklevel, CIV and CFRB */
 {{0x61,0x7f,0xb7,0x99,0x84,0x35,0x04,0x5a,0x05,0x00,0x80,0x1f,0x0d,0x54,0x5e,0x00}},
 {{0xc1,0x7f,0xb7,0x4d,0x8c,0x1e,0x31,0x5a,0x05,0x00,0x80,0x1f,0x15,0xc0,0x1e,0x00}},
 {{0xe4,0x7f,0xb7,0x1e,0xaf,0x29,0x37,0x5a,0x05,0x00,0x80,0x1d,0xf1,0x6c,0xcb,0x00}}
#endif
};

static const unsigned char SiS310_CHTVVCLKUNTSC[] = { 0x41,0x41,0x41,0x41,0x42,0x46,0x53 };
static const unsigned char SiS310_CHTVVCLKONTSC[] = { 0x48,0x48,0x48,0x48,0x45,0x43,0x51 };

static const unsigned char SiS310_CHTVVCLKUPAL[]  = { 0x47,0x47,0x47,0x47,0x48,0x4a,0x54 };
static const unsigned char SiS310_CHTVVCLKOPAL[]  = { 0x47,0x47,0x47,0x47,0x48,0x4f,0x52 };

static const unsigned char SiS310_CHTVVCLKUPALM[] = { 0x41,0x41,0x41,0x41,0x42,0x46,0x53 };
static const unsigned char SiS310_CHTVVCLKOPALM[] = { 0x48,0x48,0x48,0x48,0x45,0x43,0x51 };

static const unsigned char SiS310_CHTVVCLKUPALN[] = { 0x47,0x47,0x47,0x47,0x48,0x4a,0x54 };
static const unsigned char SiS310_CHTVVCLKOPALN[] = { 0x47,0x47,0x47,0x47,0x48,0x4f,0x52 };


