/* $Id: stdio.c,v 1.1.1.1 2006/09/14 01:59:06 root Exp $ */

/*
 * Copyright (c) 2000-2002 Opsycon AB  (www.opsycon.se)
 * 
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *	This product includes software developed by Opsycon AB.
 * 4. The name of the author may not be used to endorse or promote products
 *    derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 */
#include <string.h>
#include <stdio.h>
#include <fcntl.h>

/*************************************************************
 *
 *   fprintf --\    putchar ------ putc --\
 *             |                          |
 *    printf --+-- vfprintf --+-- fputs --+-- write
 *                            |           |
 *                            puts --/  fwrite --/
 *
 *
 *  getchar ----- getc --+-- fgetc ----- read
 *                      |
 *     gets ---- fgets --/
 *
 *************************************************************/

FILE            _iob[OPEN_MAX] =
{
    {0, 1},
    {1, 1},
    {2, 1},
    {3, 1},
    {4, 1},
};

FILE *
fopen(const char *fname, const char *mode)
{
	int i, fd, flags;

	for (i = 0; i < OPEN_MAX && _iob[i].valid; i++);
	if (i == OPEN_MAX)
		return (0);
	if (mode == 0)
		flags = O_RDONLY;
	else if (!strcmp(mode, "r"))
		flags = O_RDONLY;
	else if (!strcmp(mode, "w"))
		flags = O_WRONLY;
	else if (!strcmp(mode, "r+"))
		flags = O_RDWR;
	fd = open (fname, flags, 0);
	if (fd == -1)
		return (0);
	_iob[i].fd = fd;
	_iob[i].valid = 1;
	return (&_iob[i]);
}
