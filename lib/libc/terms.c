/* $Id: terms.c,v 1.1.1.1 2006/09/14 01:59:06 root Exp $ */

/*
 * Copyright (c) 2000-2002 Opsycon AB  (www.opsycon.se)
 * 
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *	This product includes software developed by Opsycon AB.
 * 4. The name of the author may not be used to endorse or promote products
 *    derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 */
#include <stdio.h>
#include <termio.h>

#if 0
int
tvi920 (fd, op, a1, a2)
     int             fd, op, a1, a2;
{
    switch (op) {
    case TT_CM:
	printf ("\033=%c%c", a2 + ' ', a1 + ' ');
	break;
    case TT_CLR:
	printf ("\032");
	break;
    case TT_CUROFF:
	printf ("\033.0");
	break;
    case TT_CURON:
	printf ("\033.2");
	break;
    default:
	return (-1);
    }
    return (0);
}
#endif

int
vt100 (fd, op, a1, a2)
     int             fd, op, a1, a2;
{
    switch (op) {
    case TT_CM:
	printf ("\033[%d;%dH", a2 + 1, a1 + 1);
	break;
    case TT_CLR:
	printf ("\033[H\033[J");
	break;
    case TT_CUROFF:
	printf ("\033[?25l");
	break;
    case TT_CURON:
	printf ("\033[?25h");
	break;
    default:
	return (-1);
    }
    return (0);
}
