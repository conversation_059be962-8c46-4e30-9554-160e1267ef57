/********************************************************************************/
/*										*/
/*			This file contains extra TCM2B structures		*/
/*			     Written by <PERSON>				*/
/*		       IBM Thomas J. Watson Research Center			*/
/*            $Id: TCMB.h 1521 2019-11-15 21:00:47Z kgoldman $			*/
/*										*/
/*  Licenses and Notices							*/
/*										*/
/*  1. Copyright Licenses:							*/
/*										*/
/*  - Trusted Computing Group (TCG) grants to the user of the source code in	*/
/*    this specification (the "Source Code") a worldwide, irrevocable, 		*/
/*    nonexclusive, royalty free, copyright license to reproduce, create 	*/
/*    derivative works, distribute, display and perform the Source Code and	*/
/*    derivative works thereof, and to grant others the rights granted herein.	*/
/*										*/
/*  - The TCG grants to the user of the other parts of the specification 	*/
/*    (other than the Source Code) the rights to reproduce, distribute, 	*/
/*    display, and perform the specification solely for the purpose of 		*/
/*    developing products based on such documents.				*/
/*										*/
/*  2. Source Code Distribution Conditions:					*/
/*										*/
/*  - Redistributions of Source Code must retain the above copyright licenses, 	*/
/*    this list of conditions and the following disclaimers.			*/
/*										*/
/*  - Redistributions in binary form must reproduce the above copyright 	*/
/*    licenses, this list of conditions	and the following disclaimers in the 	*/
/*    documentation and/or other materials provided with the distribution.	*/
/*										*/
/*  3. Disclaimers:								*/
/*										*/
/*  - THE COPYRIGHT LICENSES SET FORTH ABOVE DO NOT REPRESENT ANY FORM OF	*/
/*  LICENSE OR WAIVER, EXPRESS OR IMPLIED, BY ESTOPPEL OR OTHERWISE, WITH	*/
/*  RESPECT TO PATENT RIGHTS HELD BY TCG MEMBERS (OR OTHER THIRD PARTIES)	*/
/*  THAT MAY BE NECESSARY TO IMPLEMENT THIS SPECIFICATION OR OTHERWISE.		*/
/*  Contact TCG Administration (<EMAIL>) for 		*/
/*  information on specification licensing rights available through TCG 	*/
/*  membership agreements.							*/
/*										*/
/*  - THIS SPECIFICATION IS PROVIDED "AS IS" WITH NO EXPRESS OR IMPLIED 	*/
/*    WARRANTIES WHATSOEVER, INCLUDING ANY WARRANTY OF MERCHANTABILITY OR 	*/
/*    FITNESS FOR A PARTICULAR PURPOSE, ACCURACY, COMPLETENESS, OR 		*/
/*    NONINFRINGEMENT OF INTELLECTUAL PROPERTY RIGHTS, OR ANY WARRANTY 		*/
/*    OTHERWISE ARISING OUT OF ANY PROPOSAL, SPECIFICATION OR SAMPLE.		*/
/*										*/
/*  - Without limitation, TCG and its members and licensors disclaim all 	*/
/*    liability, including liability for infringement of any proprietary 	*/
/*    rights, relating to use of information in this specification and to the	*/
/*    implementation of this specification, and TCG disclaims all liability for	*/
/*    cost of procurement of substitute goods or services, lost profits, loss 	*/
/*    of use, loss of data or any incidental, consequential, direct, indirect, 	*/
/*    or special damages, whether under contract, tort, warranty or otherwise, 	*/
/*    arising in any way out of use or reliance upon this specification or any 	*/
/*    information herein.							*/
/*										*/
/*  (c) Copyright IBM Corp. and others, 2016 - 2019				*/
/*										*/
/********************************************************************************/

#ifndef TCMB_H
#define TCMB_H

#include<TcmBaseTypes.h>
/* 5.17	TCMB.h */
/* This file contains extra TCM2B structures */

/* TCM2B Types */
typedef struct {
    UINT16          size;
    BYTE            buffer[1];
} TCM2B, *P2B;
typedef const TCM2B     *PC2B;

/* This macro helps avoid having to type in the structure in order to create a new TCM2B type that
   is used in a function. */

#define TCM2B_TYPE(name, bytes)			    \
    typedef union {				    \
	struct  {					    \
	    UINT16  size;				    \
	    BYTE    buffer[(bytes)];			    \
	} t;						    \
	TCM2B   b;					    \
    } TCM2B_##name

/* This macro defines a TCM2B with a constant character value. This macro sets the size of the
   string to the size minus the terminating zero byte. This lets the user of the label add their
   terminating 0. This method is chosen so that existing code that provides a label will continue to
   work correctly. Macro to instance and initialize a TCM2B value */
#define TCM2B_INIT(TYPE, name)					\
    TCM2B_##TYPE    name = {sizeof(name.t.buffer), {0}}
#define TCM2B_BYTE_VALUE(bytes) TCM2B_TYPE(bytes##_BYTE_VALUE, bytes)

#endif


