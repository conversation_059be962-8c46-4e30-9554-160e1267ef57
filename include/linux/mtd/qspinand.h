
#ifndef __LINUX_MTD_QSPI_NAND_H
#define __LINUX_MTD_QSPI_NAND_H
typedef unsigned int   u32;
typedef unsigned long long   u64;
typedef unsigned short u16;
typedef unsigned char  u8;
#define true 1 
enum spi_mem_data_dir {
        SPI_MEM_DATA_IN,
        SPI_MEM_DATA_OUT,
};


struct spi_mem_op {
        struct {
                u8 buswidth;
                u8 opcode;
        } cmd;

        struct {
                u8 nbytes;
                u8 buswidth;
                u64 val;
        } addr;

        struct {
                u8 nbytes;
                u8 buswidth;
        } dummy;

        struct {
                u8 buswidth;
                enum spi_mem_data_dir dir;
                unsigned int nbytes;
                union {
                        void *in;
                        const void *out;
                } buf;
        } data;
};
extern int qspi_exec_mem_op( const struct spi_mem_op *op);
#if 0
struct spi_transfer {
        /* it's ok if tx_buf == rx_buf (right?)
         * for MicroWire, one buffer must be null
         * buffers must work with dma_*map_single() calls, unless
         *   spi_message.is_dma_mapped reports a pre-existing mapping
         */
        const void      *tx_buf;
        void            *rx_buf;
        unsigned        len;


        unsigned        cs_change:1;
        unsigned        tx_nbits:3;
        unsigned        rx_nbits:3;
#define SPI_NBITS_SINGLE        0x01 /* 1bit transfer */
#define SPI_NBITS_DUAL          0x02 /* 2bits transfer */
#define SPI_NBITS_QUAD          0x04 /* 4bits transfer */
        u8              bits_per_word;
        u16             delay_usecs;
        u32             speed_hz;
};
#endif

#define SPI_MEM_OP_CMD(__opcode, __buswidth)                    \
        {                                                       \
                .buswidth = __buswidth,                         \
                .opcode = __opcode,                             \
        }

#define SPI_MEM_OP_ADDR(__nbytes, __val, __buswidth)            \
        {                                                       \
                .nbytes = __nbytes,                             \
                .val = __val,                                   \
                .buswidth = __buswidth,                         \
        }

#define SPI_MEM_OP_NO_ADDR      { }

#define SPI_MEM_OP_DUMMY(__nbytes, __buswidth)                  \
        {                                                       \
                .nbytes = __nbytes,                             \
                .buswidth = __buswidth,                         \
        }

#define SPI_MEM_OP_NO_DUMMY     { }

#define SPI_MEM_OP_DATA_IN(__nbytes, __buf, __buswidth)         \
        {                                                       \
                .dir = SPI_MEM_DATA_IN,                         \
                .nbytes = __nbytes,                             \
                .buf.in = __buf,                                \
                .buswidth = __buswidth,                         \
        }

#define SPI_MEM_OP_DATA_OUT(__nbytes, __buf, __buswidth)        \
        {                                                       \
                .dir = SPI_MEM_DATA_OUT,                        \
                .nbytes = __nbytes,                             \
                .buf.out = __buf,                               \
                .buswidth = __buswidth,                         \
        }

#define SPI_MEM_OP_NO_DATA      { }



#define SPI_MEM_OP(__cmd, __addr, __dummy, __data)              \
        {                                                       \
                .cmd = __cmd,                                   \
                .addr = __addr,                                 \
                .dummy = __dummy,                               \
                .data = __data,                                 \
        }
#define SPINAND_RESET_OP                                                \
        SPI_MEM_OP(SPI_MEM_OP_CMD(0xff, 1),                             \
                   SPI_MEM_OP_NO_ADDR,                                  \
                   SPI_MEM_OP_NO_DUMMY,                                 \
                   SPI_MEM_OP_NO_DATA)

#define SPINAND_WR_EN_DIS_OP(enable)                                    \
        SPI_MEM_OP(SPI_MEM_OP_CMD((enable) ? 0x06 : 0x04, 1),           \
                   SPI_MEM_OP_NO_ADDR,                                  \
                   SPI_MEM_OP_NO_DUMMY,                                 \
                   SPI_MEM_OP_NO_DATA)

#define SPINAND_READID_OP(ndummy, buf, len)                             \
        SPI_MEM_OP(SPI_MEM_OP_CMD(0x9f, 1),                             \
                   SPI_MEM_OP_NO_ADDR,                                  \
                   SPI_MEM_OP_DUMMY(ndummy, 1),                         \
                   SPI_MEM_OP_DATA_IN(len, buf, 1))

#define SPINAND_SET_FEATURE_OP(reg, valptr)                             \
        SPI_MEM_OP(SPI_MEM_OP_CMD(0x1f, 1),                             \
                   SPI_MEM_OP_ADDR(1, reg, 1),                          \
                   SPI_MEM_OP_NO_DUMMY,                                 \
                   SPI_MEM_OP_DATA_OUT(1, valptr, 1))

#define SPINAND_GET_FEATURE_OP(reg, valptr)                             \
        SPI_MEM_OP(SPI_MEM_OP_CMD(0x0f, 1),                             \
                   SPI_MEM_OP_ADDR(1, reg, 1),                          \
                   SPI_MEM_OP_NO_DUMMY,                                 \
                   SPI_MEM_OP_DATA_IN(1, valptr, 1))

#define SPINAND_BLK_ERASE_OP(addr)                                      \
        SPI_MEM_OP(SPI_MEM_OP_CMD(0xd8, 1),                             \
                   SPI_MEM_OP_ADDR(3, addr, 1),                         \
                   SPI_MEM_OP_NO_DUMMY,                                 \
                   SPI_MEM_OP_NO_DATA)

#define SPINAND_PAGE_READ_OP(addr)                                      \
        SPI_MEM_OP(SPI_MEM_OP_CMD(0x13, 1),                             \
                   SPI_MEM_OP_ADDR(3, addr, 1),                         \
                   SPI_MEM_OP_NO_DUMMY,                                 \
                   SPI_MEM_OP_NO_DATA)

#define SPINAND_PAGE_READ_FROM_CACHE_OP(fast, addr, ndummy, buf, len)   \
        SPI_MEM_OP(SPI_MEM_OP_CMD(fast ? 0x0b : 0x03, 1),               \
                   SPI_MEM_OP_ADDR(2, addr, 1),                         \
                   SPI_MEM_OP_DUMMY(ndummy, 1),                         \
                   SPI_MEM_OP_DATA_IN(len, buf, 1))

#define SPINAND_PAGE_READ_FROM_CACHE_OP_3A(fast, addr, ndummy, buf, len) \
        SPI_MEM_OP(SPI_MEM_OP_CMD(fast ? 0x0b : 0x03, 1),               \
                   SPI_MEM_OP_ADDR(3, addr, 1),                         \
                   SPI_MEM_OP_DUMMY(ndummy, 1),                         \
                   SPI_MEM_OP_DATA_IN(len, buf, 1))

#define SPINAND_PAGE_READ_FROM_CACHE_X2_OP(addr, ndummy, buf, len)      \
        SPI_MEM_OP(SPI_MEM_OP_CMD(0x3b, 1),                             \
                   SPI_MEM_OP_ADDR(2, addr, 1),                         \
                   SPI_MEM_OP_DUMMY(ndummy, 1),                         \
                   SPI_MEM_OP_DATA_IN(len, buf, 2))

#define SPINAND_PAGE_READ_FROM_CACHE_X2_OP_3A(addr, ndummy, buf, len)   \
        SPI_MEM_OP(SPI_MEM_OP_CMD(0x3b, 1),                             \
                   SPI_MEM_OP_ADDR(3, addr, 1),                         \
                   SPI_MEM_OP_DUMMY(ndummy, 1),                         \
                   SPI_MEM_OP_DATA_IN(len, buf, 2))

#define SPINAND_PAGE_READ_FROM_CACHE_X4_OP(addr, ndummy, buf, len)      \
        SPI_MEM_OP(SPI_MEM_OP_CMD(0x6b, 1),                             \
                   SPI_MEM_OP_ADDR(2, addr, 1),                         \
                   SPI_MEM_OP_DUMMY(ndummy, 1),                         \
                   SPI_MEM_OP_DATA_IN(len, buf, 4))

#define SPINAND_PAGE_READ_FROM_CACHE_X4_OP_3A(addr, ndummy, buf, len)   \
        SPI_MEM_OP(SPI_MEM_OP_CMD(0x6b, 1),                             \
                   SPI_MEM_OP_ADDR(3, addr, 1),                         \
                   SPI_MEM_OP_DUMMY(ndummy, 1),                         \
                   SPI_MEM_OP_DATA_IN(len, buf, 4))

#define SPINAND_PAGE_READ_FROM_CACHE_DUALIO_OP(addr, ndummy, buf, len)  \
        SPI_MEM_OP(SPI_MEM_OP_CMD(0xbb, 1),                             \
                   SPI_MEM_OP_ADDR(2, addr, 2),                         \
                   SPI_MEM_OP_DUMMY(ndummy, 2),                         \
                   SPI_MEM_OP_DATA_IN(len, buf, 2))

#define SPINAND_PAGE_READ_FROM_CACHE_DUALIO_OP_3A(addr, ndummy, buf, len) \
        SPI_MEM_OP(SPI_MEM_OP_CMD(0xbb, 1),                             \
                   SPI_MEM_OP_ADDR(3, addr, 2),                         \
                   SPI_MEM_OP_DUMMY(ndummy, 2),                         \
                   SPI_MEM_OP_DATA_IN(len, buf, 2))

#define SPINAND_PAGE_READ_FROM_CACHE_QUADIO_OP(addr, ndummy, buf, len)  \
        SPI_MEM_OP(SPI_MEM_OP_CMD(0xeb, 1),                             \
                   SPI_MEM_OP_ADDR(2, addr, 4),                         \
                   SPI_MEM_OP_DUMMY(ndummy, 4),                         \
                   SPI_MEM_OP_DATA_IN(len, buf, 4))

#define SPINAND_PAGE_READ_FROM_CACHE_QUADIO_OP_3A(addr, ndummy, buf, len) \
        SPI_MEM_OP(SPI_MEM_OP_CMD(0xeb, 1),                             \
                   SPI_MEM_OP_ADDR(3, addr, 4),                         \
                   SPI_MEM_OP_DUMMY(ndummy, 4),                         \
                   SPI_MEM_OP_DATA_IN(len, buf, 4))

#define SPINAND_PROG_EXEC_OP(addr)                                      \
        SPI_MEM_OP(SPI_MEM_OP_CMD(0x10, 1),                             \
                   SPI_MEM_OP_ADDR(3, addr, 1),                         \
                   SPI_MEM_OP_NO_DUMMY,                                 \
                   SPI_MEM_OP_NO_DATA)

#define SPINAND_PROG_LOAD(reset, addr, buf, len)                        \
        SPI_MEM_OP(SPI_MEM_OP_CMD(reset ? 0x02 : 0x84, 1),              \
                   SPI_MEM_OP_ADDR(2, addr, 1),                         \
                   SPI_MEM_OP_NO_DUMMY,                                 \
                   SPI_MEM_OP_DATA_OUT(len, buf, 1))

#define SPINAND_PROG_LOAD_X4(reset, addr, buf, len)                     \
        SPI_MEM_OP(SPI_MEM_OP_CMD(reset ? 0x32 : 0x34, 1),              \
                   SPI_MEM_OP_ADDR(2, addr, 1),                         \
                   SPI_MEM_OP_NO_DUMMY,                                 \
                   SPI_MEM_OP_DATA_OUT(len, buf, 4))


#endif
