/*	$Id: crc32.h,v 1.2 2006/09/09 10:15:05 pefo Exp $	*/
/*
 * Copyright (c) 2005-2006 Opsycon AB  (www.opsycon.se)
 * 
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 */

#ifndef __CRC32_H__
#define __CRC32_H__

#include <sys/types.h>

u_int	crc32_generate(void *, size_t);
u_int	crc32_generate_seg(void *, size_t, u_int);
u_int	crc32_check(void *, size_t);

/* Helper functions, see build options */
#define	CRC32_CHECK_CRC		1
#define	CRC32_CHECK_SIG		2

#define	CRC32_OK		0
#define	CRC32_ERR_FILE		-1
#define	CRC32_ERR_CRC		-2
#define	CRC32_ERR_SIG		-3

int	crc32sig_check(const char *, u_int64_t, int);


#endif /* __CRC32_H__ */
