/* SPDX-License-Identifier: GPL-2.0+ */
/*
 * Copyright (c) 2013 The Chromium OS Authors.
 * Coypright (c) 2013 Guntermann & Drunck GmbH
 */
#ifndef __TCM_COMMON_H
#define __TCM_COMMON_H

#include <stdio.h>
#include <sys/linux/types.h>

#define RT_NULL                         (0)

/* RT-Thread error code definitions */
#define RT_EOK                          0               /**< There is no error */
#define RT_ERROR                        1               /**< A generic error happens */
#define RT_ETIMEOUT                     2               /**< Timed out */
#define RT_EFULL                        3               /**< The resource is full */
#define RT_EEMPTY                       4               /**< The resource is empty */
#define RT_ENOMEM                       5               /**< No memory */
#define RT_ENOSYS                       6               /**< No system */
#define RT_EBUSY                        7               /**< Busy */
#define RT_EIO                          8               /**< IO error */
#define RT_EINTR                        9               /**< Interrupted system call */
#define RT_EINVAL                       10              /**< Invalid argument */

/* boolean type definitions */
#define RT_TRUE                         1               /**< boolean true  */
#define RT_FALSE                        0               /**< boolean fails */

/**@}*/

/* maximum value of base type */
#define RT_UINT8_MAX                    0xff            /**< Maxium number of UINT8 */
#define RT_UINT16_MAX                   0xffff          /**< Maxium number of UINT16 */
#define RT_UINT32_MAX                   0xffffffff      /**< Maxium number of UINT32 */
#define RT_TICK_MAX                     RT_UINT32_MAX   /**< Maxium number of tick */

/* maximum value of ipc type */
#define RT_SEM_VALUE_MAX                RT_UINT16_MAX   /**< Maxium number of semaphore .value */
#define RT_MUTEX_VALUE_MAX              RT_UINT16_MAX   /**< Maxium number of mutex .value */
#define RT_MUTEX_HOLD_MAX               RT_UINT8_MAX    /**< Maxium number of mutex .hold */
#define RT_MB_ENTRY_MAX                 RT_UINT16_MAX   /**< Maxium number of mailbox .entry */
#define RT_MQ_ENTRY_MAX                 RT_UINT16_MAX   /**< Maxium number of message queue .entry */

enum tcm_duration {
	TCM_SHORT = 0,
	TCM_MEDIUM = 1,
	TCM_LONG = 2,
	TCM_UNDEFINED,
	TCM_DURATION_COUNT,
};

/*
 * Here is a partial implementation of TCM commands.  Please consult TCG Main
 * Specification for definitions of TCM commands.
 */

#define TCM_HEADER_SIZE		10

/* Max buffer size supported by our tcm */
#define TCM_DEV_BUFSIZE		1260

#define TCM_PCR_MINIMUM_DIGEST_SIZE 20

/**
 * enum tcm_version - The version of the TCM stack to be used
 * @TCM_V1:		Use TCM v1.x stack
 * @TCM_V2:		Use TCM v2.x stack
 */
enum tcm_version {
	TCM_V1 = 0,
	TCM_V2,
};

/**
 * tcm_open() - Request access to locality 0 for the caller
 *
 * After all commands have been completed the caller is supposed to
 * call tcm_close().
 *
 * @dev - TCM device
 * Returns 0 on success, -ve on failure.
 */
int tcm_open();

/**
 * tcm_close() - Close the current session
 *
 * Releasing the locked locality. Returns 0 on success, -ve 1 on
 * failure (in case lock removal did not succeed).
 *
 * @dev - TCM device
 * Returns 0 on success, -ve on failure.
 */
int tcm_close();

/**
 * tcm_clear_and_reenable() - Force clear the TCM and reenable it
 *
 * Return: 0 on success, -ve on failure
 */
u32 tcm_clear_and_reenable();

/**
 * tcm_get_desc() - Get a text description of the TCM
 *
 * @buf:	Buffer to put the string
 * @size:	Maximum size of buffer
 * Return: length of string, or -ENOSPC it no space
 */
int tcm_get_desc(char *buf, int size);

/**
 * tcm_report_state() - Collect information about the current TCM state
 *
 * @buf:	Buffer to put the string
 * @size:	Maximum size of buffer
 * Return: return code of the operation (0 = success)
 */
int tcm_report_state(char *buf, int size);

/**
 * tcm_xfer() - send data to the TCM and get response
 *
 * This first uses the device's send() method to send the bytes. Then it calls
 * recv() to get the reply. If recv() returns -EAGAIN then it will delay a
 * short time and then call recv() again.
 *
 * Regardless of whether recv() completes successfully, it will then call
 * cleanup() to finish the transaction.
 *
 * Note that the outgoing data is inspected to determine command type
 * (ordinal) and a timeout is used for that command type.
 *
 * @sendbuf - buffer of the data to send
 * @send_size size of the data to send
 * @recvbuf - memory to save the response to
 * @recv_len - pointer to the size of the response buffer
 *
 * Returns 0 on success (and places the number of response bytes at
 * recv_len) or -ve on failure.
 */
int tcm_xfer(const u8 *sendbuf, size_t send_size,
	     u8 *recvbuf, size_t *recv_size);

/**
 * Initialize TCM device.  It must be called before any TCM commands.
 *
 * Return: 0 on success, non-0 on error.
 */
int tcm_init();


/**
 * tcm_get_version() - Find the version of a TCM
 *
 * This checks the uclass data for a TCM device and returns the version number
 * it supports.
 *
 * Return: version number (TCM_V1 or TCMV2)
 */
enum tcm_version tcm_get_version();

#endif /* __TCM_COMMON_H */
