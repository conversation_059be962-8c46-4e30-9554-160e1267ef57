#ifndef TCMTYPES_H
#define TCMTYPES_H

#include <TcmBaseTypes.h>

/*TCG Algorithm Registry: Table 1:2 - Definition of TCM_ALG_ID Constants */

typedef UINT16 TCM_ALG_ID;
#define TYPE_OF_TCM_ALG_ID UINT16
#define ALG_ERROR_VALUE 0x0000
#define TCM_ALG_ERROR (TCM_ALG_ID)(ALG_ERROR_VALUE)
#define ALG_RSA_VALUE 0x0001
#define TCM_ALG_RSA (TCM_ALG_ID)(ALG_RSA_VALUE)
#define ALG_TDES_VALUE 0x0003
#define TCM_ALG_TDES (TCM_ALG_ID)(ALG_TDES_VALUE)
#define ALG_SHA_VALUE 0x0004
#define TCM_ALG_SHA (TCM_ALG_ID)(ALG_SHA_VALUE)
#define ALG_SHA1_VALUE 0x0004
#define TCM_ALG_SHA1 (TCM_ALG_ID)(ALG_SHA1_VALUE)
#define ALG_HMAC_VALUE 0x0005
#define TCM_ALG_HMAC (TCM_ALG_ID)(ALG_HMAC_VALUE)
#define ALG_AES_VALUE 0x0006
#define TCM_ALG_AES (TCM_ALG_ID)(ALG_AES_VALUE)
#define ALG_MGF1_VALUE 0x0007
#define TCM_ALG_MGF1 (TCM_ALG_ID)(ALG_MGF1_VALUE)
#define ALG_KEYEDHASH_VALUE 0x0008
#define TCM_ALG_KEYEDHASH (TCM_ALG_ID)(ALG_KEYEDHASH_VALUE)
#define ALG_XOR_VALUE 0x000A
#define TCM_ALG_XOR (TCM_ALG_ID)(ALG_XOR_VALUE)
#define ALG_SHA256_VALUE 0x000B
#define TCM_ALG_SHA256 (TCM_ALG_ID)(ALG_SHA256_VALUE)
#define ALG_SHA384_VALUE 0x000C
#define TCM_ALG_SHA384 (TCM_ALG_ID)(ALG_SHA384_VALUE)
#define ALG_SHA512_VALUE 0x000D
#define TCM_ALG_SHA512 (TCM_ALG_ID)(ALG_SHA512_VALUE)
#define ALG_NULL_VALUE 0x0010
#define TCM_ALG_NULL (TCM_ALG_ID)(ALG_NULL_VALUE)
#define ALG_SM3_256_VALUE 0x0012
#define TCM_ALG_SM3_256 (TCM_ALG_ID)(ALG_SM3_256_VALUE)
#define ALG_SM4_VALUE 0x0013
#define TCM_ALG_SM4 (TCM_ALG_ID)(ALG_SM4_VALUE)
#define ALG_RSASSA_VALUE 0x0014
#define TCM_ALG_RSASSA (TCM_ALG_ID)(ALG_RSASSA_VALUE)
#define ALG_RSAES_VALUE 0x0015
#define TCM_ALG_RSAES (TCM_ALG_ID)(ALG_RSAES_VALUE)
#define ALG_RSAPSS_VALUE 0x0016
#define TCM_ALG_RSAPSS (TCM_ALG_ID)(ALG_RSAPSS_VALUE)
#define ALG_OAEP_VALUE 0x0017
#define TCM_ALG_OAEP (TCM_ALG_ID)(ALG_OAEP_VALUE)
#define ALG_ECDSA_VALUE 0x0018
#define TCM_ALG_ECDSA (TCM_ALG_ID)(ALG_ECDSA_VALUE)
#define ALG_ECDH_VALUE 0x0019
#define TCM_ALG_ECDH (TCM_ALG_ID)(ALG_ECDH_VALUE)
#define ALG_ECDAA_VALUE 0x001A
#define TCM_ALG_ECDAA (TCM_ALG_ID)(ALG_ECDAA_VALUE)
#define ALG_SM2_VALUE 0x001B
#define TCM_ALG_SM2 (TCM_ALG_ID)(ALG_SM2_VALUE)
#define ALG_ECSCHNORR_VALUE 0x001C
#define TCM_ALG_ECSCHNORR (TCM_ALG_ID)(ALG_ECSCHNORR_VALUE)
#define ALG_ECMQV_VALUE 0x001D
#define TCM_ALG_ECMQV (TCM_ALG_ID)(ALG_ECMQV_VALUE)
#define ALG_KDF1_SP800_56A_VALUE 0x0020
#define TCM_ALG_KDF1_SP800_56A (TCM_ALG_ID)(ALG_KDF1_SP800_56A_VALUE)
#define ALG_KDF2_VALUE 0x0021
#define TCM_ALG_KDF2 (TCM_ALG_ID)(ALG_KDF2_VALUE)
#define ALG_KDF1_SP800_108_VALUE 0x0022
#define TCM_ALG_KDF1_SP800_108 (TCM_ALG_ID)(ALG_KDF1_SP800_108_VALUE)
#define ALG_ECC_VALUE 0x0023
#define TCM_ALG_ECC (TCM_ALG_ID)(ALG_ECC_VALUE)
#define ALG_SYMCIPHER_VALUE 0x0025
#define TCM_ALG_SYMCIPHER (TCM_ALG_ID)(ALG_SYMCIPHER_VALUE)
#define ALG_CAMELLIA_VALUE 0x0026
#define TCM_ALG_CAMELLIA (TCM_ALG_ID)(ALG_CAMELLIA_VALUE)
#define ALG_SHA3_256_VALUE 0x0027
#define TCM_ALG_SHA3_256 (TCM_ALG_ID)(ALG_SHA3_256_VALUE)
#define ALG_SHA3_384_VALUE 0x0028
#define TCM_ALG_SHA3_384 (TCM_ALG_ID)(ALG_SHA3_384_VALUE)
#define ALG_SHA3_512_VALUE 0x0029
#define TCM_ALG_SHA3_512 (TCM_ALG_ID)(ALG_SHA3_512_VALUE)
#define ALG_CMAC_VALUE 0x003F
#define TCM_ALG_CMAC (TCM_ALG_ID)(ALG_CMAC_VALUE)
#define ALG_CTR_VALUE 0x0040
#define TCM_ALG_CTR (TCM_ALG_ID)(ALG_CTR_VALUE)
#define ALG_OFB_VALUE 0x0041
#define TCM_ALG_OFB (TCM_ALG_ID)(ALG_OFB_VALUE)
#define ALG_CBC_VALUE 0x0042
#define TCM_ALG_CBC (TCM_ALG_ID)(ALG_CBC_VALUE)
#define ALG_CFB_VALUE 0x0043
#define TCM_ALG_CFB (TCM_ALG_ID)(ALG_CFB_VALUE)
#define ALG_ECB_VALUE 0x0044
#define TCM_ALG_ECB (TCM_ALG_ID)(ALG_ECB_VALUE)


/*Values derived from Table 1:2 */
#define ALG_FIRST_VALUE 0x0001
#define TCM_ALG_FIRST (TCM_ALG_ID)(ALG_FIRST_VALUE)
#define ALG_LAST_VALUE 0x0044
#define TCM_ALG_LAST (TCM_ALG_ID)(ALG_LAST_VALUE)


/*TCG Algorithm Registry: Table 1:3 - Definition of TCM_ECC_CURVE Constants */
typedef UINT16 TCM_ECC_CURVE;
#define TYPE_OF_TCM_ECC_CURVE UINT16
#define TCM_ECC_NONE (TCM_ECC_CURVE)(0x0000)
#define TCM_ECC_NIST_P192 (TCM_ECC_CURVE)(0x0001)
#define TCM_ECC_NIST_P224 (TCM_ECC_CURVE)(0x0002)
#define TCM_ECC_NIST_P256 (TCM_ECC_CURVE)(0x0003)
#define TCM_ECC_NIST_P384 (TCM_ECC_CURVE)(0x0004)
#define TCM_ECC_NIST_P521 (TCM_ECC_CURVE)(0x0005)
#define TCM_ECC_BN_P256 (TCM_ECC_CURVE)(0x0010)
#define TCM_ECC_BN_P638 (TCM_ECC_CURVE)(0x0011)
#define TCM_ECC_SM2_P256 (TCM_ECC_CURVE)(0x0020)


/*TCM 2.0 Part 2: Table 2:12 - Definition of TCM_CC Constants */
typedef UINT32 TCM_CC;
#define TYPE_OF_TCM_CC UINT32
#define TCM_CC_NV_UndefineSpaceSpecial (TCM_CC)(0x0000011F)
#define TCM_CC_EvictControl (TCM_CC)(0x00000120)
#define TCM_CC_HierarchyControl (TCM_CC)(0x00000121)
#define TCM_CC_NV_UndefineSpace (TCM_CC)(0x00000122)
#define TCM_CC_ChangeEPS (TCM_CC)(0x00000124)
#define TCM_CC_ChangePPS (TCM_CC)(0x00000125)
#define TCM_CC_Clear (TCM_CC)(0x00000126)
#define TCM_CC_ClearControl (TCM_CC)(0x00000127)
#define TCM_CC_ClockSet (TCM_CC)(0x00000128)
#define TCM_CC_HierarchyChangeAuth (TCM_CC)(0x00000129)
#define TCM_CC_NV_DefineSpace (TCM_CC)(0x0000012A)
#define TCM_CC_PCR_Allocate (TCM_CC)(0x0000012B)
#define TCM_CC_PCR_SetAuthPolicy (TCM_CC)(0x0000012C)
#define TCM_CC_PP_Commands (TCM_CC)(0x0000012D)
#define TCM_CC_SetPrimaryPolicy (TCM_CC)(0x0000012E)
#define TCM_CC_FieldUpgradeStart (TCM_CC)(0x0000012F)
#define TCM_CC_ClockRateAdjust (TCM_CC)(0x00000130)
#define TCM_CC_CreatePrimary (TCM_CC)(0x00000131)
#define TCM_CC_NV_GlobalWriteLock (TCM_CC)(0x00000132)
#define TCM_CC_GetCommandAuditDigest (TCM_CC)(0x00000133)
#define TCM_CC_NV_Increment (TCM_CC)(0x00000134)
#define TCM_CC_NV_SetBits (TCM_CC)(0x00000135)
#define TCM_CC_NV_Extend (TCM_CC)(0x00000136)
#define TCM_CC_NV_Write (TCM_CC)(0x00000137)
#define TCM_CC_NV_WriteLock (TCM_CC)(0x00000138)
#define TCM_CC_DictionaryAttackLockReset (TCM_CC)(0x00000139)
#define TCM_CC_DictionaryAttackParameters (TCM_CC)(0x0000013A)
#define TCM_CC_NV_ChangeAuth (TCM_CC)(0x0000013B)
#define TCM_CC_PCR_Event (TCM_CC)(0x0000013C)
#define TCM_CC_PCR_Reset (TCM_CC)(0x0000013D)
#define TCM_CC_SequenceComplete (TCM_CC)(0x0000013E)
#define TCM_CC_SetAlgorithmSet (TCM_CC)(0x0000013F)
#define TCM_CC_SetCommandCodeAuditStatus (TCM_CC)(0x00000140)
#define TCM_CC_FieldUpgradeData (TCM_CC)(0x00000141)
#define TCM_CC_IncrementalSelfTest (TCM_CC)(0x00000142)
#define TCM_CC_SelfTest (TCM_CC)(0x00000143)
#define TCM_CC_Startup (TCM_CC)(0x00000144)
#define TCM_CC_Shutdown (TCM_CC)(0x00000145)
#define TCM_CC_StirRandom (TCM_CC)(0x00000146)
#define TCM_CC_ActivateCredential (TCM_CC)(0x00000147)
#define TCM_CC_Certify (TCM_CC)(0x00000148)
#define TCM_CC_PolicyNV (TCM_CC)(0x00000149)
#define TCM_CC_CertifyCreation (TCM_CC)(0x0000014A)
#define TCM_CC_Duplicate (TCM_CC)(0x0000014B)
#define TCM_CC_GetTime (TCM_CC)(0x0000014C)
#define TCM_CC_GetSessionAuditDigest (TCM_CC)(0x0000014D)
#define TCM_CC_NV_Read (TCM_CC)(0x0000014E)
#define TCM_CC_NV_ReadLock (TCM_CC)(0x0000014F)
#define TCM_CC_ObjectChangeAuth (TCM_CC)(0x00000150)
#define TCM_CC_PolicySecret (TCM_CC)(0x00000151)
#define TCM_CC_Rewrap (TCM_CC)(0x00000152)
#define TCM_CC_Create (TCM_CC)(0x00000153)
#define TCM_CC_ECDH_ZGen (TCM_CC)(0x00000154)
#define TCM_CC_HMAC (TCM_CC)(0x00000155)
#define TCM_CC_MAC (TCM_CC)(0x00000155)
#define TCM_CC_Import (TCM_CC)(0x00000156)
#define TCM_CC_Load (TCM_CC)(0x00000157)
#define TCM_CC_Quote (TCM_CC)(0x00000158)
#define TCM_CC_RSA_Decrypt (TCM_CC)(0x00000159)
#define TCM_CC_HMAC_Start (TCM_CC)(0x0000015B)
#define TCM_CC_MAC_Start (TCM_CC)(0x0000015B)
#define TCM_CC_SequenceUpdate (TCM_CC)(0x0000015C)
#define TCM_CC_Sign (TCM_CC)(0x0000015D)
#define TCM_CC_Unseal (TCM_CC)(0x0000015E)
#define TCM_CC_PolicySigned (TCM_CC)(0x00000160)
#define TCM_CC_ContextLoad (TCM_CC)(0x00000161)
#define TCM_CC_ContextSave (TCM_CC)(0x00000162)
#define TCM_CC_ECDH_KeyGen (TCM_CC)(0x00000163)
#define TCM_CC_EncryptDecrypt (TCM_CC)(0x00000164)
#define TCM_CC_FlushContext (TCM_CC)(0x00000165)
#define TCM_CC_LoadExternal (TCM_CC)(0x00000167)
#define TCM_CC_MakeCredential (TCM_CC)(0x00000168)
#define TCM_CC_NV_ReadPublic (TCM_CC)(0x00000169)
#define TCM_CC_PolicyAuthorize (TCM_CC)(0x0000016A)
#define TCM_CC_PolicyAuthValue (TCM_CC)(0x0000016B)
#define TCM_CC_PolicyCommandCode (TCM_CC)(0x0000016C)
#define TCM_CC_PolicyCounterTimer (TCM_CC)(0x0000016D)
#define TCM_CC_PolicyCpHash (TCM_CC)(0x0000016E)
#define TCM_CC_PolicyLocality (TCM_CC)(0x0000016F)
#define TCM_CC_PolicyNameHash (TCM_CC)(0x00000170)
#define TCM_CC_PolicyOR (TCM_CC)(0x00000171)
#define TCM_CC_PolicyTicket (TCM_CC)(0x00000172)
#define TCM_CC_ReadPublic (TCM_CC)(0x00000173)
#define TCM_CC_RSA_Encrypt (TCM_CC)(0x00000174)
#define TCM_CC_StartAuthSession (TCM_CC)(0x00000176)
#define TCM_CC_VerifySignature (TCM_CC)(0x00000177)
#define TCM_CC_ECC_Parameters (TCM_CC)(0x00000178)
#define TCM_CC_FirmwareRead (TCM_CC)(0x00000179)
#define TCM_CC_GetCapability (TCM_CC)(0x0000017A)
#define TCM_CC_GetRandom (TCM_CC)(0x0000017B)
#define TCM_CC_GetTestResult (TCM_CC)(0x0000017C)
#define TCM_CC_Hash (TCM_CC)(0x0000017D)
#define TCM_CC_PCR_Read (TCM_CC)(0x0000017E)
#define TCM_CC_PolicyPCR (TCM_CC)(0x0000017F)
#define TCM_CC_PolicyRestart (TCM_CC)(0x00000180)
#define TCM_CC_ReadClock (TCM_CC)(0x00000181)
#define TCM_CC_PCR_Extend (TCM_CC)(0x00000182)
#define TCM_CC_PCR_SetAuthValue (TCM_CC)(0x00000183)
#define TCM_CC_NV_Certify (TCM_CC)(0x00000184)
#define TCM_CC_EventSequenceComplete (TCM_CC)(0x00000185)
#define TCM_CC_HashSequenceStart (TCM_CC)(0x00000186)
#define TCM_CC_PolicyPhysicalPresence (TCM_CC)(0x00000187)
#define TCM_CC_PolicyDuplicationSelect (TCM_CC)(0x00000188)
#define TCM_CC_PolicyGetDigest (TCM_CC)(0x00000189)
#define TCM_CC_TestParms (TCM_CC)(0x0000018A)
#define TCM_CC_Commit (TCM_CC)(0x0000018B)
#define TCM_CC_PolicyPassword (TCM_CC)(0x0000018C)
#define TCM_CC_ZGen_2Phase (TCM_CC)(0x0000018D)
#define TCM_CC_EC_Ephemeral (TCM_CC)(0x0000018E)
#define TCM_CC_PolicyNvWritten (TCM_CC)(0x0000018F)
#define TCM_CC_PolicyTemplate (TCM_CC)(0x00000190)
#define TCM_CC_CreateLoaded (TCM_CC)(0x00000191)
#define TCM_CC_PolicyAuthorizeNV (TCM_CC)(0x00000192)
#define TCM_CC_EncryptDecrypt2 (TCM_CC)(0x00000193)
#define TCM_CC_AC_GetCapability (TCM_CC)(0x00000194)
#define TCM_CC_AC_Send (TCM_CC)(0x00000195)
#define TCM_CC_Policy_AC_SendSelect (TCM_CC)(0x00000196)
#define TCM_CC_CertifyX509 (TCM_CC)(0x00000197)
#define TCM_CC_ACT_SetTimeout (TCM_CC)(0x00000198)
#define TCM_CC_ECC_Encrypt (TCM_CC)(0x00000199)
#define TCM_CC_ECC_Decrypt (TCM_CC)(0x0000019A)
#define CC_VEND 0x20000000
#define TCM_CC_Vendor_TCG_Test (TCM_CC)(0x20000000)

#define NTC2_CC_PreConfig (TCM_CC)(0x20000211)
#define NTC2_CC_LockPreConfig (TCM_CC)(0x20000212)
#define NTC2_CC_GetConfig (TCM_CC)(0x20000213)


/*Table 2:5 - Definition of Types for Documentation Clarity */
typedef UINT32 TCM_ALGORITHM_ID;
#define TYPE_OF_TCM_ALGORITHM_ID UINT32
typedef UINT32 TCM_MODIFIER_INDICATOR;
#define TYPE_OF_TCM_MODIFIER_INDICATOR UINT32
typedef UINT32 TCM_AUTHORIZATION_SIZE;
#define TYPE_OF_TCM_AUTHORIZATION_SIZE UINT32
typedef UINT32 TCM_PARAMETER_SIZE;
#define TYPE_OF_TCM_PARAMETER_SIZE UINT32
typedef UINT16 TCM_KEY_SIZE;
#define TYPE_OF_TCM_KEY_SIZE UINT16
typedef UINT16 TCM_KEY_BITS;
#define TYPE_OF_TCM_KEY_BITS UINT16


/*Table 2:6 - Definition of TCM_SPEC Constants */
typedef UINT32 TCM_SPEC;
#define TYPE_OF_TCM_SPEC UINT32
#define SPEC_FAMILY 0x322E3000
#define TCM_SPEC_FAMILY (TCM_SPEC)(SPEC_FAMILY)
#define SPEC_LEVEL 00
#define TCM_SPEC_LEVEL (TCM_SPEC)(SPEC_LEVEL)
#define SPEC_VERSION 162
#define TCM_SPEC_VERSION (TCM_SPEC)(SPEC_VERSION)
#define SPEC_YEAR 2020
#define TCM_SPEC_YEAR (TCM_SPEC)(SPEC_YEAR)
#define SPEC_DAY_OF_YEAR 53
#define TCM_SPEC_DAY_OF_YEAR (TCM_SPEC)(SPEC_DAY_OF_YEAR)


/*Table 2:7 - Definition of TCM_CONSTANTS32 Constants */

typedef UINT32 TCM_CONSTANTS32;
#define TYPE_OF_TCM_CONSTANTS32 UINT32
#define TCM_GENERATED_VALUE (TCM_CONSTANTS32)(0xFF544347)
#define TCM_MAX_DERIVATION_BITS (TCM_CONSTANTS32)8192


/*Table 2:16 - Definition of TCM2_RC Constants */
typedef UINT32 TCM2_RC;
#define TYPE_OF_TCM2_RC UINT32
#define TCM2_RC_SUCCESS (TCM2_RC)(0x000)
#define TCM2_RC_BAD_TAG (TCM2_RC)(0x01E)
#define RC_VER1 (TCM2_RC)(0x100)
#define TCM2_RC_INITIALIZE (TCM2_RC)(RC_VER1 + 0x000)
#define TCM2_RC_FAILURE (TCM2_RC)(RC_VER1 + 0x001)
#define TCM2_RC_SEQUENCE (TCM2_RC)(RC_VER1 + 0x003)
#define TCM2_RC_PRIVATE (TCM2_RC)(RC_VER1 + 0x00B)
#define TCM2_RC_HMAC (TCM2_RC)(RC_VER1 + 0x019)
#define TCM2_RC_DISABLED (TCM2_RC)(RC_VER1 + 0x020)
#define TCM2_RC_EXCLUSIVE (TCM2_RC)(RC_VER1 + 0x021)
#define TCM2_RC_AUTH_TYPE (TCM2_RC)(RC_VER1 + 0x024)
#define TCM2_RC_AUTH_MISSING (TCM2_RC)(RC_VER1 + 0x025)
#define TCM2_RC_POLICY (TCM2_RC)(RC_VER1 + 0x026)
#define TCM2_RC_PCR (TCM2_RC)(RC_VER1 + 0x027)
#define TCM2_RC_PCR_CHANGED (TCM2_RC)(RC_VER1 + 0x028)
#define TCM2_RC_UPGRADE (TCM2_RC)(RC_VER1 + 0x02D)
#define TCM2_RC_TOO_MANY_CONTEXTS (TCM2_RC)(RC_VER1 + 0x02E)
#define TCM2_RC_AUTH_UNAVAILABLE (TCM2_RC)(RC_VER1 + 0x02F)
#define TCM2_RC_REBOOT (TCM2_RC)(RC_VER1 + 0x030)
#define TCM2_RC_UNBALANCED (TCM2_RC)(RC_VER1 + 0x031)
#define TCM2_RC_COMMAND_SIZE (TCM2_RC)(RC_VER1 + 0x042)
#define TCM2_RC_COMMAND_CODE (TCM2_RC)(RC_VER1 + 0x043)
#define TCM2_RC_AUTHSIZE (TCM2_RC)(RC_VER1 + 0x044)
#define TCM2_RC_AUTH_CONTEXT (TCM2_RC)(RC_VER1 + 0x045)
#define TCM2_RC_NV_RANGE (TCM2_RC)(RC_VER1 + 0x046)
#define TCM2_RC_NV_SIZE (TCM2_RC)(RC_VER1 + 0x047)
#define TCM2_RC_NV_LOCKED (TCM2_RC)(RC_VER1 + 0x048)
#define TCM2_RC_NV_AUTHORIZATION (TCM2_RC)(RC_VER1 + 0x049)
#define TCM2_RC_NV_UNINITIALIZED (TCM2_RC)(RC_VER1 + 0x04A)
#define TCM2_RC_NV_SPACE (TCM2_RC)(RC_VER1 + 0x04B)
#define TCM2_RC_NV_DEFINED (TCM2_RC)(RC_VER1 + 0x04C)
#define TCM2_RC_BAD_CONTEXT (TCM2_RC)(RC_VER1 + 0x050)
#define TCM2_RC_CPHASH (TCM2_RC)(RC_VER1 + 0x051)
#define TCM2_RC_PARENT (TCM2_RC)(RC_VER1 + 0x052)
#define TCM2_RC_NEEDS_TEST (TCM2_RC)(RC_VER1 + 0x053)
#define TCM2_RC_NO_RESULT (TCM2_RC)(RC_VER1 + 0x054)
#define TCM2_RC_SENSITIVE (TCM2_RC)(RC_VER1 + 0x055)
#define RC_MAX_FM0 (TCM2_RC)(RC_VER1 + 0x07F)
#define RC_FMT1 (TCM2_RC)(0x080)
#define TCM2_RC_ASYMMETRIC (TCM2_RC)(RC_FMT1 + 0x001)
#define TCM2_RCS_ASYMMETRIC (TCM2_RC)(RC_FMT1 + 0x001)
#define TCM2_RC_ATTRIBUTES (TCM2_RC)(RC_FMT1 + 0x002)
#define TCM2_RCS_ATTRIBUTES (TCM2_RC)(RC_FMT1 + 0x002)
#define TCM2_RC_HASH (TCM2_RC)(RC_FMT1 + 0x003)
#define TCM2_RCS_HASH (TCM2_RC)(RC_FMT1 + 0x003)
#define TCM2_RC_VALUE (TCM2_RC)(RC_FMT1 + 0x004)
#define TCM2_RCS_VALUE (TCM2_RC)(RC_FMT1 + 0x004)
#define TCM2_RC_HIERARCHY (TCM2_RC)(RC_FMT1 + 0x005)
#define TCM2_RCS_HIERARCHY (TCM2_RC)(RC_FMT1 + 0x005)
#define TCM2_RC_KEY_SIZE (TCM2_RC)(RC_FMT1 + 0x007)
#define TCM2_RCS_KEY_SIZE (TCM2_RC)(RC_FMT1 + 0x007)
#define TCM2_RC_MGF (TCM2_RC)(RC_FMT1 + 0x008)
#define TCM2_RCS_MGF (TCM2_RC)(RC_FMT1 + 0x008)
#define TCM2_RC_MODE (TCM2_RC)(RC_FMT1 + 0x009)
#define TCM2_RCS_MODE (TCM2_RC)(RC_FMT1 + 0x009)
#define TCM2_RC_TYPE (TCM2_RC)(RC_FMT1 + 0x00A)
#define TCM2_RCS_TYPE (TCM2_RC)(RC_FMT1 + 0x00A)
#define TCM2_RC_HANDLE (TCM2_RC)(RC_FMT1 + 0x00B)
#define TCM2_RCS_HANDLE (TCM2_RC)(RC_FMT1 + 0x00B)
#define TCM2_RC_KDF (TCM2_RC)(RC_FMT1 + 0x00C)
#define TCM2_RCS_KDF (TCM2_RC)(RC_FMT1 + 0x00C)
#define TCM2_RC_RANGE (TCM2_RC)(RC_FMT1 + 0x00D)
#define TCM2_RCS_RANGE (TCM2_RC)(RC_FMT1 + 0x00D)
#define TCM2_RC_AUTH_FAIL (TCM2_RC)(RC_FMT1 + 0x00E)
#define TCM2_RCS_AUTH_FAIL (TCM2_RC)(RC_FMT1 + 0x00E)
#define TCM2_RC_NONCE (TCM2_RC)(RC_FMT1 + 0x00F)
#define TCM2_RCS_NONCE (TCM2_RC)(RC_FMT1 + 0x00F)
#define TCM2_RC_PP (TCM2_RC)(RC_FMT1 + 0x010)
#define TCM2_RCS_PP (TCM2_RC)(RC_FMT1 + 0x010)
#define TCM2_RC_SCHEME (TCM2_RC)(RC_FMT1 + 0x012)
#define TCM2_RCS_SCHEME (TCM2_RC)(RC_FMT1 + 0x012)
#define TCM2_RC_SIZE (TCM2_RC)(RC_FMT1 + 0x015)
#define TCM2_RCS_SIZE (TCM2_RC)(RC_FMT1 + 0x015)
#define TCM2_RC_SYMMETRIC (TCM2_RC)(RC_FMT1 + 0x016)
#define TCM2_RCS_SYMMETRIC (TCM2_RC)(RC_FMT1 + 0x016)
#define TCM2_RC_TAG (TCM2_RC)(RC_FMT1 + 0x017)
#define TCM2_RCS_TAG (TCM2_RC)(RC_FMT1 + 0x017)
#define TCM2_RC_SELECTOR (TCM2_RC)(RC_FMT1 + 0x018)
#define TCM2_RCS_SELECTOR (TCM2_RC)(RC_FMT1 + 0x018)
#define TCM2_RC_INSUFFICIENT (TCM2_RC)(RC_FMT1 + 0x01A)
#define TCM2_RCS_INSUFFICIENT (TCM2_RC)(RC_FMT1 + 0x01A)
#define TCM2_RC_SIGNATURE (TCM2_RC)(RC_FMT1 + 0x01B)
#define TCM2_RCS_SIGNATURE (TCM2_RC)(RC_FMT1 + 0x01B)
#define TCM2_RC_KEY (TCM2_RC)(RC_FMT1 + 0x01C)
#define TCM2_RCS_KEY (TCM2_RC)(RC_FMT1 + 0x01C)
#define TCM2_RC_POLICY_FAIL (TCM2_RC)(RC_FMT1 + 0x01D)
#define TCM2_RCS_POLICY_FAIL (TCM2_RC)(RC_FMT1 + 0x01D)
#define TCM2_RC_INTEGRITY (TCM2_RC)(RC_FMT1 + 0x01F)
#define TCM2_RCS_INTEGRITY (TCM2_RC)(RC_FMT1 + 0x01F)
#define TCM2_RC_TICKET (TCM2_RC)(RC_FMT1 + 0x020)
#define TCM2_RCS_TICKET (TCM2_RC)(RC_FMT1 + 0x020)
#define TCM2_RC_RESERVED_BITS (TCM2_RC)(RC_FMT1 + 0x021)
#define TCM2_RCS_RESERVED_BITS (TCM2_RC)(RC_FMT1 + 0x021)
#define TCM2_RC_BAD_AUTH (TCM2_RC)(RC_FMT1 + 0x022)
#define TCM2_RCS_BAD_AUTH (TCM2_RC)(RC_FMT1 + 0x022)
#define TCM2_RC_EXPIRED (TCM2_RC)(RC_FMT1 + 0x023)
#define TCM2_RCS_EXPIRED (TCM2_RC)(RC_FMT1 + 0x023)
#define TCM2_RC_POLICY_CC (TCM2_RC)(RC_FMT1 + 0x024)
#define TCM2_RCS_POLICY_CC (TCM2_RC)(RC_FMT1 + 0x024)
#define TCM2_RC_BINDING (TCM2_RC)(RC_FMT1 + 0x025)
#define TCM2_RCS_BINDING (TCM2_RC)(RC_FMT1 + 0x025)
#define TCM2_RC_CURVE (TCM2_RC)(RC_FMT1 + 0x026)
#define TCM2_RCS_CURVE (TCM2_RC)(RC_FMT1 + 0x026)
#define TCM2_RC_ECC_POINT (TCM2_RC)(RC_FMT1 + 0x027)
#define TCM2_RCS_ECC_POINT (TCM2_RC)(RC_FMT1 + 0x027)
#define RC_WARN (TCM2_RC)(0x900)
#define TCM2_RC_CONTEXT_GAP (TCM2_RC)(RC_WARN + 0x001)
#define TCM2_RC_OBJECT_MEMORY (TCM2_RC)(RC_WARN + 0x002)
#define TCM2_RC_SESSION_MEMORY (TCM2_RC)(RC_WARN + 0x003)
#define TCM2_RC_MEMORY (TCM2_RC)(RC_WARN + 0x004)
#define TCM2_RC_SESSION_HANDLES (TCM2_RC)(RC_WARN + 0x005)
#define TCM2_RC_OBJECT_HANDLES (TCM2_RC)(RC_WARN + 0x006)
#define TCM2_RC_LOCALITY (TCM2_RC)(RC_WARN + 0x007)
#define TCM2_RC_YIELDED (TCM2_RC)(RC_WARN + 0x008)
#define TCM2_RC_CANCELED (TCM2_RC)(RC_WARN + 0x009)
#define TCM2_RC_TESTING (TCM2_RC)(RC_WARN + 0x00A)
#define TCM2_RC_REFERENCE_H0 (TCM2_RC)(RC_WARN + 0x010)
#define TCM2_RC_REFERENCE_H1 (TCM2_RC)(RC_WARN + 0x011)
#define TCM2_RC_REFERENCE_H2 (TCM2_RC)(RC_WARN + 0x012)
#define TCM2_RC_REFERENCE_H3 (TCM2_RC)(RC_WARN + 0x013)
#define TCM2_RC_REFERENCE_H4 (TCM2_RC)(RC_WARN + 0x014)
#define TCM2_RC_REFERENCE_H5 (TCM2_RC)(RC_WARN + 0x015)
#define TCM2_RC_REFERENCE_H6 (TCM2_RC)(RC_WARN + 0x016)
#define TCM2_RC_REFERENCE_S0 (TCM2_RC)(RC_WARN + 0x018)
#define TCM2_RC_REFERENCE_S1 (TCM2_RC)(RC_WARN + 0x019)
#define TCM2_RC_REFERENCE_S2 (TCM2_RC)(RC_WARN + 0x01A)
#define TCM2_RC_REFERENCE_S3 (TCM2_RC)(RC_WARN + 0x01B)
#define TCM2_RC_REFERENCE_S4 (TCM2_RC)(RC_WARN + 0x01C)
#define TCM2_RC_REFERENCE_S5 (TCM2_RC)(RC_WARN + 0x01D)
#define TCM2_RC_REFERENCE_S6 (TCM2_RC)(RC_WARN + 0x01E)
#define TCM2_RC_NV_RATE (TCM2_RC)(RC_WARN + 0x020)
#define TCM2_RC_LOCKOUT (TCM2_RC)(RC_WARN + 0x021)
#define TCM2_RC_RETRY (TCM2_RC)(RC_WARN + 0x022)
#define TCM2_RC_NV_UNAVAILABLE (TCM2_RC)(RC_WARN + 0x023)
#define TCM2_RC_NOT_USED (TCM2_RC)(RC_WARN + 0x7F)
#define TCM2_RC_H (TCM2_RC)(0x000)
#define TCM2_RC_P (TCM2_RC)(0x040)
#define TCM2_RC_S (TCM2_RC)(0x800)
#define TCM2_RC_1 (TCM2_RC)(0x100)
#define TCM2_RC_2 (TCM2_RC)(0x200)
#define TCM2_RC_3 (TCM2_RC)(0x300)
#define TCM2_RC_4 (TCM2_RC)(0x400)
#define TCM2_RC_5 (TCM2_RC)(0x500)
#define TCM2_RC_6 (TCM2_RC)(0x600)
#define TCM2_RC_7 (TCM2_RC)(0x700)
#define TCM2_RC_8 (TCM2_RC)(0x800)
#define TCM2_RC_9 (TCM2_RC)(0x900)
#define TCM2_RC_A (TCM2_RC)(0xA00)
#define TCM2_RC_B (TCM2_RC)(0xB00)
#define TCM2_RC_C (TCM2_RC)(0xC00)
#define TCM2_RC_D (TCM2_RC)(0xD00)
#define TCM2_RC_E (TCM2_RC)(0xE00)
#define TCM2_RC_F (TCM2_RC)(0xF00)
#define TCM2_RC_N_MASK (TCM2_RC)(0xF00)


/*Table 2:17 - Definition of TCM_CLOCK_ADJUST Constants */
typedef INT8 TCM_CLOCK_ADJUST;
#define TYPE_OF_TCM_CLOCK_ADJUST UINT8
#define TCM_CLOCK_COARSE_SLOWER (TCM_CLOCK_ADJUST)(-3)
#define TCM_CLOCK_MEDIUM_SLOWER (TCM_CLOCK_ADJUST)(-2)
#define TCM_CLOCK_FINE_SLOWER (TCM_CLOCK_ADJUST)(-1)
#define TCM_CLOCK_NO_CHANGE (TCM_CLOCK_ADJUST)(0)
#define TCM_CLOCK_FINE_FASTER (TCM_CLOCK_ADJUST)(1)
#define TCM_CLOCK_MEDIUM_FASTER (TCM_CLOCK_ADJUST)(2)
#define TCM_CLOCK_COARSE_FASTER (TCM_CLOCK_ADJUST)(3)


/*Table 2:18 - Definition of TCM_EO Constants */
typedef UINT16 TCM_EO;
#define TYPE_OF_TCM_EO UINT16
#define TCM_EO_EQ (TCM_EO)(0x0000)
#define TCM_EO_NEQ (TCM_EO)(0x0001)
#define TCM_EO_SIGNED_GT (TCM_EO)(0x0002)
#define TCM_EO_UNSIGNED_GT (TCM_EO)(0x0003)
#define TCM_EO_SIGNED_LT (TCM_EO)(0x0004)
#define TCM_EO_UNSIGNED_LT (TCM_EO)(0x0005)
#define TCM_EO_SIGNED_GE (TCM_EO)(0x0006)
#define TCM_EO_UNSIGNED_GE (TCM_EO)(0x0007)
#define TCM_EO_SIGNED_LE (TCM_EO)(0x0008)
#define TCM_EO_UNSIGNED_LE (TCM_EO)(0x0009)
#define TCM_EO_BITSET (TCM_EO)(0x000A)
#define TCM_EO_BITCLEAR (TCM_EO)(0x000B)

/*Table 2:19 - Definition of TCM_ST Constants */

typedef UINT16 TCM_ST;
#define TYPE_OF_TCM_ST UINT16
#define TCM_ST_RSP_COMMAND (TCM_ST)(0x00C4)
#define TCM_ST_NULL (TCM_ST)(0x8000)
#define TCM_ST_NO_SESSIONS (TCM_ST)(0x8001)
#define TCM_ST_SESSIONS (TCM_ST)(0x8002)
#define TCM_ST_ATTEST_NV (TCM_ST)(0x8014)
#define TCM_ST_ATTEST_COMMAND_AUDIT (TCM_ST)(0x8015)
#define TCM_ST_ATTEST_SESSION_AUDIT (TCM_ST)(0x8016)
#define TCM_ST_ATTEST_CERTIFY (TCM_ST)(0x8017)
#define TCM_ST_ATTEST_QUOTE (TCM_ST)(0x8018)
#define TCM_ST_ATTEST_TIME (TCM_ST)(0x8019)
#define TCM_ST_ATTEST_CREATION (TCM_ST)(0x801A)
#define TCM_ST_ATTEST_NV_DIGEST (TCM_ST)(0x801C)
#define TCM_ST_CREATION (TCM_ST)(0x8021)
#define TCM_ST_VERIFIED (TCM_ST)(0x8022)
#define TCM_ST_AUTH_SECRET (TCM_ST)(0x8023)
#define TCM_ST_HASHCHECK (TCM_ST)(0x8024)
#define TCM_ST_AUTH_SIGNED (TCM_ST)(0x8025)
#define TCM_ST_FU_MANIFEST (TCM_ST)(0x8029)



/*Table 2:20 - Definition of TCM_SU Constants */
typedef UINT16 TCM_SU;
#define TYPE_OF_TCM_SU UINT16
#define TCM_SU_CLEAR (TCM_SU)(0x0000)
#define TCM_SU_STATE (TCM_SU)(0x0001)


/*Table 2:21 - Definition of TCM_SE Constants */
typedef UINT8 TCM_SE;
#define TYPE_OF_TCM_SE UINT8
#define TCM2_SE_HMAC (TCM_SE)(0x00)
#define TCM2_SE_POLICY (TCM_SE)(0x01)
#define TCM2_SE_TRIAL (TCM_SE)(0x03)


/*Table 2:22 - Definition of TCM_CAP Constants */
typedef UINT32 TCM_CAP;
#define TYPE_OF_TCM_CAP UINT32
#define TCM_CAP_FIRST (TCM_CAP)(0x00000000)
#define TCM_CAP_ALGS (TCM_CAP)(0x00000000)
#define TCM_CAP_HANDLES (TCM_CAP)(0x00000001)
#define TCM_CAP_COMMANDS (TCM_CAP)(0x00000002)
#define TCM_CAP_PP_COMMANDS (TCM_CAP)(0x00000003)
#define TCM_CAP_AUDIT_COMMANDS (TCM_CAP)(0x00000004)
#define TCM_CAP_PCRS (TCM_CAP)(0x00000005)
#define TCM_CAP_TCM_PROPERTIES (TCM_CAP)(0x00000006)
#define TCM_CAP_PCR_PROPERTIES (TCM_CAP)(0x00000007)
#define TCM_CAP_ECC_CURVES (TCM_CAP)(0x00000008)
#define TCM_CAP_AUTH_POLICIES (TCM_CAP)(0x00000009)
#define TCM_CAP_ACT (TCM_CAP)(0x0000000a)
#define TCM_CAP_LAST (TCM_CAP)(0x0000000a)
#define TCM_CAP_VENDOR_PROPERTY (TCM_CAP)(0x00000100)


/*Table 2:23 - Definition of TCM_PT Constants */
typedef UINT32 TCM_PT;
#define TYPE_OF_TCM_PT UINT32
#define TCM_PT_NONE (TCM_PT)(0x00000000)
#define PT_GROUP (TCM_PT)(0x00000100)
#define PT_FIXED (TCM_PT)(PT_GROUP * 1)
#define TCM_PT_FAMILY_INDICATOR (TCM_PT)(PT_FIXED + 0)
#define TCM_PT_LEVEL (TCM_PT)(PT_FIXED + 1)
#define TCM_PT_REVISION (TCM_PT)(PT_FIXED + 2)
#define TCM_PT_DAY_OF_YEAR (TCM_PT)(PT_FIXED + 3)
#define TCM_PT_YEAR (TCM_PT)(PT_FIXED + 4)
#define TCM_PT_MANUFACTURER (TCM_PT)(PT_FIXED + 5)
#define TCM_PT_VENDOR_STRING_1 (TCM_PT)(PT_FIXED + 6)
#define TCM_PT_VENDOR_STRING_2 (TCM_PT)(PT_FIXED + 7)
#define TCM_PT_VENDOR_STRING_3 (TCM_PT)(PT_FIXED + 8)
#define TCM_PT_VENDOR_STRING_4 (TCM_PT)(PT_FIXED + 9)
#define TCM_PT_VENDOR_TCM_TYPE (TCM_PT)(PT_FIXED + 10)
#define TCM_PT_FIRMWARE_VERSION_1 (TCM_PT)(PT_FIXED + 11)
#define TCM_PT_FIRMWARE_VERSION_2 (TCM_PT)(PT_FIXED + 12)
#define TCM_PT_INPUT_BUFFER (TCM_PT)(PT_FIXED + 13)
#define TCM_PT_HR_TRANSIENT_MIN (TCM_PT)(PT_FIXED + 14)
#define TCM_PT_HR_PERSISTENT_MIN (TCM_PT)(PT_FIXED + 15)
#define TCM_PT_HR_LOADED_MIN (TCM_PT)(PT_FIXED + 16)
#define TCM_PT_ACTIVE_SESSIONS_MAX (TCM_PT)(PT_FIXED + 17)
#define TCM_PT_PCR_COUNT (TCM_PT)(PT_FIXED + 18)
#define TCM_PT_PCR_SELECT_MIN (TCM_PT)(PT_FIXED + 19)
#define TCM_PT_CONTEXT_GAP_MAX (TCM_PT)(PT_FIXED + 20)
#define TCM_PT_NV_COUNTERS_MAX (TCM_PT)(PT_FIXED + 22)
#define TCM_PT_NV_INDEX_MAX (TCM_PT)(PT_FIXED + 23)
#define TCM_PT_MEMORY (TCM_PT)(PT_FIXED + 24)
#define TCM_PT_CLOCK_UPDATE (TCM_PT)(PT_FIXED + 25)
#define TCM_PT_CONTEXT_HASH (TCM_PT)(PT_FIXED + 26)
#define TCM_PT_CONTEXT_SYM (TCM_PT)(PT_FIXED + 27)
#define TCM_PT_CONTEXT_SYM_SIZE (TCM_PT)(PT_FIXED + 28)
#define TCM_PT_ORDERLY_COUNT (TCM_PT)(PT_FIXED + 29)
#define TCM_PT_MAX_COMMAND_SIZE (TCM_PT)(PT_FIXED + 30)
#define TCM_PT_MAX_RESPONSE_SIZE (TCM_PT)(PT_FIXED + 31)
#define TCM_PT_MAX_DIGEST (TCM_PT)(PT_FIXED + 32)
#define TCM_PT_MAX_OBJECT_CONTEXT (TCM_PT)(PT_FIXED + 33)
#define TCM_PT_MAX_SESSION_CONTEXT (TCM_PT)(PT_FIXED + 34)
#define TCM_PT_PS_FAMILY_INDICATOR (TCM_PT)(PT_FIXED + 35)
#define TCM_PT_PS_LEVEL (TCM_PT)(PT_FIXED + 36)
#define TCM_PT_PS_REVISION (TCM_PT)(PT_FIXED + 37)
#define TCM_PT_PS_DAY_OF_YEAR (TCM_PT)(PT_FIXED + 38)
#define TCM_PT_PS_YEAR (TCM_PT)(PT_FIXED + 39)
#define TCM_PT_SPLIT_MAX (TCM_PT)(PT_FIXED + 40)
#define TCM_PT_TOTAL_COMMANDS (TCM_PT)(PT_FIXED + 41)
#define TCM_PT_LIBRARY_COMMANDS (TCM_PT)(PT_FIXED + 42)
#define TCM_PT_VENDOR_COMMANDS (TCM_PT)(PT_FIXED + 43)
#define TCM_PT_NV_BUFFER_MAX (TCM_PT)(PT_FIXED + 44)
#define TCM_PT_MODES (TCM_PT)(PT_FIXED + 45)
#define TCM_PT_MAX_CAP_BUFFER (TCM_PT)(PT_FIXED + 46)
#define PT_VAR (TCM_PT)(PT_GROUP * 2)
#define TCM_PT_PERMANENT (TCM_PT)(PT_VAR + 0)
#define TCM_PT_STARTUP_CLEAR (TCM_PT)(PT_VAR + 1)
#define TCM_PT_HR_NV_INDEX (TCM_PT)(PT_VAR + 2)
#define TCM_PT_HR_LOADED (TCM_PT)(PT_VAR + 3)
#define TCM_PT_HR_LOADED_AVAIL (TCM_PT)(PT_VAR + 4)
#define TCM_PT_HR_ACTIVE (TCM_PT)(PT_VAR + 5)
#define TCM_PT_HR_ACTIVE_AVAIL (TCM_PT)(PT_VAR + 6)
#define TCM_PT_HR_TRANSIENT_AVAIL (TCM_PT)(PT_VAR + 7)
#define TCM_PT_HR_PERSISTENT (TCM_PT)(PT_VAR + 8)
#define TCM_PT_HR_PERSISTENT_AVAIL (TCM_PT)(PT_VAR + 9)
#define TCM_PT_NV_COUNTERS (TCM_PT)(PT_VAR + 10)
#define TCM_PT_NV_COUNTERS_AVAIL (TCM_PT)(PT_VAR + 11)
#define TCM_PT_ALGORITHM_SET (TCM_PT)(PT_VAR + 12)
#define TCM_PT_LOADED_CURVES (TCM_PT)(PT_VAR + 13)
#define TCM_PT_LOCKOUT_COUNTER (TCM_PT)(PT_VAR + 14)
#define TCM_PT_MAX_AUTH_FAIL (TCM_PT)(PT_VAR + 15)
#define TCM_PT_LOCKOUT_INTERVAL (TCM_PT)(PT_VAR + 16)
#define TCM_PT_LOCKOUT_RECOVERY (TCM_PT)(PT_VAR + 17)
#define TCM_PT_NV_WRITE_RECOVERY (TCM_PT)(PT_VAR + 18)
#define TCM_PT_AUDIT_COUNTER_0 (TCM_PT)(PT_VAR + 19)
#define TCM_PT_AUDIT_COUNTER_1 (TCM_PT)(PT_VAR + 20)


/*Table 2:24 - Definition of TCM_PT_PCR Constants */
typedef UINT32 TCM_PT_PCR;
#define TYPE_OF_TCM_PT_PCR UINT32
#define TCM_PT_PCR_FIRST (TCM_PT_PCR)(0x00000000)
#define TCM_PT_PCR_SAVE (TCM_PT_PCR)(0x00000000)
#define TCM_PT_PCR_EXTEND_L0 (TCM_PT_PCR)(0x00000001)
#define TCM_PT_PCR_RESET_L0 (TCM_PT_PCR)(0x00000002)
#define TCM_PT_PCR_EXTEND_L1 (TCM_PT_PCR)(0x00000003)
#define TCM_PT_PCR_RESET_L1 (TCM_PT_PCR)(0x00000004)
#define TCM_PT_PCR_EXTEND_L2 (TCM_PT_PCR)(0x00000005)
#define TCM_PT_PCR_RESET_L2 (TCM_PT_PCR)(0x00000006)
#define TCM_PT_PCR_EXTEND_L3 (TCM_PT_PCR)(0x00000007)
#define TCM_PT_PCR_RESET_L3 (TCM_PT_PCR)(0x00000008)
#define TCM_PT_PCR_EXTEND_L4 (TCM_PT_PCR)(0x00000009)
#define TCM_PT_PCR_RESET_L4 (TCM_PT_PCR)(0x0000000A)
#define TCM_PT_PCR_NO_INCREMENT (TCM_PT_PCR)(0x00000011)
#define TCM_PT_PCR_DRTM_RESET (TCM_PT_PCR)(0x00000012)
#define TCM_PT_PCR_POLICY (TCM_PT_PCR)(0x00000013)
#define TCM_PT_PCR_AUTH (TCM_PT_PCR)(0x00000014)
#define TCM_PT_PCR_LAST (TCM_PT_PCR)(0x00000014)


/*Table 2:25 - Definition of TCM_PS Constants  */
typedef UINT32 TCM_PS;
#define TYPE_OF_TCM_PS UINT32
#define TCM_PS_MAIN (TCM_PS)(0x00000000)
#define TCM_PS_PC (TCM_PS)(0x00000001)
#define TCM_PS_PDA (TCM_PS)(0x00000002)
#define TCM_PS_CELL_PHONE (TCM_PS)(0x00000003)
#define TCM_PS_SERVER (TCM_PS)(0x00000004)
#define TCM_PS_PERIPHERAL (TCM_PS)(0x00000005)
#define TCM_PS_TSS (TCM_PS)(0x00000006)
#define TCM_PS_STORAGE (TCM_PS)(0x00000007)
#define TCM_PS_AUTHENTICATION (TCM_PS)(0x00000008)
#define TCM_PS_EMBEDDED (TCM_PS)(0x00000009)
#define TCM_PS_HARDCOPY (TCM_PS)(0x0000000A)
#define TCM_PS_INFRASTRUCTURE (TCM_PS)(0x0000000B)
#define TCM_PS_VIRTUALIZATION (TCM_PS)(0x0000000C)
#define TCM_PS_TNC (TCM_PS)(0x0000000D)
#define TCM_PS_MULTI_TENANT (TCM_PS)(0x0000000E)
#define TCM_PS_TC (TCM_PS)(0x0000000F)


/*Table 2:26 - Definition of Types for Handles */
typedef UINT32 TCM_HANDLE;
#define TYPE_OF_TCM_HANDLE UINT32
#define TCM2_RH_FIRST       ((TCM2_RH) 0x40000000) /* R */
#define TCM2_RH_SRK         ((TCM2_RH) 0x40000000) /* R */
#define TCM2_RH_OWNER       ((TCM2_RH) 0x40000001) /* K A P */
#define TCM2_RH_REVOKE      ((TCM2_RH) 0x40000002) /* R */
#define TCM2_RH_TRANSPORT   ((TCM2_RH) 0x40000003) /* R */
#define TCM2_RH_OPERATOR    ((TCM2_RH) 0x40000004) /* R */
#define TCM2_RH_ADMIN       ((TCM2_RH) 0x40000005) /* R */
#define TCM2_RH_EK          ((TCM2_RH) 0x40000006) /* R */
#define TCM2_RH_NULL        ((TCM2_RH) 0x40000007) /* K A P */
#define TCM2_RH_UNASSIGNED  ((TCM2_RH) 0x40000008) /* R */
#define TCM2_RH_PW          ((TCM2_RH) 0x40000009) /* S */
#define TCM2_RS_PW          ((TCM2_RH) 0x40000009) /* S; This was a bug; to be deprecated*/
#define TCM2_RH_LOCKOUT     ((TCM2_RH) 0x4000000A) /* A */
#define TCM2_RH_ENDORSEMENT ((TCM2_RH) 0x4000000B) /* K A P */
#define TCM2_RH_PLATFORM    ((TCM2_RH) 0x4000000C) /* K A P */
#define TCM2_RH_PLATFORM_NV ((TCM2_RH) 0x4000000D) /* C */
#define TCM2_RH_AUTH_00     ((TCM2_RH) 0x40000010) /* A */
#define TCM2_RH_AUTH_FF     ((TCM2_RH) 0x4000010F) /* A */
#define TCM2_RH_ACT_0       ((TCM2_RH) 0x40000110) /* A P */
#define TCM2_RH_ACT_1       ((TCM2_RH) 0x40000111)
#define TCM2_RH_ACT_2       ((TCM2_RH) 0x40000112)
#define TCM2_RH_ACT_3       ((TCM2_RH) 0x40000113)
#define TCM2_RH_ACT_4       ((TCM2_RH) 0x40000114)
#define TCM2_RH_ACT_5       ((TCM2_RH) 0x40000115)
#define TCM2_RH_ACT_6       ((TCM2_RH) 0x40000116)
#define TCM2_RH_ACT_7       ((TCM2_RH) 0x40000117)
#define TCM2_RH_ACT_8       ((TCM2_RH) 0x40000118)
#define TCM2_RH_ACT_9       ((TCM2_RH) 0x40000119)
#define TCM2_RH_ACT_A       ((TCM2_RH) 0x4000011A)
#define TCM2_RH_ACT_B       ((TCM2_RH) 0x4000011B)
#define TCM2_RH_ACT_C       ((TCM2_RH) 0x4000011C)
#define TCM2_RH_ACT_D       ((TCM2_RH) 0x4000011D)
#define TCM2_RH_ACT_E       ((TCM2_RH) 0x4000011E)
#define TCM2_RH_ACT_F       ((TCM2_RH) 0x4000011F) /* A P */
#define TCM2_RH_FW_OWNER             ((TCM2_RH) 0x40000140) /* K */
#define TCM2_RH_FW_ENDORSEMENT       ((TCM2_RH) 0x40000141) /* K */
#define TCM2_RH_FW_PLATFORM          ((TCM2_RH) 0x40000142) /* K */
#define TCM2_RH_FW_NULL              ((TCM2_RH) 0x40000143) /* K */
#define TCM2_RH_SVN_OWNER_BASE       ((TCM2_RH) 0x40010000) /* K */
#define TCM2_RH_SVN_ENDORSEMENT_BASE ((TCM2_RH) 0x40020000) /* K */
#define TCM2_RH_SVN_PLATFORM_BASE    ((TCM2_RH) 0x40030000) /* K */
#define TCM2_RH_SVN_NULL_BASE        ((TCM2_RH) 0x40040000) /* K */
#define TCM2_RH_LAST                 ((TCM2_RH) 0x4004FFFF) /* R */

/*Table 2:27 - Definition of TCM_HT Constants  */
typedef UINT8 TCM_HT;
#define TYPE_OF_TCM_HT UINT8
#define TCM_HT_PCR (TCM_HT)(0x00)
#define TCM_HT_NV_INDEX (TCM_HT)(0x01)
#define TCM_HT_HMAC_SESSION (TCM_HT)(0x02)
#define TCM_HT_LOADED_SESSION (TCM_HT)(0x02)
#define TCM_HT_POLICY_SESSION (TCM_HT)(0x03)
#define TCM_HT_SAVED_SESSION (TCM_HT)(0x03)
#define TCM_HT_PERMANENT (TCM_HT)(0x40)
#define TCM_HT_TRANSIENT (TCM_HT)(0x80)
#define TCM_HT_PERSISTENT (TCM_HT)(0x81)
#define TCM_HT_AC (TCM_HT)(0x90)


/*Table 2:28 - Definition of TCM_RH Constants  */
typedef TCM_HANDLE TCM_RH;
#define TCM_RH_FIRST (TCM_RH)(0x40000000)
#define TCM_RH_SRK (TCM_RH)(0x40000000)
#define TCM_RH_OWNER (TCM_RH)(0x40000001)
#define TCM_RH_REVOKE (TCM_RH)(0x40000002)
#define TCM_RH_TRANSPORT (TCM_RH)(0x40000003)
#define TCM_RH_OPERATOR (TCM_RH)(0x40000004)
#define TCM_RH_ADMIN (TCM_RH)(0x40000005)
#define TCM_RH_EK (TCM_RH)(0x40000006)
#define TCM_RH_NULL (TCM_RH)(0x40000007)
#define TCM_RH_UNASSIGNED (TCM_RH)(0x40000008)
#define TCM_RS_PW (TCM_RH)(0x40000009)
#define TCM_RH_LOCKOUT (TCM_RH)(0x4000000A)
#define TCM_RH_ENDORSEMENT (TCM_RH)(0x4000000B)
#define TCM_RH_PLATFORM (TCM_RH)(0x4000000C)
#define TCM_RH_PLATFORM_NV (TCM_RH)(0x4000000D)
#define TCM_RH_AUTH_00 (TCM_RH)(0x40000010)
#define TCM_RH_AUTH_FF (TCM_RH)(0x4000010F)
#define TCM_RH_ACT_0 (TCM_RH)(0x40000110)
#define TCM_RH_ACT_F (TCM_RH)(0x4000011F)
#define TCM_RH_LAST (TCM_RH)(0x4000011F)

/*Table 2:29 - Definition of TCM_HC Constants  */
typedef TCM_HANDLE TCM_HC;
#define HR_HANDLE_MASK (TCM_HC)(0x00FFFFFF)
#define HR_RANGE_MASK (TCM_HC)(0xFF000000)
#define HR_SHIFT (TCM_HC)(24)
#define HR_PCR (TCM_HC)((TCM_HT_PCR << HR_SHIFT))
#define HR_HMAC_SESSION (TCM_HC)((TCM_HT_HMAC_SESSION << HR_SHIFT))
#define HR_POLICY_SESSION (TCM_HC)((TCM_HT_POLICY_SESSION << HR_SHIFT))

#define HR_TRANSIENT (TCM_HC)((((UINT32)TCM_HT_TRANSIENT) << HR_SHIFT))
#define HR_PERSISTENT (TCM_HC)((((UINT32)TCM_HT_PERSISTENT) << HR_SHIFT))

#define HR_NV_INDEX (TCM_HC)((TCM_HT_NV_INDEX << HR_SHIFT))
#define HR_PERMANENT (TCM_HC)((TCM_HT_PERMANENT << HR_SHIFT))
#define PCR_FIRST (TCM_HC)((HR_PCR + 0))
#define PCR_LAST (TCM_HC)((PCR_FIRST + IMPLEMENTATION_PCR - 1))
#define HMAC_SESSION_FIRST (TCM_HC)((HR_HMAC_SESSION + 0))
#define HMAC_SESSION_LAST (TCM_HC)((HMAC_SESSION_FIRST + MAX_ACTIVE_SESSIONS - 1))
#define LOADED_SESSION_FIRST (TCM_HC)(HMAC_SESSION_FIRST)
#define LOADED_SESSION_LAST (TCM_HC)(HMAC_SESSION_LAST)
#define POLICY_SESSION_FIRST (TCM_HC)((HR_POLICY_SESSION + 0))
#define POLICY_SESSION_LAST (TCM_HC)((POLICY_SESSION_FIRST + MAX_ACTIVE_SESSIONS - 1))
#define TRANSIENT_FIRST (TCM_HC)((HR_TRANSIENT + 0))
#define ACTIVE_SESSION_FIRST (TCM_HC)(POLICY_SESSION_FIRST)
#define ACTIVE_SESSION_LAST (TCM_HC)(POLICY_SESSION_LAST)
#define TRANSIENT_LAST (TCM_HC)((TRANSIENT_FIRST + MAX_LOADED_OBJECTS - 1))
#define PERSISTENT_FIRST (TCM_HC)((HR_PERSISTENT + 0))
#define PERSISTENT_LAST (TCM_HC)((PERSISTENT_FIRST + 0x00FFFFFF))
#define PLATFORM_PERSISTENT (TCM_HC)((PERSISTENT_FIRST + 0x00800000))
#define NV_INDEX_FIRST (TCM_HC)((HR_NV_INDEX + 0))
#define NV_INDEX_LAST (TCM_HC)((NV_INDEX_FIRST + 0x00FFFFFF))
#define PERMANENT_FIRST (TCM_HC)(TCM_RH_FIRST)
#define PERMANENT_LAST (TCM_HC)(TCM_RH_LAST)
#define HR_NV_AC (TCM_HC)(((TCM_HT_NV_INDEX << HR_SHIFT) + 0xD00000))
#define NV_AC_FIRST (TCM_HC)((HR_NV_AC + 0))
#define NV_AC_LAST (TCM_HC)((HR_NV_AC + 0x0000FFFF))
#define HR_AC (TCM_HC)((TCM_HT_AC << HR_SHIFT))
#define AC_FIRST (TCM_HC)((HR_AC + 0))
#define AC_LAST (TCM_HC)((HR_AC + 0x0000FFFF))

#define TYPE_OF_TCMA_ALGORITHM UINT32
#define TCMA_ALGORITHM_TO_UINT32(a) (*((UINT32 *)&(a)))
#define UINT32_TO_TCMA_ALGORITHM(a) (*((TCMA_ALGORITHM *)&(a)))

#define TCMA_ALGORITHM_TO_BYTE_ARRAY(i, a) \
    UINT32_TO_BYTE_ARRAY((TCMA_ALGORITHM_TO_UINT32(i)), (a))

#define BYTE_ARRAY_TO_TCMA_ALGORITHM(i, a)  \
    {                                       \
        UINT32 x = BYTE_ARRAY_TO_UINT32(a); \
        i = UINT32_TO_TCMA_ALGORITHM(x);    \
    }


/*Table 2:30 - Definition of TCMA_ALGORITHM Bits */
#if USE_BIT_FIELD_STRUCTURES
typedef struct TCMA_ALGORITHM {
    unsigned asymmetric : 1;
    unsigned symmetric : 1;
    unsigned hash : 1;
    unsigned object : 1;
    unsigned Reserved_bits_at_4 : 4;
    unsigned signing : 1;
    unsigned encrypting : 1;
    unsigned method : 1;
    unsigned Reserved_bits_at_11 : 21;
} TCMA_ALGORITHM;

/*This is the initializer for a TCMA_ALGORITHM structure. */
#define TCMA_ALGORITHM_INITIALIZER(                                                          \
    asymmetric, symmetric, hash, object, bits_at_4, signing, encrypting, method, bits_at_11) \
    {asymmetric, symmetric, hash, object, bits_at_4, signing, encrypting, method, bits_at_11}
#else  // USE_BIT_FIELD_STRUCTURES

typedef UINT32 TCMA_ALGORITHM;
#define TYPE_OF_TCMA_ALGORITHM UINT32
#define TCMA_ALGORITHM_asymmetric ((TCMA_ALGORITHM)1 << 0)
#define TCMA_ALGORITHM_symmetric ((TCMA_ALGORITHM)1 << 1)
#define TCMA_ALGORITHM_hash ((TCMA_ALGORITHM)1 << 2)
#define TCMA_ALGORITHM_object ((TCMA_ALGORITHM)1 << 3)
#define TCMA_ALGORITHM_signing ((TCMA_ALGORITHM)1 << 8)
#define TCMA_ALGORITHM_encrypting ((TCMA_ALGORITHM)1 << 9)
#define TCMA_ALGORITHM_method ((TCMA_ALGORITHM)1 << 10)
#define TCMA_ALGORITHM_reserved 0xfffff8f0

// This is the initializer for a TCMA_ALGORITHM bit array.
#define TCMA_ALGORITHM_INITIALIZER(                                                          \
    asymmetric, symmetric, hash, object, bits_at_4, signing, encrypting, method, bits_at_11) \
    (TCMA_ALGORITHM)(                                                                        \
        (asymmetric << 0) + (symmetric << 1) + (hash << 2) +                                 \
        (object << 3) + (signing << 8) + (encrypting << 9) +                                 \
        (method << 10))
#endif  // USE_BIT_FIELD_STRUCTURES

#define TYPE_OF_TCMA_OBJECT UINT32
#define TCMA_OBJECT_TO_UINT32(a) (*((UINT32 *)&(a)))
#define UINT32_TO_TCMA_OBJECT(a) (*((TCMA_OBJECT *)&(a)))
#define TCMA_OBJECT_TO_BYTE_ARRAY(i, a) \
    UINT32_TO_BYTE_ARRAY((TCMA_OBJECT_TO_UINT32(i)), (a))
#define BYTE_ARRAY_TO_TCMA_OBJECT(i, a)     \
    {                                       \
        UINT32 x = BYTE_ARRAY_TO_UINT32(a); \
        i = UINT32_TO_TCMA_OBJECT(x);       \
    }


/*This is the initializer for a TCMA_OBJECT structure. */
#if USE_BIT_FIELD_STRUCTURES
typedef struct TCMA_OBJECT {  // Table 2:31
    unsigned Reserved_bit_at_0 : 1;
    unsigned fixedTCM : 1;
    unsigned stClear : 1;
    unsigned Reserved_bit_at_3 : 1;
    unsigned fixedParent : 1;
    unsigned sensitiveDataOrigin : 1;
    unsigned userWithAuth : 1;
    unsigned adminWithPolicy : 1;
    unsigned Reserved_bits_at_8 : 2;
    unsigned noDA : 1;
    unsigned encryptedDuplication : 1;
    unsigned Reserved_bits_at_12 : 4;
    unsigned restricted : 1;
    unsigned decrypt : 1;
    unsigned sign : 1;
    unsigned x509sign : 1;
    unsigned Reserved_bits_at_20 : 12;
} TCMA_OBJECT;
// This is the initializer for a TCMA_OBJECT structure
#define TCMA_OBJECT_INITIALIZER(                                                                                                                                                                                \
    bit_at_0, fixedtcm, stclear, bit_at_3, fixedparent, sensitivedataorigin, userwithauth, adminwithpolicy, bits_at_8, noda, encryptedduplication, bits_at_12, restricted, decrypt, sign, x509sign, bits_at_20) \
    {bit_at_0, fixedtcm, stclear, bit_at_3, fixedparent, sensitivedataorigin, userwithauth, adminwithpolicy, bits_at_8, noda, encryptedduplication, bits_at_12, restricted, decrypt, sign, x509sign, bits_at_20}
#else  // USE_BIT_FIELD_STRUCTURES
// This implements Table 2:31 TCMA_OBJECT using bit masking
typedef UINT32 TCMA_OBJECT;
#define TYPE_OF_TCMA_OBJECT UINT32
#define TCMA_OBJECT_Reserved_bit_at_0 ((TCMA_OBJECT)1 << 0)  // 保留位，地址：0x00000001

// 对象固定在当前TCM实例上，无法导出到其他TCM实例，地址：0x00000002
#define TCMA_OBJECT_fixedTCM ((TCMA_OBJECT)1 << 1)
// 清除状态（stClear），地址：0x00000004
#define TCMA_OBJECT_stClear ((TCMA_OBJECT)1 << 2)
// 对象固定绑定其父对象，地址：0x00000010
#define TCMA_OBJECT_fixedParent ((TCMA_OBJECT)1 << 4)
// 敏感数据由TCM生成，地址：0x00000020
#define TCMA_OBJECT_sensitiveDataOrigin ((TCMA_OBJECT)1 << 5)
// 需要用户授权（如密码）才能使用对象，地址：0x00000040
#define TCMA_OBJECT_userWithAuth ((TCMA_OBJECT)1 << 6)
// 需要通过特定策略才能使用对象，地址：0x00000080
#define TCMA_OBJECT_adminWithPolicy ((TCMA_OBJECT)1 << 7)
// 禁用字典攻击防护机制，地址：0x00000400
#define TCMA_OBJECT_noDA ((TCMA_OBJECT)1 << 10)
// 对象导出时加密副本数据，地址：0x00000800
#define TCMA_OBJECT_encryptedDuplication ((TCMA_OBJECT)1 << 11)
// 限制对象使用范围，通常与解密相关，地址：0x00010000
#define TCMA_OBJECT_restricted ((TCMA_OBJECT)1 << 16)
// 对象可用于解密操作，地址：0x00020000
#define TCMA_OBJECT_decrypt ((TCMA_OBJECT)1 << 17)
// 对象可用于签名或HMAC操作，地址：0x00040000
#define TCMA_OBJECT_SIGN ((TCMA_OBJECT)1 << 18)
// 对象可用于x509证书签名，地址：0x00080000
#define TCMA_OBJECT_x509sign ((TCMA_OBJECT)1 << 19)
// 保留位，不能使用，地址：0xfff0f309
#define TCMA_OBJECT_reserved ((TCMA_OBJECT)0xfff0f309)



//  This is the initializer for a TCMA_OBJECT bit array.
#define TCMA_OBJECT_INITIALIZER(                                                                                                                                                                                \
    bit_at_0, fixedtcm, stclear, bit_at_3, fixedparent, sensitivedataorigin, userwithauth, adminwithpolicy, bits_at_8, noda, encryptedduplication, bits_at_12, restricted, decrypt, sign, x509sign, bits_at_20) \
    (TCMA_OBJECT)(                                                                                                                                                                                              \
        (fixedtcm << 1) + (stclear << 2) +                                                                                                                                                                      \
        (fixedparent << 4) + (sensitivedataorigin << 5) +                                                                                                                                                       \
        (userwithauth << 6) + (adminwithpolicy << 7) +                                                                                                                                                          \
        (noda << 10) + (encryptedduplication << 11) +                                                                                                                                                           \
        (restricted << 16) + (decrypt << 17) +                                                                                                                                                                  \
        (sign << 18) + (x509sign << 19))
#endif  // USE_BIT_FIELD_STRUCTURES

#define TYPE_OF_TCMA_SESSION UINT8
#define TCMA_SESSION_TO_UINT8(a) (*((UINT8 *)&(a)))
#define UINT8_TO_TCMA_SESSION(a) (*((TCMA_SESSION *)&(a)))
#define TCMA_SESSION_TO_BYTE_ARRAY(i, a) \
    UINT8_TO_BYTE_ARRAY((TCMA_SESSION_TO_UINT8(i)), (a))
#define BYTE_ARRAY_TO_TCMA_SESSION(i, a)  \
    {                                     \
        UINT8 x = BYTE_ARRAY_TO_UINT8(a); \
        i = UINT8_TO_TCMA_SESSION(x);     \
    }
#if USE_BIT_FIELD_STRUCTURES
typedef struct TCMA_SESSION {  // Table 2:32
    unsigned continueSession : 1;
    unsigned auditExclusive : 1;
    unsigned auditReset : 1;
    unsigned Reserved_bits_at_3 : 2;
    unsigned decrypt : 1;
    unsigned encrypt : 1;
    unsigned audit : 1;
} TCMA_SESSION;
// This is the initializer for a TCMA_SESSION structure
#define TCMA_SESSION_INITIALIZER(                                                    \
    continuesession, auditexclusive, auditreset, bits_at_3, decrypt, encrypt, audit) \
    {continuesession, auditexclusive, auditreset, bits_at_3, decrypt, encrypt, audit}
#else  // USE_BIT_FIELD_STRUCTURES
// This implements Table 2:32 TCMA_SESSION using bit masking
typedef UINT8 TCMA_SESSION;
#define TYPE_OF_TCMA_SESSION UINT8
#define TCMA_SESSION_continueSession ((TCMA_SESSION)1 << 0)
#define TCMA_SESSION_auditExclusive ((TCMA_SESSION)1 << 1)
#define TCMA_SESSION_auditReset ((TCMA_SESSION)1 << 2)
#define TCMA_SESSION_decrypt ((TCMA_SESSION)1 << 5)
#define TCMA_SESSION_encrypt ((TCMA_SESSION)1 << 6)
#define TCMA_SESSION_audit ((TCMA_SESSION)1 << 7)
#define TCMA_SESSION_reserved 0x18
// This is the initializer for a TCMA_SESSION bit array.
#define TCMA_SESSION_INITIALIZER(                                                    \
    continuesession, auditexclusive, auditreset, bits_at_3, decrypt, encrypt, audit) \
    (TCMA_SESSION)(                                                                  \
        (continuesession << 0) + (auditexclusive << 1) +                             \
        (auditreset << 2) + (decrypt << 5) +                                         \
        (encrypt << 6) + (audit << 7))
#endif  // USE_BIT_FIELD_STRUCTURES

#define TYPE_OF_TCMA_LOCALITY UINT8
#define TCMA_LOCALITY_TO_UINT8(a) (*((UINT8 *)&(a)))
#define UINT8_TO_TCMA_LOCALITY(a) (*((TCMA_LOCALITY *)&(a)))
#define TCMA_LOCALITY_TO_BYTE_ARRAY(i, a) \
    UINT8_TO_BYTE_ARRAY((TCMA_LOCALITY_TO_UINT8(i)), (a))
#define BYTE_ARRAY_TO_TCMA_LOCALITY(i, a) \
    {                                     \
        UINT8 x = BYTE_ARRAY_TO_UINT8(a); \
        i = UINT8_TO_TCMA_LOCALITY(x);    \
    }
#if USE_BIT_FIELD_STRUCTURES
typedef struct TCMA_LOCALITY {  // Table 2:33
    unsigned TCM_LOC_ZERO : 1;
    unsigned TCM_LOC_ONE : 1;
    unsigned TCM_LOC_TWO : 1;
    unsigned TCM_LOC_THREE : 1;
    unsigned TCM_LOC_FOUR : 1;
    unsigned Extended : 3;
} TCMA_LOCALITY;
// This is the initializer for a TCMA_LOCALITY structure
#define TCMA_LOCALITY_INITIALIZER(                                                 \
    tcm_loc_zero, tcm_loc_one, tcm_loc_two, tcm_loc_three, tcm_loc_four, extended) \
    {tcm_loc_zero, tcm_loc_one, tcm_loc_two, tcm_loc_three, tcm_loc_four, extended}
#else  // USE_BIT_FIELD_STRUCTURES
// This implements Table 2:33 TCMA_LOCALITY using bit masking
typedef UINT8 TCMA_LOCALITY;
#define TYPE_OF_TCMA_LOCALITY UINT8
#define TCMA_LOCALITY_TCM_LOC_ZERO ((TCMA_LOCALITY)1 << 0)
#define TCMA_LOCALITY_TCM_LOC_ONE ((TCMA_LOCALITY)1 << 1)
#define TCMA_LOCALITY_TCM_LOC_TWO ((TCMA_LOCALITY)1 << 2)
#define TCMA_LOCALITY_TCM_LOC_THREE ((TCMA_LOCALITY)1 << 3)
#define TCMA_LOCALITY_TCM_LOC_FOUR ((TCMA_LOCALITY)1 << 4)
#define TCMA_LOCALITY_Extended_SHIFT 5
#define TCMA_LOCALITY_Extended ((TCMA_LOCALITY)0x7 << 5)
// This is the initializer for a TCMA_LOCALITY bit array.
#define TCMA_LOCALITY_INITIALIZER(                                                 \
    tcm_loc_zero, tcm_loc_one, tcm_loc_two, tcm_loc_three, tcm_loc_four, extended) \
    (TCMA_LOCALITY)(                                                               \
        (tcm_loc_zero << 0) + (tcm_loc_one << 1) + (tcm_loc_two << 2) +            \
        (tcm_loc_three << 3) + (tcm_loc_four << 4) + (extended << 5))
#endif  // USE_BIT_FIELD_STRUCTURES

#define TYPE_OF_TCMA_PERMANENT UINT32
#define TCMA_PERMANENT_TO_UINT32(a) (*((UINT32 *)&(a)))
#define UINT32_TO_TCMA_PERMANENT(a) (*((TCMA_PERMANENT *)&(a)))
#define TCMA_PERMANENT_TO_BYTE_ARRAY(i, a) \
    UINT32_TO_BYTE_ARRAY((TCMA_PERMANENT_TO_UINT32(i)), (a))
#define BYTE_ARRAY_TO_TCMA_PERMANENT(i, a)  \
    {                                       \
        UINT32 x = BYTE_ARRAY_TO_UINT32(a); \
        i = UINT32_TO_TCMA_PERMANENT(x);    \
    }


/*Table 2:34 - Definition of TCMA_PERMANENT Bits (BitsTable()) */
#if USE_BIT_FIELD_STRUCTURES
typedef struct TCMA_PERMANENT {  // Table 2:34
    unsigned ownerAuthSet : 1;
    unsigned endorsementAuthSet : 1;
    unsigned lockoutAuthSet : 1;
    unsigned Reserved_bits_at_3 : 5;
    unsigned disableClear : 1;
    unsigned inLockout : 1;
    unsigned tcmGeneratedEPS : 1;
    unsigned Reserved_bits_at_11 : 21;
} TCMA_PERMANENT;
// This is the initializer for a TCMA_PERMANENT structure
#define TCMA_PERMANENT_INITIALIZER(                                                                                    \
    ownerauthset, endorsementauthset, lockoutauthset, bits_at_3, disableclear, inlockout, tcmgeneratedeps, bits_at_11) \
    {ownerauthset, endorsementauthset, lockoutauthset, bits_at_3, disableclear, inlockout, tcmgeneratedeps, bits_at_11}
#else  // USE_BIT_FIELD_STRUCTURES
// This implements Table 2:34 TCMA_PERMANENT using bit masking
typedef UINT32 TCMA_PERMANENT;
#define TYPE_OF_TCMA_PERMANENT UINT32
#define TCMA_PERMANENT_ownerAuthSet ((TCMA_PERMANENT)1 << 0)
#define TCMA_PERMANENT_endorsementAuthSet ((TCMA_PERMANENT)1 << 1)
#define TCMA_PERMANENT_lockoutAuthSet ((TCMA_PERMANENT)1 << 2)
#define TCMA_PERMANENT_disableClear ((TCMA_PERMANENT)1 << 8)
#define TCMA_PERMANENT_inLockout ((TCMA_PERMANENT)1 << 9)
#define TCMA_PERMANENT_tcmGeneratedEPS ((TCMA_PERMANENT)1 << 10)
// This is the initializer for a TCMA_PERMANENT bit array.
#define TCMA_PERMANENT_INITIALIZER(                                                                                    \
    ownerauthset, endorsementauthset, lockoutauthset, bits_at_3, disableclear, inlockout, tcmgeneratedeps, bits_at_11) \
    (TCMA_PERMANENT)(                                                                                                  \
        (ownerauthset << 0) + (endorsementauthset << 1) +                                                              \
        (lockoutauthset << 2) + (disableclear << 8) +                                                                  \
        (inlockout << 9) + (tcmgeneratedeps << 10))
#endif  // USE_BIT_FIELD_STRUCTURES

#define TYPE_OF_TCMA_STARTUP_CLEAR UINT32
#define TCMA_STARTUP_CLEAR_TO_UINT32(a) (*((UINT32 *)&(a)))
#define UINT32_TO_TCMA_STARTUP_CLEAR(a) (*((TCMA_STARTUP_CLEAR *)&(a)))
#define TCMA_STARTUP_CLEAR_TO_BYTE_ARRAY(i, a) \
    UINT32_TO_BYTE_ARRAY((TCMA_STARTUP_CLEAR_TO_UINT32(i)), (a))
#define BYTE_ARRAY_TO_TCMA_STARTUP_CLEAR(i, a) \
    {                                          \
        UINT32 x = BYTE_ARRAY_TO_UINT32(a);    \
        i = UINT32_TO_TCMA_STARTUP_CLEAR(x);   \
    }


/*Table 2:35 - Definition of TCMA_STARTUP_CLEAR Bits */
#if USE_BIT_FIELD_STRUCTURES
typedef struct TCMA_STARTUP_CLEAR {  // Table 2:35
    unsigned phEnable : 1;
    unsigned shEnable : 1;
    unsigned ehEnable : 1;
    unsigned phEnableNV : 1;
    unsigned Reserved_bits_at_4 : 27;
    unsigned orderly : 1;
} TCMA_STARTUP_CLEAR;
// This is the initializer for a TCMA_STARTUP_CLEAR structure
#define TCMA_STARTUP_CLEAR_INITIALIZER(                           \
    phenable, shenable, ehenable, phenablenv, bits_at_4, orderly) \
    {phenable, shenable, ehenable, phenablenv, bits_at_4, orderly}
#else  // USE_BIT_FIELD_STRUCTURES
// This implements Table 2:35 TCMA_STARTUP_CLEAR using bit masking
typedef UINT32 TCMA_STARTUP_CLEAR;
#define TYPE_OF_TCMA_STARTUP_CLEAR UINT32
#define TCMA_STARTUP_CLEAR_phEnable ((TCMA_STARTUP_CLEAR)1 << 0)
#define TCMA_STARTUP_CLEAR_shEnable ((TCMA_STARTUP_CLEAR)1 << 1)
#define TCMA_STARTUP_CLEAR_ehEnable ((TCMA_STARTUP_CLEAR)1 << 2)
#define TCMA_STARTUP_CLEAR_phEnableNV ((TCMA_STARTUP_CLEAR)1 << 3)
#define TCMA_STARTUP_CLEAR_orderly ((TCMA_STARTUP_CLEAR)1 << 31)
// This is the initializer for a TCMA_STARTUP_CLEAR bit array.
#define TCMA_STARTUP_CLEAR_INITIALIZER(                           \
    phenable, shenable, ehenable, phenablenv, bits_at_4, orderly) \
    (TCMA_STARTUP_CLEAR)(                                         \
        (phenable << 0) + (shenable << 1) + (ehenable << 2) +     \
        (phenablenv << 3) + (orderly << 31))
#endif  // USE_BIT_FIELD_STRUCTURES

#define TYPE_OF_TCMA_MEMORY UINT32
#define TCMA_MEMORY_TO_UINT32(a) (*((UINT32 *)&(a)))
#define UINT32_TO_TCMA_MEMORY(a) (*((TCMA_MEMORY *)&(a)))
#define TCMA_MEMORY_TO_BYTE_ARRAY(i, a) \
    UINT32_TO_BYTE_ARRAY((TCMA_MEMORY_TO_UINT32(i)), (a))
#define BYTE_ARRAY_TO_TCMA_MEMORY(i, a)     \
    {                                       \
        UINT32 x = BYTE_ARRAY_TO_UINT32(a); \
        i = UINT32_TO_TCMA_MEMORY(x);       \
    }
#if USE_BIT_FIELD_STRUCTURES
typedef struct TCMA_MEMORY {  // Table 2:36
    unsigned sharedRAM : 1;
    unsigned sharedNV : 1;
    unsigned objectCopiedToRam : 1;
    unsigned Reserved_bits_at_3 : 29;
} TCMA_MEMORY;
// This is the initializer for a TCMA_MEMORY structure
#define TCMA_MEMORY_INITIALIZER(                       \
    sharedram, sharednv, objectcopiedtoram, bits_at_3) \
    {sharedram, sharednv, objectcopiedtoram, bits_at_3}
#else  // USE_BIT_FIELD_STRUCTURES
// This implements Table 2:36 TCMA_MEMORY using bit masking
typedef UINT32 TCMA_MEMORY;
#define TYPE_OF_TCMA_MEMORY UINT32
#define TCMA_MEMORY_sharedRAM ((TCMA_MEMORY)1 << 0)
#define TCMA_MEMORY_sharedNV ((TCMA_MEMORY)1 << 1)
#define TCMA_MEMORY_objectCopiedToRam ((TCMA_MEMORY)1 << 2)
// This is the initializer for a TCMA_MEMORY bit array.
#define TCMA_MEMORY_INITIALIZER(                       \
    sharedram, sharednv, objectcopiedtoram, bits_at_3) \
    (TCMA_MEMORY)(                                     \
        (sharedram << 0) + (sharednv << 1) + (objectcopiedtoram << 2))
#endif  // USE_BIT_FIELD_STRUCTURES

#define TYPE_OF_TCMA_CC UINT32
#define TCMA_CC_TO_UINT32(a) (*((UINT32 *)&(a)))
#define UINT32_TO_TCMA_CC(a) (*((TCMA_CC *)&(a)))
#define TCMA_CC_TO_BYTE_ARRAY(i, a) \
    UINT32_TO_BYTE_ARRAY((TCMA_CC_TO_UINT32(i)), (a))
#define BYTE_ARRAY_TO_TCMA_CC(i, a)         \
    {                                       \
        UINT32 x = BYTE_ARRAY_TO_UINT32(a); \
        i = UINT32_TO_TCMA_CC(x);           \
    }
#if USE_BIT_FIELD_STRUCTURES
typedef struct TCMA_CC {  // Table 2:37
    unsigned commandIndex : 16;
    unsigned Reserved_bits_at_16 : 6;
    unsigned nv : 1;
    unsigned extensive : 1;
    unsigned flushed : 1;
    unsigned cHandles : 3;
    unsigned rHandle : 1;
    unsigned V : 1;
    unsigned Reserved_bits_at_30 : 2;
} TCMA_CC;
// This is the initializer for a TCMA_CC structure
#define TCMA_CC_INITIALIZER(                                                            \
    commandindex, bits_at_16, nv, extensive, flushed, chandles, rhandle, v, bits_at_30) \
    {commandindex, bits_at_16, nv, extensive, flushed, chandles, rhandle, v, bits_at_30}
#else  // USE_BIT_FIELD_STRUCTURES
// This implements Table 2:37 TCMA_CC using bit masking
typedef UINT32 TCMA_CC;
#define TYPE_OF_TCMA_CC UINT32
#define TCMA_CC_commandIndex_SHIFT 0
#define TCMA_CC_commandIndex ((TCMA_CC)0xffff << 0)
#define TCMA_CC_nv ((TCMA_CC)1 << 22)
#define TCMA_CC_extensive ((TCMA_CC)1 << 23)
#define TCMA_CC_flushed ((TCMA_CC)1 << 24)
#define TCMA_CC_cHandles_SHIFT 25
#define TCMA_CC_cHandles ((TCMA_CC)0x7 << 25)
#define TCMA_CC_rHandle ((TCMA_CC)1 << 28)
#define TCMA_CC_V ((TCMA_CC)1 << 29)
#define TCMA_CC_reserved 0xc03f0000
// This is the initializer for a TCMA_CC bit array.
#define TCMA_CC_INITIALIZER(                                                            \
    commandindex, bits_at_16, nv, extensive, flushed, chandles, rhandle, v, bits_at_30) \
    (TCMA_CC)(                                                                          \
        (commandindex << 0) + (nv << 22) + (extensive << 23) +                          \
        (flushed << 24) + (chandles << 25) + (rhandle << 28) +                          \
        (v << 29))
#endif  // USE_BIT_FIELD_STRUCTURES

#define TYPE_OF_TCMA_MODES UINT32
#define TCMA_MODES_TO_UINT32(a) (*((UINT32 *)&(a)))
#define UINT32_TO_TCMA_MODES(a) (*((TCMA_MODES *)&(a)))
#define TCMA_MODES_TO_BYTE_ARRAY(i, a) \
    UINT32_TO_BYTE_ARRAY((TCMA_MODES_TO_UINT32(i)), (a))
#define BYTE_ARRAY_TO_TCMA_MODES(i, a)      \
    {                                       \
        UINT32 x = BYTE_ARRAY_TO_UINT32(a); \
        i = UINT32_TO_TCMA_MODES(x);        \
    }
#if USE_BIT_FIELD_STRUCTURES
typedef struct TCMA_MODES {  // Table 2:38
    unsigned FIPS_140_2 : 1;
    unsigned Reserved_bits_at_1 : 31;
} TCMA_MODES;
// This is the initializer for a TCMA_MODES structure
#define TCMA_MODES_INITIALIZER(fips_140_2, bits_at_1) {fips_140_2, bits_at_1}
#else  // USE_BIT_FIELD_STRUCTURES
// This implements Table 2:38 TCMA_MODES using bit masking
typedef UINT32 TCMA_MODES;
#define TYPE_OF_TCMA_MODES UINT32
#define TCMA_MODES_FIPS_140_2 ((TCMA_MODES)1 << 0)
// This is the initializer for a TCMA_MODES bit array.
#define TCMA_MODES_INITIALIZER(fips_140_2, bits_at_1) \
        (TCMA_MODES)(					\
    ((fips_140_2 << 0))
#endif  // USE_BIT_FIELD_STRUCTURES

#define TYPE_OF_TCMA_X509_KEY_USAGE UINT32
#define TCMA_X509_KEY_USAGE_TO_UINT32(a) (*((UINT32 *)&(a)))
#define UINT32_TO_TCMA_X509_KEY_USAGE(a) (*((TCMA_X509_KEY_USAGE *)&(a)))
#define TCMA_X509_KEY_USAGE_TO_BYTE_ARRAY(i, a) \
    UINT32_TO_BYTE_ARRAY((TCMA_X509_KEY_USAGE_TO_UINT32(i)), (a))
#define BYTE_ARRAY_TO_TCMA_X509_KEY_USAGE(i, a) \
    {                                           \
        UINT32 x = BYTE_ARRAY_TO_UINT32(a);     \
        i = UINT32_TO_TCMA_X509_KEY_USAGE(x);   \
    }
#if USE_BIT_FIELD_STRUCTURES
typedef struct TCMA_X509_KEY_USAGE {  // Table 2:39
    unsigned Reserved_bits_at_0 : 23;
    unsigned decipherOnly : 1;
    unsigned encipherOnly : 1;
    unsigned cRLSign : 1;
    unsigned keyCertSign : 1;
    unsigned keyAgreement : 1;
    unsigned dataEncipherment : 1;
    unsigned keyEncipherment : 1;
    unsigned nonrepudiation : 1;
    unsigned digitalSignature : 1;
} TCMA_X509_KEY_USAGE; 
/*Bits */
// This is the initializer for a TCMA_X509_KEY_USAGE structure
#define TCMA_X509_KEY_USAGE_INITIALIZER(                                                                                                            \
    bits_at_0, decipheronly, encipheronly, cRLSign, keycertsign, keyagreement, dataencipherment, keyencipherment, nonrepudiation, digitalsignature) \
    {bits_at_0, decipheronly, encipheronly, cRLSign, keycertsign, keyagreement, dataencipherment, keyencipherment, nonrepudiation, digitalsignature}
#else  // USE_BIT_FIELD_STRUCTURES

/*This implements Table 2:39 TCMA_X509_KEY_USAGE using bit masking */
typedef UINT32 TCMA_X509_KEY_USAGE;
#define TYPE_OF_TCMA_X509_KEY_USAGE UINT32
#define TCMA_X509_KEY_USAGE_decipherOnly ((TCMA_X509_KEY_USAGE)1 << 23)
#define TCMA_X509_KEY_USAGE_encipherOnly ((TCMA_X509_KEY_USAGE)1 << 24)
#define TCMA_X509_KEY_USAGE_cRLSign ((TCMA_X509_KEY_USAGE)1 << 25)
#define TCMA_X509_KEY_USAGE_keyCertSign ((TCMA_X509_KEY_USAGE)1 << 26)
#define TCMA_X509_KEY_USAGE_keyAgreement ((TCMA_X509_KEY_USAGE)1 << 27)
#define TCMA_X509_KEY_USAGE_dataEncipherment ((TCMA_X509_KEY_USAGE)1 << 28)
#define TCMA_X509_KEY_USAGE_keyEncipherment ((TCMA_X509_KEY_USAGE)1 << 29)
#define TCMA_X509_KEY_USAGE_nonrepudiation ((TCMA_X509_KEY_USAGE)1 << 30)
#define TCMA_X509_KEY_USAGE_digitalSignature ((TCMA_X509_KEY_USAGE)1 << 31)
//  This is the initializer for a TCMA_X509_KEY_USAGE bit array.
#define TCMA_X509_KEY_USAGE_INITIALIZER(                                                                                                            \
    bits_at_0, decipheronly, encipheronly, cRLSign, keycertsign, keyagreement, dataencipherment, keyencipherment, nonrepudiation, digitalsignature) \
    (TCMA_X509_KEY_USAGE)(                                                                                                                          \
        (decipheronly << 23) + (encipheronly << 24) +                                                                                               \
        (cRLSign << 25) + (keycertsign << 26) +                                                                                                     \
        (keyagreement << 27) + (dataencipherment << 28) +                                                                                           \
        (keyencipherment << 29) + (nonrepudiation << 30) +                                                                                          \
        (digitalsignature << 31))
#endif  // USE_BIT_FIELD_STRUCTURES

#define TYPE_OF_TCMA_ACT UINT32
#define TCMA_ACT_TO_UINT32(a) (*((UINT32 *)&(a)))
#define UINT32_TO_TCMA_ACT(a) (*((TCMA_ACT *)&(a)))
#define TCMA_ACT_TO_BYTE_ARRAY(i, a) \
    UINT32_TO_BYTE_ARRAY((TCMA_ACT_TO_UINT32(i)), (a))
#define BYTE_ARRAY_TO_TCMA_ACT(i, a)        \
    {                                       \
        UINT32 x = BYTE_ARRAY_TO_UINT32(a); \
        i = UINT32_TO_TCMA_ACT(x);          \
    }
#if USE_BIT_FIELD_STRUCTURES
typedef struct TCMA_ACT {  // Table 2:40
    unsigned signaled : 1;
    unsigned preserveSignaled : 1;
    unsigned Reserved_bits_at_2 : 30;
} TCMA_ACT; 
/*Bits */
// This is the initializer for a TCMA_ACT structure
#define TCMA_ACT_INITIALIZER(signaled, preservesignaled, bits_at_2) \
    {signaled, preservesignaled, bits_at_2}
#else  // USE_BIT_FIELD_STRUCTURES
// This implements Table 2:40 TCMA_ACT using bit masking
typedef UINT32 TCMA_ACT;
#define TYPE_OF_TCMA_ACT UINT32
#define TCMA_ACT_signaled ((TCMA_ACT)1 << 0)
#define TCMA_ACT_preserveSignaled ((TCMA_ACT)1 << 1)
// This is the initializer for a TCMA_ACT bit array.
#define TCMA_ACT_INITIALIZER(signaled, preservesignaled, bits_at_2) \
    (TCMA_ACT)(                                                     \
        (signaled << 0) + (preservesignaled << 1))
#endif  // USE_BIT_FIELD_STRUCTURES


/*Table 2:39 - Definition of TCMI_YES_NO Type  */
typedef BYTE TCMI_YES_NO;

/*Table 2:40 - Definition of TCMI_DH_OBJECT Type  */
typedef TCM_HANDLE TCMI_DH_OBJECT;

/*Table 2:41 - Definition of TCMI_DH_PARENT Type  */
typedef TCM_HANDLE TCMI_DH_PARENT;

/*Table 2:42 - Definition of TCMI_DH_PERSISTENT Type  */
typedef TCM_HANDLE TCMI_DH_PERSISTENT;

/*Table 2:43 - Definition of TCMI_DH_ENTITY Type  */
typedef TCM_HANDLE TCMI_DH_ENTITY;

/*Table 2:44 - Definition of TCMI_DH_PCR Type  */
typedef TCM_HANDLE TCMI_DH_PCR;

/*Table 2:45 - Definition of TCMI_SH_AUTH_SESSION Type  */
typedef TCM_HANDLE TCMI_SH_AUTH_SESSION;

/*Table 2:46 - Definition of TCMI_SH_HMAC Type  */
typedef TCM_HANDLE TCMI_SH_HMAC;

/*Table 2:47 - Definition of TCMI_SH_POLICY Type  */
typedef TCM_HANDLE TCMI_SH_POLICY;

/*Table 2:48 - Definition of TCMI_DH_CONTEXT Type  */
typedef TCM_HANDLE TCMI_DH_CONTEXT;

/*Table 49 - Definition of (TCM_HANDLE) TCMI_DH_SAVED Type  */
typedef TCM_HANDLE TCMI_DH_SAVED;

/*Table 2:49 - Definition of TCMI_RH_HIERARCHY Type  */
typedef TCM_HANDLE TCMI_RH_HIERARCHY;

/*Table 2:50 - Definition of TCMI_RH_ENABLES Type  */
typedef TCM_HANDLE TCMI_RH_ENABLES;

/*Table 2:51 - Definition of TCMI_RH_HIERARCHY_AUTH Type  */
typedef TCM_HANDLE TCMI_RH_HIERARCHY_AUTH;

/*Table 2:55 - Definition of TCMI_RH_HIERARCHY_POLICY Type  */
typedef TCM_HANDLE TCMI_RH_HIERARCHY_POLICY;

/*Table 2:52 - Definition of TCMI_RH_PLATFORM Type  */
typedef TCM_HANDLE TCMI_RH_PLATFORM;

/*Table 2:53 - Definition of TCMI_RH_OWNER Type  */
typedef TCM_HANDLE TCMI_RH_OWNER;

/*Table 2:54 - Definition of TCMI_RH_ENDORSEMENT Type  */
typedef TCM_HANDLE TCMI_RH_ENDORSEMENT;

/*Table 2:55 - Definition of TCMI_RH_PROVISION Type  */
typedef TCM_HANDLE TCMI_RH_PROVISION;

/*Table 2:56 - Definition of TCMI_RH_CLEAR Type  */
typedef TCM_HANDLE TCMI_RH_CLEAR;

/*Table 2:57 - Definition of TCMI_RH_NV_AUTH Type  */
typedef TCM_HANDLE TCMI_RH_NV_AUTH;

/*Table 2:58 - Definition of TCMI_RH_LOCKOUT Type  */
typedef TCM_HANDLE TCMI_RH_LOCKOUT;

/*Table 2:59 - Definition of TCMI_RH_NV_INDEX Type  */
typedef TCM_HANDLE TCMI_RH_NV_INDEX;

/*Table 2:60 - Definition of TCMI_RH_AC Type  */
typedef TCM_HANDLE TCMI_RH_AC;

/*Table 2:65 - Definition of TCMI_RH_ACT Type  */
typedef TCM_HANDLE TCMI_RH_ACT;

/*Table 2:61 - Definition of TCMI_ALG_HASH Type  */
typedef TCM_ALG_ID TCMI_ALG_HASH;

/*Table 2:62 - Definition of TCMI_ALG_ASYM Type  */
typedef TCM_ALG_ID TCMI_ALG_ASYM;

/*Table 2:63 - Definition of TCMI_ALG_SYM Type  */
typedef TCM_ALG_ID TCMI_ALG_SYM;

/*Table 2:64 - Definition of TCMI_ALG_SYM_OBJECT Type  */
typedef TCM_ALG_ID TCMI_ALG_SYM_OBJECT;

/*Table 2:65 - Definition of TCMI_ALG_SYM_MODE Type  */
typedef TCM_ALG_ID TCMI_ALG_SYM_MODE;

/*Table 2:66 - Definition of TCMI_ALG_KDF Type  */
typedef TCM_ALG_ID TCMI_ALG_KDF;

/*Table 2:67 - Definition of TCMI_ALG_SIG_SCHEME Type  */
typedef TCM_ALG_ID TCMI_ALG_SIG_SCHEME;

/*Table 2:68 - Definition of TCMI_ECC_KEY_EXCHANGE Type  */
typedef TCM_ALG_ID TCMI_ECC_KEY_EXCHANGE;

/*Table 2:69 - Definition of TCMI_ST_COMMAND_TAG Type  */
typedef TCM_ST TCMI_ST_COMMAND_TAG;

/*Table 2:70 - Definition of TCMI_ALG_MAC_SCHEME Type */
typedef TCM_ALG_ID TCMI_ALG_MAC_SCHEME;

/*le 2:70 - Definition of TCMI_ALG_CIPHER_MODE Type */
typedef TCM_ALG_ID TCMI_ALG_CIPHER_MODE;

/*Table 2:70 - Definition of TCMS_EMPTY Structure  */
typedef BYTE TCMS_EMPTY;

/*Table 2:71 - Definition of TCMS_ALGORITHM_DESCRIPTION Structure  */
typedef struct {
    TCM_ALG_ID alg;
    TCMA_ALGORITHM attributes;
} TCMS_ALGORITHM_DESCRIPTION;

/*Table 2:71 - Definition of TCMU_HA Union  */
#define ALG_SM3_256 1
#define SM3_256_DIGEST_SIZE 32
typedef union {
#if ALG_SHA1
    BYTE sha1[SHA1_DIGEST_SIZE];
#endif  // ALG_SHA1
#if ALG_SHA256
    BYTE sha256[SHA256_DIGEST_SIZE];
#endif  // ALG_SHA256
#if ALG_SHA384
    BYTE sha384[SHA384_DIGEST_SIZE];
#endif  // ALG_SHA384
#if ALG_SHA512
    BYTE sha512[SHA512_DIGEST_SIZE];
#endif  // ALG_SHA512
#if ALG_SM3_256
    BYTE sm3_256[SM3_256_DIGEST_SIZE];
#endif  // ALG_SM3_256
#if ALG_SHA3_256
    BYTE sha3_256[SHA3_256_DIGEST_SIZE];
#endif  // ALG_SHA3_256
#if ALG_SHA3_384
    BYTE sha3_384[SHA3_384_DIGEST_SIZE];
#endif  // ALG_SHA3_384
#if ALG_SHA3_512
    BYTE sha3_512[SHA3_512_DIGEST_SIZE];
#endif  // ALG_SHA3_512
} TCMU_HA;

#include <TCMB.h>
#include <TcmProfile.h>


/*Table 2:72 - Definition of TCMT_HA Structure  */
typedef struct {
    TCMI_ALG_HASH hashAlg;
    TCMU_HA digest;
} TCMT_HA;


/*Table 2:73 - Definition of TCM2B_DIGEST Structure  */
typedef struct {
    UINT16 size;
    BYTE buffer[sizeof(TCMU_HA)];
} TCM2B_DIGEST;

/*Table 2:74 - Definition of TCM2B_DATA Structure  */
typedef union {
    UINT16 size;
    BYTE buffer[sizeof(TCMT_HA)];
} TCM2B_DATA;

/*Table 2:75 - Definition of Types for TCM2B_NONCE */
typedef TCM2B_DIGEST TCM2B_NONCE;

/*Table 2:76 - Definition of Types for TCM2B_AUTH */
typedef TCM2B_DIGEST TCM2B_AUTH;

/*Table 2:77 - Definition of Types for TCM2B_OPERAND */
typedef TCM2B_DIGEST TCM2B_OPERAND;

/*Table 2:78 - Definition of TCM2B_EVENT Structure  */
typedef union {
    UINT16 size;
    BYTE buffer[1024];

} TCM2B_EVENT;

/*Table 2:79 - Definition of TCM2B_MAX_BUFFER Structure  */
typedef struct {
    UINT16 size;
    BYTE buffer[MAX_DIGEST_BUFFER];
} TCM2B_MAX_BUFFER;

/*Table 2:80 - Definition of TCM2B_MAX_NV_BUFFER Structure  */
typedef struct {
    UINT16 size;
    BYTE buffer[MAX_NV_BUFFER_SIZE];
} TCM2B_MAX_NV_BUFFER;

/*Table 2:82 - Definition of TCM2B_TIMEOUT Structure */
typedef union {
    UINT16 size;
    BYTE buffer[sizeof(UINT64)];
} TCM2B_TIMEOUT;

// --------------------------------------------- ADD ----------------------------------------------------------------

#include <TcmAlgorithmDefines.h>

#ifndef MAX
#define MAX(a, b) ((a) > (b) ? (a) : (b))
#endif

#ifndef MIN
#define MIN(a, b) (((a) < (b)) ? (a) : (b))
#endif

#define MAX_SYM_BLOCK_SIZE \
    (MAX(AES_MAX_BLOCK_SIZE, MAX(CAMELLIA_MAX_BLOCK_SIZE, MAX(SM4_MAX_BLOCK_SIZE, MAX(TDES_MAX_BLOCK_SIZE, 0)))))

#define PCR_SELECT_MIN ((PLATFORM_PCR + 7) / 8)
#define PCR_SELECT_MAX ((IMPLEMENTATION_PCR + 7) / 8)
#define MAX_ORDERLY_COUNT ((1 << ORDERLY_BITS) - 1)
#define RSA_MAX_PRIME (MAX_RSA_KEY_BYTES / 2)
#define RSA_PRIVATE_SIZE (RSA_MAX_PRIME * 5)

// --------------------------------------------- ADD ----------------------------------------------------------------


/*Table 2:82 - Definition of TCM2B_IV Structure  */
typedef union {
    UINT16 size;
    BYTE buffer[MAX_SYM_BLOCK_SIZE];
} TCM2B_IV;

/*Table 2:83 - Definition of TCMU_NAME Union  */
typedef union {
    TCMT_HA digest;
    TCM_HANDLE handle;
} TCMU_NAME;

/*Table 2:84 - Definition of TCM2B_NAME Structure  */
typedef union {
    UINT16 size;
    BYTE name[sizeof(TCMU_NAME)];
} TCM2B_NAME;


/*Table 2:85 - Definition of TCMS_PCR_SELECT Structure  */
typedef struct {
    UINT8 sizeofSelect;
    BYTE pcrSelect[PCR_SELECT_MAX];
} TCMS_PCR_SELECT;


/*Table 2:86 - Definition of TCMS_PCR_SELECTION Structure  */
typedef struct {
    TCMI_ALG_HASH hash;
    UINT8 sizeofSelect;
    BYTE pcrSelect[PCR_SELECT_MAX];
} TCMS_PCR_SELECTION;


/*Table 2:89 - Definition of TCMT_TK_CREATION Structure  */
typedef struct {
    TCM_ST tag;
    TCMI_RH_HIERARCHY hierarchy;
    TCM2B_DIGEST digest;
} TCMT_TK_CREATION;

/*Table 2:90 - Definition of TCMT_TK_VERIFIED Structure  */
typedef struct {
    TCM_ST tag;
    TCMI_RH_HIERARCHY hierarchy;
    TCM2B_DIGEST digest;
} TCMT_TK_VERIFIED;

/*Table 2:91 - Definition of TCMT_TK_AUTH Structure  */
typedef struct {
    TCM_ST tag;
    TCMI_RH_HIERARCHY hierarchy;
    TCM2B_DIGEST digest;
} TCMT_TK_AUTH;

/*Table 2:92 - Definition of TCMT_TK_HASHCHECK Structure  */
typedef struct {
    TCM_ST tag;
    TCMI_RH_HIERARCHY hierarchy;
    TCM2B_DIGEST digest;
} TCMT_TK_HASHCHECK;

/*Table 2:93 - Definition of TCMS_ALG_PROPERTY Structure  */
typedef struct {
    TCM_ALG_ID alg;
    TCMA_ALGORITHM algProperties;
} TCMS_ALG_PROPERTY;

/*Table 2:94 - Definition of TCMS_TAGGED_PROPERTY Structure  */
typedef struct {
    TCM_PT property;
    UINT32 value;
} TCMS_TAGGED_PROPERTY;

/*Table 2:95 - Definition of TCMS_TAGGED_PCR_SELECT Structure  */
typedef struct {
    TCM_PT_PCR tag;
    UINT8 sizeofSelect;
    BYTE pcrSelect[PCR_SELECT_MAX];
} TCMS_TAGGED_PCR_SELECT;

/*Table 2:96 - Definition of TCMS_TAGGED_POLICY Structure  */
typedef struct {
    TCM_HANDLE handle;
    TCMT_HA policyHash;
} TCMS_TAGGED_POLICY;

/*Table 105 - Definition of TCMS_ACT_DATA Structure <OUT> */
typedef struct {
    TCM_HANDLE handle;
    UINT32 timeout;
    TCMA_ACT attributes;
} TCMS_ACT_DATA;

// --------------------------------------------- ADD ----------------------------------------------------------------

#ifndef _CAPABILITIES_H
#define _CAPABILITIES_H

#define MAX_CAP_DATA (MAX_CAP_BUFFER - sizeof(TCM_CAP) - sizeof(UINT32))
#define MAX_CAP_ALGS (MAX_CAP_DATA / sizeof(TCMS_ALG_PROPERTY))
#define MAX_CAP_HANDLES (MAX_CAP_DATA / sizeof(TCM_HANDLE))
#define MAX_CAP_CC (MAX_CAP_DATA / sizeof(TCM_CC))
#define MAX_TCM_PROPERTIES (MAX_CAP_DATA / sizeof(TCMS_TAGGED_PROPERTY))
#define MAX_PCR_PROPERTIES (MAX_CAP_DATA / sizeof(TCMS_TAGGED_PCR_SELECT))
#define MAX_ECC_CURVES (MAX_CAP_DATA / sizeof(TCM_ECC_CURVE))
#define MAX_TAGGED_POLICIES (MAX_CAP_DATA / sizeof(TCMS_TAGGED_POLICY))
#define MAX_ACT_DATA (MAX_CAP_DATA / sizeof(TCMS_ACT_DATA))
#define MAX_AC_CAPABILITIES (MAX_CAP_DATA / sizeof(TCMS_AC_OUTPUT))

#endif
// --------------------------------------------- ADD ----------------------------------------------------------------


/*Table 2:97 - Definition of TCML_CC Structure  */
typedef struct {
    UINT32 count;
    TCM_CC commandCodes[MAX_CAP_CC];
} TCML_CC;

/*Table 2:98 - Definition of TCML_CCA Structure  */
typedef struct {
    UINT32 count;
    TCMA_CC commandAttributes[MAX_CAP_CC];
} TCML_CCA;

/*Table 2:99 - Definition of TCML_ALG Structure  */
typedef struct {
    UINT32 count;
    TCM_ALG_ID algorithms[MAX_ALG_LIST_SIZE];
} TCML_ALG;

/*Table 2:100 - Definition of TCML_HANDLE Structure  */
typedef struct {
    UINT32 count;
    TCM_HANDLE handle[MAX_CAP_HANDLES];
} TCML_HANDLE;

/*Table 2:101 - Definition of TCML_DIGEST Structure  */
typedef struct {
    UINT32 count;
    TCM2B_DIGEST digests[8];
} TCML_DIGEST;

/*Table 2:102 - Definition of TCML_DIGEST_VALUES Structure  */
typedef struct {
    UINT32 count;
    TCMT_HA digests[HASH_COUNT];
} TCML_DIGEST_VALUES;

/*Table 2:104 - Definition of TCML_PCR_SELECTION Structure  */
typedef struct {
    UINT32 count;
    TCMS_PCR_SELECTION pcrSelections[HASH_COUNT];
} TCML_PCR_SELECTION;

/*Table 2:105 - Definition of TCML_ALG_PROPERTY Structure  */
typedef struct {
    UINT32 count;
    TCMS_ALG_PROPERTY algProperties[MAX_CAP_ALGS];
} TCML_ALG_PROPERTY;

/*Table 2:106 - Definition of TCML_TAGGED_TCM_PROPERTY Structure  */
typedef struct {
    UINT32 count;
    TCMS_TAGGED_PROPERTY tcmProperty[MAX_TCM_PROPERTIES];
} TCML_TAGGED_TCM_PROPERTY;

/*Table 2:107 - Definition of TCML_TAGGED_PCR_PROPERTY Structure  */
typedef struct {
    UINT32 count;
    TCMS_TAGGED_PCR_SELECT pcrProperty[MAX_PCR_PROPERTIES];
} TCML_TAGGED_PCR_PROPERTY;

/*Table 2:108 - Definition of TCML_ECC_CURVE Structure  */
typedef struct {
    UINT32 count;
    TCM_ECC_CURVE eccCurves[MAX_ECC_CURVES];
} TCML_ECC_CURVE;

/*Table 2:109 - Definition of TCML_TAGGED_POLICY Structure  */
typedef struct {
    UINT32 count;
    TCMS_TAGGED_POLICY policies[MAX_TAGGED_POLICIES];
} TCML_TAGGED_POLICY;

/*Table 2:118 - Definition of TCML_ACT_DATA Structure <OUT> */
typedef struct {
    UINT32 count;
    TCMS_ACT_DATA actData[MAX_ACT_DATA];
} TCML_ACT_DATA;

/*Table 2:110 - Definition of TCMU_CAPABILITIES Union  */
typedef union {
    TCML_ALG_PROPERTY algorithms;
    TCML_HANDLE handles;
    TCML_CCA command;
    TCML_CC ppCommands;
    TCML_CC auditCommands;
    TCML_PCR_SELECTION assignedPCR;
    TCML_TAGGED_TCM_PROPERTY tcmProperties;
    TCML_TAGGED_PCR_PROPERTY pcrProperties;
#if ALG_ECC
    TCML_ECC_CURVE eccCurves;
#endif  // ALG_ECC
    TCML_TAGGED_POLICY authPolicies;
    TCML_ACT_DATA actData;
} TCMU_CAPABILITIES;

/*Table 2:111 - Definition of TCMS_CAPABILITY_DATA Structure  */
typedef struct {
    TCM_CAP capability;
    TCMU_CAPABILITIES data;
} TCMS_CAPABILITY_DATA;

/*Table 2:112 - Definition of TCMS_CLOCK_INFO Structure  */
typedef struct {
    UINT64 clock;
    UINT32 resetCount;
    UINT32 restartCount;
    TCMI_YES_NO safe;
} TCMS_CLOCK_INFO;

/*Table 2:113 - Definition of TCMS_TIME_INFO Structure  */
typedef struct {
    UINT64 time;
    TCMS_CLOCK_INFO clockInfo;
} TCMS_TIME_INFO;

/*Table 2:114 - Definition of TCMS_TIME_ATTEST_INFO Structure  */
typedef struct {
    TCMS_TIME_INFO time;
    UINT64 firmwareVersion;
} TCMS_TIME_ATTEST_INFO;

/*Table 2:115 - Definition of TCMS_CERTIFY_INFO Structure  */
typedef struct {
    TCM2B_NAME name;
    TCM2B_NAME qualifiedName;
} TCMS_CERTIFY_INFO;

/*Table 2:116 - Definition of TCMS_QUOTE_INFO Structure  */
typedef struct {
    TCML_PCR_SELECTION pcrSelect;
    TCM2B_DIGEST pcrDigest;
} TCMS_QUOTE_INFO;

/*Table 2:117 - Definition of TCMS_COMMAND_AUDIT_INFO Structure  */
typedef struct {
    UINT64 auditCounter;
    TCM_ALG_ID digestAlg;
    TCM2B_DIGEST auditDigest;
    TCM2B_DIGEST commandDigest;
} TCMS_COMMAND_AUDIT_INFO;

/*Table 2:118 - Definition of TCMS_SESSION_AUDIT_INFO Structure  */
typedef struct {
    TCMI_YES_NO exclusiveSession;
    TCM2B_DIGEST sessionDigest;
} TCMS_SESSION_AUDIT_INFO;

/*Table 2:119 - Definition of TCMS_CREATION_INFO Structure  */
typedef struct {
    TCM2B_NAME objectName;
    TCM2B_DIGEST creationHash;
} TCMS_CREATION_INFO;

/*Table 2:120 - Definition of TCMS_NV_CERTIFY_INFO Structure  */
typedef struct {
    TCM2B_NAME indexName;
    UINT16 offset;
    TCM2B_MAX_NV_BUFFER nvContents;
} TCMS_NV_CERTIFY_INFO;

/*Table 125 - Definition of TCMS_NV_DIGEST_CERTIFY_INFO Structure <OUT> */
typedef struct {
    TCM2B_NAME indexName;
    TCM2B_DIGEST nvDigest;
} TCMS_NV_DIGEST_CERTIFY_INFO;

/*Table 2:121 - Definition of TCMI_ST_ATTEST Type  */
typedef TCM_ST TCMI_ST_ATTEST;

/*Table 2:122 - Definition of TCMU_ATTEST Union  */
typedef union {
    TCMS_CERTIFY_INFO certify;
    TCMS_CREATION_INFO creation;
    TCMS_QUOTE_INFO quote;
    TCMS_COMMAND_AUDIT_INFO commandAudit;
    TCMS_SESSION_AUDIT_INFO sessionAudit;
    TCMS_TIME_ATTEST_INFO time;
    TCMS_NV_CERTIFY_INFO nv;
    TCMS_NV_DIGEST_CERTIFY_INFO nvDigest;
} TCMU_ATTEST;

/*Table 2:123 - Definition of TCMS_ATTEST Structure  */
typedef struct {
    TCM_CONSTANTS32 magic;
    TCMI_ST_ATTEST type;
    TCM2B_NAME qualifiedSigner;
    TCM2B_DATA extraData;
    TCMS_CLOCK_INFO clockInfo;
    UINT64 firmwareVersion;
    TCMU_ATTEST attested;
} TCMS_ATTEST;

/*Table 2:124 - Definition of TCM2B_ATTEST Structure  */
typedef union {
    UINT16 size;
    BYTE attestationData[sizeof(TCMS_ATTEST)];

} TCM2B_ATTEST;

/*Table 2:125 - Definition of TCMS_AUTH_COMMAND Structure  */
typedef struct {
    UINT32 size;
    TCMI_SH_AUTH_SESSION sessionHandle;  // 会话句柄
    TCM2B_NONCE nonce;                   // 会话nonce,可能是空缓冲区
    TCMA_SESSION sessionAttributes;      // 会话属性
    TCM2B_AUTH hmac;                     // HMAC、密码或空授权
} TCMS_AUTH_COMMAND;

/*Table 2:126 - Definition of TCMS_AUTH_RESPONSE Structure  */
typedef struct {
    TCM2B_NONCE nonce;
    TCMA_SESSION sessionAttributes;
    TCM2B_AUTH hmac;
} TCMS_AUTH_RESPONSE;

/*Table 2:127 - Definition of TCMI_TDES_KEY_BITS Type  */
typedef TCM_KEY_BITS TCMI_TDES_KEY_BITS;

/*Table 2:127 - Definition of TCMI_AES_KEY_BITS Type  */
typedef TCM_KEY_BITS TCMI_AES_KEY_BITS;

/*Table 2:127 - Definition of TCMI_SM4_KEY_BITS Type  */
typedef TCM_KEY_BITS TCMI_SM4_KEY_BITS;

/*Table 2:127 - Definition of TCMI_CAMELLIA_KEY_BITS Type  */
typedef TCM_KEY_BITS TCMI_CAMELLIA_KEY_BITS;

/*Table 2:128 - Definition of TCMU_SYM_KEY_BITS Union  */
typedef union {
#if ALG_TDES
    TCMI_TDES_KEY_BITS tdes;
#endif  // ALG_TDES
#if ALG_AES
    TCMI_AES_KEY_BITS aes;
#endif  // ALG_AES
#if ALG_SM4
    TCMI_SM4_KEY_BITS sm4;
#endif  // ALG_SM4
#if ALG_CAMELLIA
    TCMI_CAMELLIA_KEY_BITS camellia;
#endif  // ALG_CAMELLIA
    TCM_KEY_BITS sym;
#if ALG_XOR
    TCMI_ALG_HASH xorr;
#endif  // ALG_XOR
} TCMU_SYM_KEY_BITS;

/*Table 2:129 - Definition of TCMU_SYM_MODE Union  */
typedef union {
#if ALG_TDES
    TCMI_ALG_SYM_MODE tdes;
#endif  // ALG_TDES
#if ALG_AES
    TCMI_ALG_SYM_MODE aes;
#endif  // ALG_AES
#if ALG_SM4
    TCMI_ALG_SYM_MODE sm4;
#endif  // ALG_SM4
#if ALG_CAMELLIA
    TCMI_ALG_SYM_MODE camellia;
#endif  // ALG_CAMELLIA
    TCMI_ALG_SYM_MODE sym;
} TCMU_SYM_MODE;

/*Table 2:131 - Definition of TCMT_SYM_DEF Structure  */
typedef struct {
    TCMI_ALG_SYM algorithm;
    TCMU_SYM_KEY_BITS keyBits;
    TCMU_SYM_MODE mode;
} TCMT_SYM_DEF;

/*Table 2:132 - Definition of TCMT_SYM_DEF_OBJECT Structure  */
typedef struct {
    TCMI_ALG_SYM_OBJECT algorithm;
    TCMU_SYM_KEY_BITS keyBits;
    TCMU_SYM_MODE mode;
} TCMT_SYM_DEF_OBJECT;


/*Table 2:133 - Definition of TCM2B_SYM_KEY Structure  */
typedef union {
    UINT16 size;
    BYTE buffer[MAX_SYM_KEY_BYTES];

} TCM2B_SYM_KEY;

/*Table 2:134 - Definition of TCMS_SYMCIPHER_PARMS Structure  */
typedef struct {
    TCMT_SYM_DEF_OBJECT sym;
} TCMS_SYMCIPHER_PARMS;

// --------------------------------------------- ADD ----------------------------------------------------------------

#ifndef BITS_TO_BYTES
#define BITS_TO_BYTES(bits) (((bits) + 7) >> 3)
#endif

#ifndef LABEL_MAX_BUFFER
#define LABEL_MAX_BUFFER MIN(32, MAX(MAX_ECC_KEY_BYTES, MAX_DIGEST_SIZE))
#endif
// --------------------------------------------- ADD ----------------------------------------------------------------


/*Table 2:135 - Definition of TCM2B_LABEL Structure  */
typedef union {
    UINT16 size;
    BYTE buffer[LABEL_MAX_BUFFER];

} TCM2B_LABEL;

/*Table 2:136 - Definition of TCMS_DERIVE Structure  */
typedef struct {
    TCM2B_LABEL label;
    TCM2B_LABEL context;
} TCMS_DERIVE;

/*Table 2:137 - Definition of TCM2B_DERIVE Structure  */
typedef union {
    UINT16 size;
    BYTE buffer[sizeof(TCMS_DERIVE)];

} TCM2B_DERIVE;

/*Table 2:138 - Definition of TCMU_SENSITIVE_CREATE Union  */
typedef union {
    BYTE create[MAX_SYM_DATA];
    TCMS_DERIVE derive;
} TCMU_SENSITIVE_CREATE;

/*Table 2:139 - TCM2B_SENSITIVE_DATA结构的定义  */
typedef union {
    UINT16 size;
    BYTE buffer[sizeof(TCMU_SENSITIVE_CREATE)];
} TCM2B_SENSITIVE_DATA;

/*Table 2:140 - Definition of TCMS_SENSITIVE_CREATE Structure  */
typedef struct {
    TCM2B_AUTH userAuth;
    TCM2B_SENSITIVE_DATA data;
} TCMS_SENSITIVE_CREATE;

/*Table 2:141 - Definition of TCM2B_SENSITIVE_CREATE Structure  */
typedef struct {
    UINT16 size;
    TCMS_SENSITIVE_CREATE sensitive;
} TCM2B_SENSITIVE_CREATE;


/*Table 2:142 - Definition of TCMS_SCHEME_HASH Structure  */
typedef struct {
    TCMI_ALG_HASH hashAlg;
} TCMS_SCHEME_HASH;

/*Table 2:143 - Definition of TCMS_SCHEME_ECDAA Structure  */
typedef struct {
    TCMI_ALG_HASH hashAlg;
    UINT16 count;
} TCMS_SCHEME_ECDAA;

/*Table 2:144 - Definition of TCMI_ALG_KEYEDHASH_SCHEME Type  */
typedef TCM_ALG_ID TCMI_ALG_KEYEDHASH_SCHEME;

/*Table 2:145 - Definition of Types for HMAC_SIG_SCHEME */
typedef TCMS_SCHEME_HASH TCMS_SCHEME_HMAC;

/*Table 2:146 - Definition of TCMS_SCHEME_XOR Structure  */
typedef struct {
    TCMI_ALG_HASH hashAlg;
    TCMI_ALG_KDF kdf;
} TCMS_SCHEME_XOR;

/*Table 2:147 - Definition of TCMU_SCHEME_KEYEDHASH Union  */
typedef union {
#if ALG_HMAC
    TCMS_SCHEME_HMAC hmac;
#endif  // ALG_HMAC
#if ALG_XOR
    TCMS_SCHEME_XOR xorr;
#endif  // ALG_XOR
} TCMU_SCHEME_KEYEDHASH;

/*Table 2:148 - Definition of TCMT_KEYEDHASH_SCHEME Structure  */
typedef struct {
    TCMI_ALG_KEYEDHASH_SCHEME scheme;
    TCMU_SCHEME_KEYEDHASH details;
} TCMT_KEYEDHASH_SCHEME;

/*Table 2:149 - Definition of Types for RSA Signature Schemes */
typedef TCMS_SCHEME_HASH TCMS_SIG_SCHEME_RSASSA;
typedef TCMS_SCHEME_HASH TCMS_SIG_SCHEME_RSAPSS;

/*Table 2:150 - Definition of Types for ECC Signature Schemes */
typedef TCMS_SCHEME_HASH TCMS_SIG_SCHEME_ECDSA;
typedef TCMS_SCHEME_HASH TCMS_SIG_SCHEME_SM2;
typedef TCMS_SCHEME_HASH TCMS_SIG_SCHEME_ECSCHNORR;
typedef TCMS_SCHEME_ECDAA TCMS_SIG_SCHEME_ECDAA;

/*Table 2:151 - Definition of TCMU_SIG_SCHEME Union  */
typedef union {
#if ALG_ECC
    TCMS_SIG_SCHEME_ECDAA ecdaa;
#endif  // ALG_ECC
#if ALG_RSASSA
    TCMS_SIG_SCHEME_RSASSA rsassa;
#endif  // ALG_RSASSA
#if ALG_RSAPSS
    TCMS_SIG_SCHEME_RSAPSS rsapss;
#endif  // ALG_RSAPSS
#if ALG_ECDSA
    TCMS_SIG_SCHEME_ECDSA ecdsa;
#endif  // ALG_ECDSA
#if ALG_SM2
    TCMS_SIG_SCHEME_SM2 sm2;
#endif  // ALG_SM2
#if ALG_ECSCHNORR
    TCMS_SIG_SCHEME_ECSCHNORR ecschnorr;
#endif  // ALG_ECSCHNORR
#if ALG_HMAC
    TCMS_SCHEME_HMAC hmac;
#endif  // ALG_HMAC
    TCMS_SCHEME_HASH any;
} TCMU_SIG_SCHEME;

/*Table 2:152 - Definition of TCMT_SIG_SCHEME Structure  */
typedef struct {
    TCMI_ALG_SIG_SCHEME scheme;
    TCMU_SIG_SCHEME details;
} TCMT_SIG_SCHEME;

/*Table 2:153 - Definition of Types for Encryption Schemes */
typedef TCMS_SCHEME_HASH TCMS_ENC_SCHEME_OAEP;
typedef TCMS_EMPTY TCMS_ENC_SCHEME_RSAES;

/*Table 2:154 - Definition of Types for ECC Key Exchange */
typedef TCMS_SCHEME_HASH TCMS_KEY_SCHEME_ECDH;
typedef TCMS_SCHEME_HASH TCMS_KEY_SCHEME_ECMQV;

/*Table 2:155 - Definition of Types for KDF Schemes */
typedef TCMS_SCHEME_HASH TCMS_KDF_SCHEME_MGF1;
typedef TCMS_SCHEME_HASH TCMS_KDF_SCHEME_KDF1_SP800_56A;
typedef TCMS_SCHEME_HASH TCMS_KDF_SCHEME_KDF2;
typedef TCMS_SCHEME_HASH TCMS_KDF_SCHEME_KDF1_SP800_108;

/*Table 2:156 - Definition of TCMU_KDF_SCHEME Union  */
typedef union {
#if ALG_MGF1
    TCMS_KDF_SCHEME_MGF1 mgf1;
#endif  // ALG_MGF1
#if ALG_KDF1_SP800_56A
    TCMS_KDF_SCHEME_KDF1_SP800_56A kdf1_sp800_56a;
#endif  // ALG_KDF1_SP800_56A
#if ALG_KDF2
    TCMS_KDF_SCHEME_KDF2 kdf2;
#endif  // ALG_KDF2
#if ALG_KDF1_SP800_108
    TCMS_KDF_SCHEME_KDF1_SP800_108 kdf1_sp800_108;
#endif  // ALG_KDF1_SP800_108
    TCMS_SCHEME_HASH anyKdf;
} TCMU_KDF_SCHEME;

/*Table 2:157 - Definition of TCMT_KDF_SCHEME Structure  */
typedef struct {
    TCMI_ALG_KDF scheme;
    TCMU_KDF_SCHEME details;
} TCMT_KDF_SCHEME;

/*Table 2:158 - Definition of TCMI_ALG_ASYM_SCHEME Type  */
typedef TCM_ALG_ID TCMI_ALG_ASYM_SCHEME;

/*Table 2:159 - Definition of TCMU_ASYM_SCHEME Union  */
typedef union {
#if ALG_ECDH
    TCMS_KEY_SCHEME_ECDH ecdh;
#endif  // ALG_ECDH
#if ALG_ECMQV
    TCMS_KEY_SCHEME_ECMQV ecmqv;
#endif  // ALG_ECMQV
#if ALG_ECC
    TCMS_SIG_SCHEME_ECDAA ecdaa;
#endif  // ALG_ECC
#if ALG_RSASSA
    TCMS_SIG_SCHEME_RSASSA rsassa;
#endif  // ALG_RSASSA
#if ALG_RSAPSS
    TCMS_SIG_SCHEME_RSAPSS rsapss;
#endif  // ALG_RSAPSS
#if ALG_ECDSA
    TCMS_SIG_SCHEME_ECDSA ecdsa;
#endif  // ALG_ECDSA

// #define ALG_SM2 1
#if ALG_SM2
    TCMS_SIG_SCHEME_SM2 sm2;
#endif  // ALG_SM2
#if ALG_ECSCHNORR
    TCMS_SIG_SCHEME_ECSCHNORR ecschnorr;
#endif  // ALG_ECSCHNORR
#if ALG_RSAES
    TCMS_ENC_SCHEME_RSAES rsaes;
#endif  // ALG_RSAES
#if ALG_OAEP
    TCMS_ENC_SCHEME_OAEP oaep;
#endif  // ALG_OAEP
    TCMS_SCHEME_HASH anySig;
} TCMU_ASYM_SCHEME;

/*Table 2:160 - Definition of TCMT_ASYM_SCHEME Structure  */
typedef struct {
    TCMI_ALG_ASYM_SCHEME scheme;
    TCMU_ASYM_SCHEME details;
} TCMT_ASYM_SCHEME;

/*Table 2:161 - Definition of TCMI_ALG_RSA_SCHEME Type  */
typedef TCM_ALG_ID TCMI_ALG_RSA_SCHEME;

/*Table 2:162 - Definition of TCMT_RSA_SCHEME Structure  */
typedef struct {
    TCMI_ALG_RSA_SCHEME scheme;
    TCMU_ASYM_SCHEME details;
} TCMT_RSA_SCHEME;

/*Table 2:163 - Definition of TCMI_ALG_RSA_DECRYPT Type  */
typedef TCM_ALG_ID TCMI_ALG_RSA_DECRYPT;

/*Table 2:164 - Definition of TCMT_RSA_DECRYPT Structure  */
typedef struct {
    TCMI_ALG_RSA_DECRYPT scheme;
    TCMU_ASYM_SCHEME details;
} TCMT_RSA_DECRYPT;

/*Table 2:165 - Definition of TCM2B_PUBLIC_KEY_RSA Structure  */
typedef union {
    UINT16 size;
    BYTE buffer[MAX_RSA_KEY_BYTES];

} TCM2B_PUBLIC_KEY_RSA;

/*Table 2:166 - Definition of TCMI_RSA_KEY_BITS Type  */
typedef TCM_KEY_BITS TCMI_RSA_KEY_BITS;

/*Table 2:167 - Definition of TCM2B_PRIVATE_KEY_RSA Structure  */
typedef union {
    UINT16 size;
    BYTE buffer[RSA_PRIVATE_SIZE];
} TCM2B_PRIVATE_KEY_RSA;

/*Table 2:168 - Definition of TCM2B_ECC_PARAMETER Structure  */
typedef struct {
    UINT16 size;
    BYTE buffer[MAX_ECC_KEY_BYTES];
} TCM2B_ECC_PARAMETER;

/*Table 2:169 - Definition of TCMS_ECC_POINT Structure  */
typedef struct {
    TCM2B_ECC_PARAMETER x;
    TCM2B_ECC_PARAMETER y;
} TCMS_ECC_POINT;

/*Table 2:170 - Definition of TCM2B_ECC_POINT Structure  */
typedef struct {
    UINT16 size;
    TCMS_ECC_POINT point;
} TCM2B_ECC_POINT;

/*Table 2:171 - Definition of TCMI_ALG_ECC_SCHEME Type  */
typedef TCM_ALG_ID TCMI_ALG_ECC_SCHEME;

/*Table 2:172 - Definition of TCMI_ECC_CURVE Type  */
typedef TCM_ECC_CURVE TCMI_ECC_CURVE;

/*Table 2:173 - Definition of TCMT_ECC_SCHEME Structure  */
typedef struct {
    TCMI_ALG_ECC_SCHEME scheme;
    TCMU_ASYM_SCHEME details;
} TCMT_ECC_SCHEME;

/*Table 2:174 - Definition of TCMS_ALGORITHM_DETAIL_ECC Structure  */
typedef struct {
    TCM_ECC_CURVE curveID;
    UINT16 keySize;
    TCMT_KDF_SCHEME kdf;
    TCMT_ECC_SCHEME sign;
    TCM2B_ECC_PARAMETER p;
    TCM2B_ECC_PARAMETER a;
    TCM2B_ECC_PARAMETER b;
    TCM2B_ECC_PARAMETER gX;
    TCM2B_ECC_PARAMETER gY;
    TCM2B_ECC_PARAMETER n;
    TCM2B_ECC_PARAMETER h;
} TCMS_ALGORITHM_DETAIL_ECC;

/*Table 2:175 - Definition of TCMS_SIGNATURE_RSA Structure  */
typedef struct {
    TCMI_ALG_HASH hash;
    TCM2B_PUBLIC_KEY_RSA sig;
} TCMS_SIGNATURE_RSA;

/*Table 2:176 - Definition of Types for Signature */
typedef TCMS_SIGNATURE_RSA TCMS_SIGNATURE_RSASSA;
typedef TCMS_SIGNATURE_RSA TCMS_SIGNATURE_RSAPSS;

/*Table 2:177 - Definition of TCMS_SIGNATURE_ECC Structure  */
typedef struct {
    TCMI_ALG_HASH hash;
    TCM2B_ECC_PARAMETER signatureR;
    TCM2B_ECC_PARAMETER signatureS;
} TCMS_SIGNATURE_ECC;

/*Table 2:178 - Definition of Types for TCMS_SIGNATURE_ECC */
typedef TCMS_SIGNATURE_ECC TCMS_SIGNATURE_ECDAA;
typedef TCMS_SIGNATURE_ECC TCMS_SIGNATURE_ECDSA;
typedef TCMS_SIGNATURE_ECC TCMS_SIGNATURE_SM2;
typedef TCMS_SIGNATURE_ECC TCMS_SIGNATURE_ECSCHNORR;

/*Table 2:179 - Definition of TCMU_SIGNATURE Union  */
typedef union {
#if ALG_ECC
    TCMS_SIGNATURE_ECDAA ecdaa;
#endif  // ALG_ECC
#if ALG_RSA
    TCMS_SIGNATURE_RSASSA rsassa;
#endif  // ALG_RSA
#if ALG_RSA
    TCMS_SIGNATURE_RSAPSS rsapss;
#endif  // ALG_RSA
#if ALG_ECC
    TCMS_SIGNATURE_ECDSA ecdsa;
#endif  // ALG_ECC
#if ALG_ECC
    TCMS_SIGNATURE_SM2 sm2;
#endif  // ALG_ECC
#if ALG_ECC
    TCMS_SIGNATURE_ECSCHNORR ecschnorr;
#endif  // ALG_ECC
#if ALG_HMAC
    TCMT_HA hmac;
#endif  // ALG_HMAC
    TCMS_SCHEME_HASH any;
} TCMU_SIGNATURE;

/*Table 2:180 - Definition of TCMT_SIGNATURE Structure  */
typedef struct {
    TCMI_ALG_SIG_SCHEME sigAlg;
    TCMU_SIGNATURE signature;
} TCMT_SIGNATURE;

/*Table 2:181 - Definition of TCMU_ENCRYPTED_SECRET Union  */
typedef union {
#if ALG_ECC
    BYTE ecc[sizeof(TCMS_ECC_POINT)];
#endif  // ALG_ECC
#if ALG_RSA
    BYTE rsa[MAX_RSA_KEY_BYTES];
#endif  // ALG_RSA
#if ALG_SYMCIPHER
    BYTE symmetric[sizeof(TCM2B_DIGEST)];
#endif  // ALG_SYMCIPHER
#if ALG_KEYEDHASH
    BYTE keyedHash[sizeof(TCM2B_DIGEST)];
#endif  // ALG_KEYEDHASH
} TCMU_ENCRYPTED_SECRET;

/*Table 2:182 - Definition of TCM2B_ENCRYPTED_SECRET Structure  */
typedef union {
    UINT16 size;
    BYTE secret[sizeof(TCMU_ENCRYPTED_SECRET)];

} TCM2B_ENCRYPTED_SECRET;

/*Table 2:183 - Definition of TCMI_ALG_PUBLIC Type  */
typedef TCM_ALG_ID TCMI_ALG_PUBLIC;

/*Table 2:184 - Definition of TCMU_PUBLIC_ID Union  */
typedef union {
#if ALG_KEYEDHASH
    TCM2B_DIGEST keyedHash;
#endif  // ALG_KEYEDHASH
#if ALG_SYMCIPHER
    TCM2B_DIGEST sym;
#endif  // ALG_SYMCIPHER
#if ALG_RSA
    TCM2B_PUBLIC_KEY_RSA rsa;
#endif  // ALG_RSA
#if ALG_ECC
    TCMS_ECC_POINT ecc;
#endif  // ALG_ECC
    TCMS_DERIVE derive;
} TCMU_PUBLIC_ID;

/*Table 2:185 - Definition of TCMS_KEYEDHASH_PARMS Structure  */
typedef struct {
    TCMT_KEYEDHASH_SCHEME scheme;
} TCMS_KEYEDHASH_PARMS;

/*Table 2:186 - Definition of TCMS_ASYM_PARMS Structure  */
typedef struct {
    TCMT_SYM_DEF_OBJECT symmetric;
    TCMT_ASYM_SCHEME scheme;
} TCMS_ASYM_PARMS;

/*Table 2:187 - Definition of TCMS_RSA_PARMS Structure  */
typedef struct {
    TCMT_SYM_DEF_OBJECT symmetric;
    TCMT_RSA_SCHEME scheme;
    TCMI_RSA_KEY_BITS keyBits;
    UINT32 exponent;
} TCMS_RSA_PARMS;

/*Table 2:188 - Definition of TCMS_ECC_PARMS Structure  */
typedef struct {
    TCMT_SYM_DEF_OBJECT symmetric;
    TCMT_ECC_SCHEME scheme;
    TCMI_ECC_CURVE curveID;
    TCMT_KDF_SCHEME kdf;
} TCMS_ECC_PARMS;

/*Table 2:189 - Definition of TCMU_PUBLIC_PARMS Union  */
typedef union {
#if ALG_KEYEDHASH
    TCMS_KEYEDHASH_PARMS keyedHashDetail;
#endif  // ALG_KEYEDHASH
#if ALG_SYMCIPHER
    TCMS_SYMCIPHER_PARMS symDetail;
#endif  // ALG_SYMCIPHER
#if ALG_RSA
    TCMS_RSA_PARMS rsaDetail;
#endif  // ALG_RSA
#if ALG_ECC
    TCMS_ECC_PARMS eccDetail;
#endif  // ALG_ECC
    TCMS_ASYM_PARMS asymDetail;
} TCMU_PUBLIC_PARMS;

/*Table 2:190 - Definition of TCMT_PUBLIC_PARMS Structure  */
typedef struct {
    TCMI_ALG_PUBLIC type;
    TCMU_PUBLIC_PARMS parameters;
} TCMT_PUBLIC_PARMS;

/*Table 2:191 - Definition of TCMT_PUBLIC Structure  */
typedef struct {
    TCMI_ALG_PUBLIC type;
    TCMI_ALG_HASH nameAlg;
    TCMA_OBJECT objectAttributes;
    TCM2B_DIGEST authPolicy;
    TCMU_PUBLIC_PARMS parameters;
    TCMU_PUBLIC_ID unique;
} TCMT_PUBLIC;

/*Table 2:192 - Definition of TCM2B_PUBLIC Structure  */
typedef struct {
    UINT16 size;
    TCMT_PUBLIC publicArea;
} TCM2B_PUBLIC;

/*Table 2:193 - Definition of TCM2B_TEMPLATE Structure  */
typedef union {
    UINT16 size;
    BYTE buffer[sizeof(TCMT_PUBLIC)];
} TCM2B_TEMPLATE;

/*Table 2:194 - Definition of TCM2B_PRIVATE_VENDOR_SPECIFIC Structure  */
typedef union {
    UINT16 size;
    BYTE buffer[PRIVATE_VENDOR_SPECIFIC_BYTES];
} TCM2B_PRIVATE_VENDOR_SPECIFIC;

/*Table 2:195 - Definition of TCMU_SENSITIVE_COMPOSITE Union  */
typedef union {
#if ALG_RSA
    TCM2B_PRIVATE_KEY_RSA rsa;
#endif  // ALG_RSA
#if ALG_ECC
    TCM2B_ECC_PARAMETER ecc;
#endif  // ALG_ECC
#if ALG_KEYEDHASH
    TCM2B_SENSITIVE_DATA bits;
#endif  // ALG_KEYEDHASH
#if ALG_SYMCIPHER
    TCM2B_SYM_KEY sym;
#endif  // ALG_SYMCIPHER
    TCM2B_PRIVATE_VENDOR_SPECIFIC any;
} TCMU_SENSITIVE_COMPOSITE;

/*Table 2:196 - Definition of TCMT_SENSITIVE Structure  */
typedef struct {
    TCMI_ALG_PUBLIC sensitiveType;
    TCM2B_AUTH authValue;
    TCM2B_DIGEST seedValue;
    TCMU_SENSITIVE_COMPOSITE sensitive;
} TCMT_SENSITIVE;

/*Table 2:197 - Definition of TCM2B_SENSITIVE Structure  */
typedef struct {
    UINT16 size;
    TCMT_SENSITIVE sensitiveArea;
} TCM2B_SENSITIVE;

/*Table 2:198 - Definition of _PRIVATE Structure  */
typedef struct {
    TCM2B_DIGEST integrityOuter;
    TCM2B_DIGEST integrityInner;
    TCM2B_SENSITIVE sensitive;
} _PRIVATE;

/*Table 2:199 - Definition of TCM2B_PRIVATE Structure  */
typedef struct {
    UINT16 size;
    BYTE buffer[sizeof(_PRIVATE)];
} TCM2B_PRIVATE;

/*Table 2:203 - Definition of TCMS_ID_OBJECT Structure  */
typedef struct {
    TCM2B_DIGEST integrityHMAC;
    TCM2B_DIGEST encIdentity;
} TCMS_ID_OBJECT;

/*Table 204 - Definition of TCM2B_ID_OBJECT Structure <IN/OUT> */
typedef union {
    UINT16 size;
    BYTE credential[sizeof(TCMS_ID_OBJECT)];
} TCM2B_ID_OBJECT;

#define TYPE_OF_TCM_NV_INDEX UINT32
#define TCM_NV_INDEX_TO_UINT32(a) (*((UINT32 *)&(a)))
#define UINT32_TO_TCM_NV_INDEX(a) (*((TCM_NV_INDEX *)&(a)))
#define TCM_NV_INDEX_TO_BYTE_ARRAY(i, a) \
    UINT32_TO_BYTE_ARRAY((TCM_NV_INDEX_TO_UINT32(i)), (a))
#define BYTE_ARRAY_TO_TCM_NV_INDEX(i, a)    \
    {                                       \
        UINT32 x = BYTE_ARRAY_TO_UINT32(a); \
        i = UINT32_TO_TCM_NV_INDEX(x);      \
    }
#if USE_BIT_FIELD_STRUCTURES
typedef struct TCM_NV_INDEX {  // Table 2:205
    unsigned index : 24;
    unsigned RH_NV : 8;
} TCM_NV_INDEX;
// This is the initializer for a TCM_NV_INDEX structure
#define TCM_NV_INDEX_INITIALIZER(index, rh_nv) {index, rh_nv}
#else  // USE_BIT_FIELD_STRUCTURES
// This implements Table 2:205 TCM_NV_INDEX using bit masking
typedef UINT32 TCM_NV_INDEX;
#define TCM_NV_INDEX_index_SHIFT 0
#define TCM_NV_INDEX_index ((TCM_NV_INDEX)0xffffff << 0)
#define TCM_NV_INDEX_RH_NV_SHIFT 24
#define TCM_NV_INDEX_RH_NV ((TCM_NV_INDEX)0xff << 24)
// This is the initializer for a TCM_NV_INDEX bit array.
#define TCM_NV_INDEX_INITIALIZER(index, rh_nv) \
    (TCM_NV_INDEX)(                            \
        (index << 0) + (rh_nv << 24))
#endif  // USE_BIT_FIELD_STRUCTURES

// Table 2:206 - Definition of TCM_NT Constants
typedef UINT32 TCM_NT;
#define TYPE_OF_TCM_NT UINT32
#define TCM_NT_ORDINARY (TCM_NT)(0x0)
#define TCM_NT_COUNTER (TCM_NT)(0x1)
#define TCM_NT_BITS (TCM_NT)(0x2)
#define TCM_NT_EXTEND (TCM_NT)(0x4)
#define TCM_NT_PIN_FAIL (TCM_NT)(0x8)
#define TCM_NT_PIN_PASS (TCM_NT)(0x9)
// Table 2:207
typedef struct {
    UINT32 pinCount;
    UINT32 pinLimit;
} TCMS_NV_PIN_COUNTER_PARAMETERS;

#define TYPE_OF_TCMA_NV UINT32
#define TCMA_NV_TO_UINT32(a) (*((UINT32 *)&(a)))
#define UINT32_TO_TCMA_NV(a) (*((TCMA_NV *)&(a)))
#define TCMA_NV_TO_BYTE_ARRAY(i, a) \
    UINT32_TO_BYTE_ARRAY((TCMA_NV_TO_UINT32(i)), (a))
#define BYTE_ARRAY_TO_TCMA_NV(i, a)         \
    {                                       \
        UINT32 x = BYTE_ARRAY_TO_UINT32(a); \
        i = UINT32_TO_TCMA_NV(x);           \
    }
#if USE_BIT_FIELD_STRUCTURES
typedef struct TCMA_NV {  // Table 2:208
    unsigned PPWRITE : 1;
    unsigned OWNERWRITE : 1;
    unsigned AUTHWRITE : 1;
    unsigned POLICYWRITE : 1;
    unsigned TCM_NT : 4;
    unsigned Reserved_bits_at_8 : 2;
    unsigned POLICY_DELETE : 1;
    unsigned WRITELOCKED : 1;
    unsigned WRITEALL : 1;
    unsigned WRITEDEFINE : 1;
    unsigned WRITE_STCLEAR : 1;
    unsigned GLOBALLOCK : 1;
    unsigned PPREAD : 1;
    unsigned OWNERREAD : 1;
    unsigned AUTHREAD : 1;
    unsigned POLICYREAD : 1;
    unsigned Reserved_bits_at_20 : 5;
    unsigned NO_DA : 1;
    unsigned ORDERLY : 1;
    unsigned CLEAR_STCLEAR : 1;
    unsigned READLOCKED : 1;
    unsigned WRITTEN : 1;
    unsigned PLATFORMCREATE : 1;
    unsigned READ_STCLEAR : 1;
} TCMA_NV;
// This is the initializer for a TCMA_NV structure
#define TCMA_NV_INITIALIZER(                                                                                                                                                                                                                                                             \
    ppwrite, ownerwrite, authwrite, policywrite, tcm_nt, bits_at_8, policy_delete, writelocked, writeall, writedefine, write_stclear, globallock, ppread, ownerread, authread, policyread, bits_at_20, no_da, orderly, clear_stclear, readlocked, written, platformcreate, read_stclear) \
    {ppwrite, ownerwrite, authwrite, policywrite, tcm_nt, bits_at_8, policy_delete, writelocked, writeall, writedefine, write_stclear, globallock, ppread, ownerread, authread, policyread, bits_at_20, no_da, orderly, clear_stclear, readlocked, written, platformcreate, read_stclear}
#else  // USE_BIT_FIELD_STRUCTURES
// This implements Table 2:208 TCMA_NV using bit masking
typedef UINT32 TCMA_NV;
#define TYPE_OF_TCMA_NV UINT32
#define TCMA_NV_PPWRITE ((TCMA_NV)1 << 0)
#define TCMA_NV_OWNERWRITE ((TCMA_NV)1 << 1)
#define TCMA_NV_AUTHWRITE ((TCMA_NV)1 << 2)
#define TCMA_NV_POLICYWRITE ((TCMA_NV)1 << 3)
#define TCMA_NV_TCM_NT_SHIFT 4
#define TCMA_NV_TCM_NT ((TCMA_NV)0xf << 4)
#define TCMA_NV_POLICY_DELETE ((TCMA_NV)1 << 10)
#define TCMA_NV_WRITELOCKED ((TCMA_NV)1 << 11)
#define TCMA_NV_WRITEALL ((TCMA_NV)1 << 12)
#define TCMA_NV_WRITEDEFINE ((TCMA_NV)1 << 13)
#define TCMA_NV_WRITE_STCLEAR ((TCMA_NV)1 << 14)
#define TCMA_NV_GLOBALLOCK ((TCMA_NV)1 << 15)
#define TCMA_NV_PPREAD ((TCMA_NV)1 << 16)
#define TCMA_NV_OWNERREAD ((TCMA_NV)1 << 17)
#define TCMA_NV_AUTHREAD ((TCMA_NV)1 << 18)
#define TCMA_NV_POLICYREAD ((TCMA_NV)1 << 19)
#define TCMA_NV_NO_DA ((TCMA_NV)1 << 25)
#define TCMA_NV_ORDERLY ((TCMA_NV)1 << 26)
#define TCMA_NV_CLEAR_STCLEAR ((TCMA_NV)1 << 27)
#define TCMA_NV_READLOCKED ((TCMA_NV)1 << 28)
#define TCMA_NV_WRITTEN ((TCMA_NV)1 << 29)
#define TCMA_NV_PLATFORMCREATE ((TCMA_NV)1 << 30)
#define TCMA_NV_READ_STCLEAR ((TCMA_NV)1 << 31)
#define TCMA_NV_RESERVED (0x00000300 | 0x01f00000)
// This is the initializer for a TCMA_NV bit array.
#define TCMA_NV_INITIALIZER(                                                                                                                                                                                                                                                             \
    ppwrite, ownerwrite, authwrite, policywrite, tcm_nt, bits_at_8, policy_delete, writelocked, writeall, writedefine, write_stclear, globallock, ppread, ownerread, authread, policyread, bits_at_20, no_da, orderly, clear_stclear, readlocked, written, platformcreate, read_stclear) \
    (TCMA_NV)(                                                                                                                                                                                                                                                                           \
        (ppwrite << 0) + (ownerwrite << 1) +                                                                                                                                                                                                                                             \
        (authwrite << 2) + (policywrite << 3) +                                                                                                                                                                                                                                          \
        (tcm_nt << 4) + (policy_delete << 10) +                                                                                                                                                                                                                                          \
        (writelocked << 11) + (writeall << 12) +                                                                                                                                                                                                                                         \
        (writedefine << 13) + (write_stclear << 14) +                                                                                                                                                                                                                                    \
        (globallock << 15) + (ppread << 16) +                                                                                                                                                                                                                                            \
        (ownerread << 17) + (authread << 18) +                                                                                                                                                                                                                                           \
        (policyread << 19) + (no_da << 25) +                                                                                                                                                                                                                                             \
        (orderly << 26) + (clear_stclear << 27) +                                                                                                                                                                                                                                        \
        (readlocked << 28) + (written << 29) +                                                                                                                                                                                                                                           \
        (platformcreate << 30) + (read_stclear << 31))
#endif  // USE_BIT_FIELD_STRUCTURES


/*Table 2:209 - Definition of TCMS_NV_PUBLIC Structure  */
typedef struct {
    TCMI_RH_NV_INDEX nvIndex;
    TCMI_ALG_HASH nameAlg;
    TCMA_NV attributes;
    TCM2B_DIGEST authPolicy;
    UINT16 dataSize;
} TCMS_NV_PUBLIC;

/*Table 2:207 - Definition of TCM2B_NV_PUBLIC Structure  */
typedef struct {
    UINT16 size;
    TCMS_NV_PUBLIC nvPublic;
} TCM2B_NV_PUBLIC;

/*Table 2:208 - Definition of TCM2B_CONTEXT_SENSITIVE Structure  */
typedef union {
    UINT16 size;
    BYTE buffer[MAX_CONTEXT_SIZE];

} TCM2B_CONTEXT_SENSITIVE;

/*Table 2:209 - Definition of TCMS_CONTEXT_DATA Structure  */
typedef struct {
    TCM2B_DIGEST integrity;
    TCM2B_CONTEXT_SENSITIVE encrypted;
} TCMS_CONTEXT_DATA;

/*Table 2:210 - Definition of TCM2B_CONTEXT_DATA Structure  */
typedef union {
    UINT16 size;
    BYTE buffer[sizeof(TCMS_CONTEXT_DATA)];

} TCM2B_CONTEXT_DATA;

/*Table 2:211 - Definition of TCMS_CONTEXT Structure  */
typedef struct {
    UINT64 sequence;
    TCMI_DH_SAVED savedHandle;
    TCMI_RH_HIERARCHY hierarchy;
    TCM2B_CONTEXT_DATA contextBlob;
} TCMS_CONTEXT;

/*Table 2:213 - Definition of TCMS_CREATION_DATA Structure  */
typedef struct {
    TCML_PCR_SELECTION pcrSelect;
    TCM2B_DIGEST pcrDigest;
    TCMA_LOCALITY locality;
    TCM_ALG_ID parentNameAlg;
    TCM2B_NAME parentName;
    TCM2B_NAME parentQualifiedName;
    TCM2B_DATA outsideInfo;
} TCMS_CREATION_DATA;

/*Table 2:214 - Definition of TCM2B_CREATION_DATA Structure  */
typedef struct {
    UINT16 size;
    TCMS_CREATION_DATA creationData;
} TCM2B_CREATION_DATA; 
/*Structure */

// Table 2:220 - Definition of TCM_AT Constants
typedef UINT32 TCM_AT;
#define TYPE_OF_TCM_AT UINT32
#define TCM_AT_ANY (TCM_AT)(0x00000000)
#define TCM_AT_ERROR (TCM_AT)(0x00000001)
#define TCM_AT_PV1 (TCM_AT)(0x00000002)
#define TCM_AT_VEND (TCM_AT)(0x80000000)

// Table 2:221 - Definition of TCM_AE Constants
typedef UINT32 TCM_AE;
#define TYPE_OF_TCM_AE UINT32
#define TCM_AE_NONE (TCM_AE)(0x00000000)

typedef struct {  // Table 2:222
    TCM_AT tag;
    UINT32 data;
} TCMS_AC_OUTPUT;


/*Table 2:218 - Definition of TCML_AC_CAPABILITIES Structure  */
typedef struct {
    UINT32 count;
    TCMS_AC_OUTPUT acCapabilities[MAX_AC_CAPABILITIES];
} TCML_AC_CAPABILITIES;

#endif
