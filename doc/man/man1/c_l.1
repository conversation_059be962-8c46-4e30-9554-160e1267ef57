.TH c_l 1 "generated by PolyglotMan" UCB
.\"  manual page source format generated by PolyglotMan v3.0.5,
.\"  available via anonymous ftp from ftp.cs.berkeley.edu:/ucb/people/phelps/tcltk/rman.tar.Z

.br

.br

.br
.SH The l command disassembles instructions from memory.

.LP

.br
.SH Format

.LP

.br
.SH The format for the l command is:

.LP

.br

.br
l [\-bctT] [adr [cnt]]
.br

.br

.br
where:
.br

.br

.br
\-b      lists only branches.
.br

.br
\-c      lists only calls.
.br

.br
\-t      lists the trace buffer.
.br

.br
\-T      assume TinyRISC (MIPS16) instructions.

.br
adr     is the base address from which to disassemble instructions.

.br
cnt     is the number of lines to disassemble.

.br

.br
.SH When invoking this command with no options, disassembly starts at the 

.LP
address in the EPC register and is output to the more command.

.br

.br
.SH Functional Description

.LP

.br
.SH The l command disassembles the memory contents, starting either at the

.SH EPC register's current value or at the specified address. The output of

.LP
this command is passed to the more command, letting the user view one
screenful of disassembled output at a time. Optionally, the user can
specify a count value, which limits the number of disassembled lines to
that number. 
.br

.br
.SH The regstyle Variable 

.LP

.br
.SH The regstyle environment variable determines whether the Monitor displays hardware or software register

.LP
names. Hardware register names are simply $0 through $31. Software
registers are defined by the MIPS software conventions. Set regstyle to
\*(rqhw\*(lq for hardware register names. Set regstyle to \*(rqsw\*(lq for software
register names. 
.br

.br
.SH Examples illustrating the use of the l command follow.

.LP

.br

.br
.SH PMON> set regstyle sw                   Normally the default. 

.LP

.br
.SH PMON> l 9fc00240

.SH Pmon+0x240 3c09a07f lui         t1,0xa07f 

.SH Pmon+0x244 3c08003c lui         t0,0x3c

.SH Pmon+0x248 3529ff20 ori         t1,t1,0xff20 

.SH Pmon+0x24c 3508c62f ori         t0,t0,0xc62f 

.SH Pmon+0x250 ad280000 sw          t0,0(t1) 

.SH Pmon+0x254 3c09a07f lui         t1,0xa07f 

.SH Pmon+0x258 3529ff10 ori         t1,t1,0xff10 

.SH Pmon+0x25c 24080025 addiu       t0,zero,0x25 

.SH Pmon+0x260 ad280000 sw          t0,0(t1) 

.SH Pmon+0x264 3c020040 lui         v0,0x40 

.LP

.br
.SH PMON> set regstyle hw 

.SH PMON> l 9fc00264 B

.SH Pmon+0x264 3c020040 lui         $2,0x40 

.SH Pmon+0x268 40826000 mtc0        $2,$12 

.SH Pmon+0x26c 40806800 mtc0        $0,$13 

.SH Pmon+0x270 3c1d8001 lui         $29,0x8001

.SH Pmon+0x274 27bd8b40 addiu       $29,$29,0x8b40 

.SH Pmon+0x278 3c01a000 lui         $1,0xa000 

.SH Pmon+0x27c 03a1e825 or          $29,$29,$1

.SH Pmon+0x280 0ff005bc jal         flush_cache 

.SH Pmon+0x284 24040000 addiu       $4,$0,0x0 

.SH Pmon+0x288 0ff005bc jal         flush_cache

.SH Pmon+0x28c 24040001 addiu       $4,$0,0x1

.LP

.br

.br
.SH Files

.SH The l command is located in mon/dis.c.

.LP

.br
.SH See Also

.LP
d command,
.br
m command,
.br
dump command,
.br
more and 
.br
rdsrec commands.
.br

.br

.br
.SH Navigation: 

.SH Document Home | 

.SH Document Contents | 

.SH Document Index 

.LP

.br

