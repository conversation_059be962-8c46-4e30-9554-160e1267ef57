<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html>
<head>
   <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
   <meta name="GENERATOR" content="Mozilla/4.72 [en] (WinNT; U) [Netscape]">
   <title>Board Support Packages</title>
</head>
<body>

<center>
<h1>
Board Support Packages</h1></center>
<!--INDEX "Board Support Packages" "Multiply/divide emulation" --> 
<hr>
<p>This package provides several example Board Support Packages (BSPs).
<br>
  Each BSP consists of a Source Code Tree with Makefile(s) suitable for building 
  with any BSD style Unix system (we recommend <a href="http://openbsd.groupbsd.org">OpenBSD</a> 
  for the <a href="http://www.sbs.com">SBS PowerPC</a> series, but Linux could 
  be used.) 
<p>The source code tree includes a standard 'C' library for the PMON/2000
run-time environment. This library may be used to create completely stand-alone
code. The prefered method, however, is to link your code into the PMON/2000
environment and run it as a built-in command.
<p>More complex run time environments can be crafted. Please contact <PERSON>, <a href="http://www.opsycon.se">Opsycon, AB</a>, Sweden, to
explore these options.
<br>
<hr>
<p><b>Navigation:</b> <a href="pmon.htm">Document Home</a> | <a href="doctoc.htm">Document 
  Contents</a> | <a href="docindex.htm">Document Index</a> 
<p><!--$Id: bsps.htm,v 1.1.1.1 2006/09/14 01:59:06 root Exp $ -->
</body>
</html>
