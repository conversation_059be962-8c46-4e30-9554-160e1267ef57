		<title>RS232 Cabling Requirements</title>

		<h1 align="center">RS232 Cabling Requirements</h1>



<!--INDEX "rs232 cabling" "rs232 pinout" -->



Some hosts require the hardware flow control signals to be connected

before they will send and receive data via the RS232 ports. This may be

true even if you have explicitly disabled the hardware flow control.
<p> Most SBS Communications Products PowerPC Single Board Computers require only 
  pins 2, 3, and 5 of an RS-232 port to be connected.
<p>&nbsp;
<p>



<center>
</center>



<p><hr>
<b>Navigation:</b> <a href="pmon.htm">Document Home</a> | <a href="doctoc.htm">Document 
Contents</a> | <a href="docindex.htm">Document Index</a> 
<p><!--$Id: rs232.htm,v ******* 2006/09/14 01:59:06 root Exp $ --> 
