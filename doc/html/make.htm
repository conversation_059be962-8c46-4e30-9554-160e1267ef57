		<title>The Make Utility</title>
		<h1 align=center>The Make Utility</h1>

<!--INDEX "Make utility" --> 
<p>Make is an essential tool for managing large projects. It saves time by only 
  compiling those modules that have been changed since the last build. The PMON/2000 
  project is native to the OpenBSD, and as such, uses the Berkely 'make' utility. 
  This flavor of 'make' is rich with features that support parallelism and includes. 
  GNU make, although very popular, is excessive in size and not nearly as flexible. 
</p>
<p>To learn more about 'make' we recommend either of these books. </p>
<p>

<ul>
<li>
"Managing Projects with Make", by <PERSON> and <PERSON>, published
by O'Reilly & Associates, Inc. ISBN 0-937175-90-0<p>

<li>
"Mastering Make", by <PERSON><PERSON><PERSON> et al., published by Prentice Hall, ISBN 0-13-554619-2.<p>
</ul>

<b>Navigation:</b> <a href="index.html">Document Home</a> | <a href="doctoc.htm">Document 
Contents</a> | <a href="docindex.htm">Document Index</a> 
<p>
<br>
<!--$Id: make.htm,v 1.1.1.1 2006/09/14 01:59:06 root Exp $ --> 