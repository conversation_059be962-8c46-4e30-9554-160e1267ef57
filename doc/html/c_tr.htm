<title>The tr Command</title>

<h1>tr</h1>



<!--INDEX "tr command" "transparent mode" -->



The tr command selects transparent mode.<p>



<h2>Format</h2>
<p> The format for this command is:</p>
<blockquote>
  <p> <font size="+1">tr [-2] [port] </font></p>
</blockquote>
<p>where:</p>
<table width="95%" border="0" cellspacing="1" cellpadding="1" height="41">
  <tr bgcolor="#CCCCCC"> 
    <td width="14%" align="right" valign="top">-2&nbsp;&nbsp; </td>
    <td width="86%" align="left" valign="top">Selects 2-port mode.</td>
  </tr>
  <tr>
    <td width="14%" align="right" valign="top">port &nbsp;&nbsp;</td>
    <td width="86%" align="left" valign="top">Selects a specific port </td>
  </tr>
</table>
<p>&nbsp;</p>
<h2>Functional Description</h2>
<dl> 
  <dd> The tr command selects transparent mode. 
  <dd>&nbsp;</dd>
  <dt>In transparent mode, the <a href="mondef.htm">Monitor</a> copies any characters 
    typed on the keyboard to the selected port and then copies characters arriving 
    at the selected port to the screen. This command can be used for two different 
    functions.</dt>
  <dd>&nbsp;</dd>
  <dd> 
    <p> 
    <ul>
      <li> To allow the user run the <a href="mondef.htm">Monitor</a> on the same 
        serial port that is used as a login line to your host. Do this by connnecting 
        tty1 to your host system, and tty0 to your terminal. Typing 'tr' on your 
        terminal will connect you with your host. 
        <p> 
      <li> To allow the user to 'rlogin' to another board via a serial port or 
        pseudo tty port. This mode is used on the ATMizer platform in order to 
        'talk' to the SAR board. For example, 
        <p> 
    </ul>
</dl>
<blockquote> 
  <pre>

	PMON&gt; tr tty2			connect to port tty2

	SAR&gt; r				display registers on SAR board

	^K				return to main board

	PMON&gt; r				display registers on main board

</pre>
</blockquote>
<p>tty2 is the name of the pseudo tty port (located in shared memory) that is 
  used to communicate with the SAR board. </p>
<p> Note that if you are using 2-port download mode. You will need to add the 
  option '-2' to indicate that you need 2-port transparent mode. For example,</p>
<blockquote>
  <blockquote> 
    <pre> PMON&gt; tr -2 tty2</pre>
  </blockquote>
</blockquote>
<p>In this case the <a href="mondef.htm">Monitor</a> not only copies characters 
  typed on the keyboard to tty2 and characters arriving on tty2 to the screen. 
  It also copies characters arriving on tty1 to tty3, and characters arriving 
  on tty3 to tty1. </p>
<p>The <b>trabort</b> Variable</p>
<blockquote> 
  <p> The environment variable trabort selects the character that terminates the 
    transparent mode and returns the <a href="mondef.htm">Monitor</a> to the default 
    command mode.</p>
</blockquote>
<p> The <b>hostport</b> Variable</p>
<blockquote> 
  <p> The environment variable hostport determines the default port for the tr 
    command.</p>
</blockquote>
<blockquote> 
  <p>&nbsp; </p>
</blockquote>
<h2>See Also</h2><dl><dd>

<a href="c_set.htm">set</a>

command for the setup of the environment variables.<p>





</dl><p><hr>
<p><b>Navigation:</b> <a href="pmon.htm">Document Home</a> | <a href="doctoc.htm">Document 
  Contents</a> | <a href="docindex.htm">Document Index</a></p>
<p><!--$Id: c_tr.htm,v ******* 2006/09/14 01:59:06 root Exp $ --></p>
