<html>

<head>
<title>The l Command</title>
</head>

<body>

<h1>l</h1>
<!--INDEX "l command" disassemble "list memory"  PowerPC p604 p604e p603 PowerPC740 PowerPC750-->

<p>The l command disassembles instructions from memory.</p>

<h2>Format</h2>

<dl>
  <dd>The format for the l command is:
    <pre>

<font size="+1">l [-bct] [adr [cnt]]
</font>
</pre>
    <p>where:</p>
    <table width="95%">
      <tr bgcolor="#CCCCCC"> 
        <td width="72" align="right" valign="top">-b&nbsp; </td>
        <td width="730" align="left" valign="top">&nbsp;lists only branches. </td>
      </tr>
      <tr> 
        <td width="72" align="right" valign="top">-c &nbsp;</td>
        <td width="730" align="left" valign="top">&nbsp;lists only calls. </td>
      </tr>
      <tr bgcolor="#CCCCCC"> 
        <td width="72" align="right" valign="top">-t&nbsp; </td>
        <td width="730" align="left" valign="top">&nbsp;lists the trace buffer. 
        </td>
      </tr>
      <tr> 
        <td width="72" align="right" valign="top">adr &nbsp;</td>
        <td width="730" align="left" valign="top">&nbsp;is the base address from 
          which to disassemble instructions. </td>
      </tr>
      <tr bgcolor="#CCCCCC"> 
        <td width="72" align="right" valign="top">cnt &nbsp;</td>
        <td width="730" align="left" valign="top">&nbsp;is the number of lines 
          to disassemble. </td>
      </tr>
    </table>
    <p>When invoking this command with no options, disassembly starts at the address 
      in the <em>PC</em> register and is output to the more command. </p>
  </dd>
</dl>

<h2>Functional Description</h2>

<dl> 
  <dd>The l command disassembles the memory contents, starting either at the <em>PC</em> 
    register's current value or at the specified address. The output of this command 
    is passed to the more command, letting the user view one screenful of disassembled 
    output at a time. Optionally, the user can specify a count value, which limits 
    the number of disassembled lines to that number. </dd>
</dl>
<p>The regstyle Variable </p>
<p><font color="#FF0000">The regstyle environment variable is an architecture 
  dependent variable which determines whether the <a href="mondef.htm">Monitor</a> 
  displays hardware or software register names. Hardware register names are simply 
  r00 throughr31. Software registers are defined by the PowerPC software conventions</font></p>
<p><font color="#FF0000">Examples illustrating the use of the l command follow. 
  <br>
  This example are for the MIPS architecture but looks similar on other architectures,</font></p>
<blockquote> 
  <pre><font color="#FF0000">
<font color="#666666">PMON&gt; l 9fc00240

Pmon+0x240 3c09a07f lui 	t1,0xa07f 
Pmon+0x244 3c08003c lui 	t0,0x3c
Pmon+0x248 3529ff20 ori 	t1,t1,0xff20 
Pmon+0x24c 3508c62f ori 	t0,t0,0xc62f 
Pmon+0x250 ad280000 sw 	t0,0(t1) 
Pmon+0x254 3c09a07f lui 	t1,0xa07f 
Pmon+0x258 3529ff10 ori 	t1,t1,0xff10 
Pmon+0x25c 24080025 addiu 	t0,zero,0x25 
Pmon+0x260 ad280000 sw 	t0,0(t1) 
Pmon+0x264 3c020040 lui 	v0,0x40 


PMON&gt; set regstyle hw 

PMON&gt; l 9fc00264 B

Pmon+0x264 3c020040 lui 	$2,0x40 
Pmon+0x268 40826000 mtc0 	$2,$12 
Pmon+0x26c 40806800 mtc0 	$0,$13 
Pmon+0x270 3c1d8001 lui 	$29,0x8001
Pmon+0x274 27bd8b40 addiu 	$29,$29,0x8b40 
Pmon+0x278 3c01a000 lui 	$1,0xa000 
Pmon+0x27c 03a1e825 or 	$29,$29,$1
Pmon+0x280 0ff005bc jal 	flush_cache 
Pmon+0x284 24040000 addiu 	$4,$0,0x0 
Pmon+0x288 0ff005bc jal 	flush_cache
Pmon+0x28c 24040001 addiu 	$4,$0,0x1</font>

</font></pre>
</blockquote>
<h2>See Also</h2>

<dl>
  <dd><a href="c_d.htm">d command</a>, <a href="c_m.htm">m command</a>, <a href="c_dump.htm">dump
    command</a> and <a href="c_more.htm">more</a> commands. </dd>
</dl>

<hr>
<p> <b>Navigation:</b> <a href="pmon.htm">Document Home</a> | <a href="doctoc.htm">Document 
  Contents</a> | <a href="docindex.htm">Document Index</a> </p>
<p><!--$Id: c_l.htm,v 1.1.1.1 2006/09/14 01:59:06 root Exp $ --></p>
</body>
</html>
