<title>PMON Limitations</title>

<h1 align="center">PMON Limitations</h1>

<!--INDEX "PMON limitations" -->

<!--INDEX "k0 register" "k1 register" "Breakpoints in exception handlers" -->



When the target system is halted at a breakpoint, it's possible to save

the contents of most general purpose registers. However, in this scheme,

the contents of two general purpose registers must be

sacrificed; two registers are required for use by the PMON code

that extracts the contents of the other general purpose registers. The

registers chosen are k0 and k1 because those are the only two registers

that are guaranteed (by convention) to not contain useful values when

interrupts are enabled.  Users should

keep in mind that the contents of these registers at the time of a

breakpoint are therefore not available. <p>



It should also be noted that PM<PERSON> makes use of the CPU's

exception handling mechanism whenever a breakpoint occurs. Therefore,

care must be taken in debugging exception handlers, to ensure that any

necessary machine state is saved before a debug exception may occur. In

particular, the EPC (Exception Program Counter), and SR (Status

Register) must be saved, along with any other registers which may be

used by the handler.<p>



In practice, this means that breakpoints or single-stepping may safely

be used within the body of an exception handler, provided it is written

in such a way that all necessary state is saved as its initial action

on entry. However neither of these features may be used during the

first few instructions of the handler; these must be allowed to

complete the task of saving essential machine state to the stack. <p>



<p><hr>

<p><b>Navigation:</b> <a href="pmon.htm">Document Home</a> | <a href="doctoc.htm">Document 
  Contents</a> | <a href="docindex.htm">Document Index</a></p>
<p><!--$Id: pmonlim.htm,v 1.1.1.1 2006/09/14 01:59:06 root Exp $ --> </p>
<p>



