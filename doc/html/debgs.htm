<html>

<head>
<title>Debuggers</title>
</head>

<body>

<h1 align="center">Debuggers</h1>
<!--INDEX debuggers -->

<p>The debugger is used to provide a user interface to the debug <a href="mondef.htm">Monitor</a>.
You can use either a <a href="terms.htm">terminal</a> emulator, or a source-level debugger
to perform this function.</p>

<p>A source-level debugger permits you to perform your entire debugging session from a
high-level language point of view. You can display and set variables by name even if they
are local (on the stack), you can also single step one high-level language statement at a
time. Breakpoints are specified in terms of source line numbers or function names.</p>

<hr>
<p> <b>Navigation:</b> <a href="pmon.htm">Document Home</a> | <a href="doctoc.htm">Document 
  Contents</a> | <a href="docindex.htm">Document Index</a> <br>
</p>
<p><!--$Id: debgs.htm,v 1.1.1.1 2006/09/14 01:59:06 root Exp $ --> </p>
</body>
</html>
