<html>

<head>
<title>PMON 2000</title>
</head>

<body>

<h1 align="center">PMON/ 2000</h1>
<!--INDEX "Choosing and Setting Baud Rates" --> 
<hr>
<p>PMON/2000 is derived work from <a href="http://www.carmel.com/pmon">LSI Logics</a> 
  PMON4 and additional work done by <a href="http://www.algor.co.uk">Algorithmics 
  UK</a>. </p>
<p> PMON/2000 was created by <a href="http://www.opsycon.se">Opsycon Open System 
  consulting AB, Sweden</a>, on contract to <i>RTMX, Inc.</i>, to make it cross 
  platform portable. </p>
<p>The first non Mips platform supported is PowerPC, funded by <i>RTMX, Inc</i>. 
  <br>
  Funding for the Denali, K2 and Palomar Single Board Computers came from <a href="http://www.sbs.com">SBS 
  Communications</a>.</p>

<p>As the base for PMON/2000 the work done by Algorithmics was used but the kernel 
  core was replaced by newer code from <a href="http://www.openbsd.org">OpenBSD 
  2.6</a> to get a more up to date code base and additionally future support for 
  IPV6.</p>

<p>Many commands have been modified and extended and new has been added. One mainline 
  functionality added is the ability to boot from SCSI devices.</p>

<p>Credit goes to Algorithmics UK and LSI logic for making the PMON code freely 
  available. Much of the code used is covered by the BSD Copyright and thus the 
  PMON/2000 code is NOT 100% FREE but requires that credit is given to the authors. 
  <br>
  <br>
  For more information about the copyright see the source code. </p>
<p><br>
  The source code is available via anonymous CVS from: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<EMAIL>:/cvs</p>
<hr>
<p> <b>Navigation:</b> <a href="pmon.htm">Document Home</a> | <a href="doctoc.htm">Document 
  Contents</a> | <a href="docindex.htm">Document Index</a> <br>
  <!--$Id: mondef.htm,v ******* 2006/09/14 01:59:06 root Exp $ --> </p>
</body>
</html>
