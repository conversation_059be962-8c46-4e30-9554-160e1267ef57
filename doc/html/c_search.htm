<html>

<head>
<title>The search Command</title>
</head>

<body>

<h1>search</h1>
<!--INDEX "search command" "search memory" -->

<p>The search command executes a search for a memory pattern.</p>

<h2>Format</h2>

<p>The format for the search command is:</p>
<blockquote>
  <pre> <font size="+1">search from to {val|-s str}-</font></pre>
</blockquote>
<p>where:</p>
<table width="95%">
  <tr bgcolor="#CCCCCC"> 
    <td width="101" nowrap align="right" valign="top" bordercolor="#CCCCCC">from&nbsp;&nbsp; 
    </td>
    <td width="730" align="left" valign="top" bordercolor="#CCCCCC">is the start 
      address for the search operation. </td>
  </tr>
  <tr> 
    <td width="101" nowrap align="right" valign="top">&nbsp;&nbsp;to&nbsp;&nbsp; 
    </td>
    <td width="730" align="left" valign="top">is the end address for the search 
      operation. </td>
  </tr>
  <tr bgcolor="#CCCCCC"> 
    <td width="101" nowrap align="right" valign="top">val &nbsp;&nbsp;</td>
    <td width="730" align="left" valign="top">is the hexadecimal value that is 
      the object of the search. </td>
  </tr>
  <tr> 
    <td width="101" nowrap align="right" valign="top">-s str &nbsp;&nbsp;</td>
    <td width="730" align="left" valign="top">specifies that the search operation 
      is for a string str. </td>
  </tr>
</table>
<h2>Functional Description</h2>
<dl> 
  <dd>The search command searches memory for a pattern. The pattern may be a single 
    byte, multiple bytes, or an ASCII string.</dd>
</dl>
<p>If the -s option is specified, the next parameter is interpreted as an ASCII 
  string. To search for a multiple-word string, enclose the string in double quotation 
  marks.</p>
<p>The output of this command is printed to the screen via the more command.</p>
<p>The following example searches for 0x3c and 0xd4 from 0xa0020000 to 0xa0030000:</p>
<blockquote> 
  <pre>

PMON&gt; search a0020000 a0030000 3c d4

</pre>
</blockquote>
<p>The following example searches for &quot;ABC&quot; from 0xa0020000 to 0xa0030000:</p>
<blockquote>
  <pre>

PMON&gt; search a0020000 a0030000 -s &quot;ABC&quot; 

</pre>
</blockquote>
<h2>See Also</h2>

<dl>
  <dd><a href="c_d.htm">d command</a> and <a href="c_more.htm">more</a> command.</dd>
</dl>

<hr>
<p> <b>Navigation:</b> <a href="pmon.htm">Document Home</a> | <a href="doctoc.htm">Document 
  Contents</a> | <a href="docindex.htm">Document Index</a> </p>
<p><!--$Id: c_search.htm,v ******* 2006/09/14 01:59:06 root Exp $ --></p>
</body>
</html>
