<html>

<head>
<title>Getting Started with PMON 2000</title>
</head>

<body>

<h1 align="center">Getting Started with PMON 2000</h1>

<hr>
<!--INDEX "Getting Started with PMON" -->
<!--INDEX "PMON Web Site" -->
<div align="center"><center>

<table>
  <tr>
    <th>What You'll Need</th>
  </tr>
  <tr>
    <td><ul>
      <li>A Host computer system (UNIX or <a href="dosdef.htm">MSDOS</a>) </li>
      <li>A <a href="pmtargs.htm">Target</a> board </li>
    </ul>
    </td>
  </tr>
</table>
<p>&nbsp;</p></center></div>

<p>To run an application program on your Target using PMON, perform the following steps: 

<ol>
  <li>Create a working directory at the top level of you PMON source tree, i.e., 
    src/&lt;top_level&gt;/....<br>
    and install your application source code module(s). Be sure to conform to 
    strict ANSI 'C' prototyping rules and setup a Makefile that is fully comaptible 
    with the PMON/2000 Project 's &quot;.mk&quot; include files. There is a demo 
    application in the source tree for your examination.&nbsp;<br>
  </li>
  <li>Compile and link your application to the PMON/2000 standard 'C' library. 
    This library is resident in the source tree if you have built PMON/2000 at 
    least once, otherwise you must build a copy of PMON/2000 for downloading.&nbsp;</li>
  <li><a href="dlexe.htm">Download and Execute</a> your application on the Target. 
  </li>
</ol>

<hr>
<p>

<b>Navigation:</b> <a href="pmon.htm">Document Home</a> | <a href="doctoc.htm">Document
Contents</a> | <a href="docindex.htm">Document Index</a> </p>
</body>
</html>
