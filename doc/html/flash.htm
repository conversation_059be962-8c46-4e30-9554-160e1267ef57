<html>

<head>
<title>Flash Memory Types</title>
</head>

<body>
<h1 align="left">Flash Memory Types </h1>
<!--INDEX "Flash Memory Types" 29F010 29F040 "Sector erase" -->

<hr>

<p>The flash memory must support sector erase if it is to be used to hold configuration 
  settings. 
<p>PMON/2000 is currently aware of the following devices that support sector, 
  block or whole device erase.
<p>&nbsp;
<table width="583" border="1">
  <tr> 
    <td width="19%" rowspan="2" bgcolor="#CCCCCC" align="center">AMD</td>
    <td width="54%" height="35" nowrap>Am29F010 - 128KB</td>
    <td width="27%" rowspan="2" bgcolor="#CCCCCC"><img src="hg-amd.gif" width="150" height="64" align="absmiddle"></td>
  </tr>
  <tr> 
    <td width="54%" nowrap>Am29F040 - 512KB</td>
  </tr>
  <tr> 
    <td width="19%" height="121" bgcolor="#CCCCCC" align="center">Atmel</td>
    <td width="54%" height="121" nowrap>AT29F040 - 512KB</td>
    <td width="27%" height="121"><img src="atmel-anim.gif" width="154" height="123"></td>
  </tr>
  <tr> 
    <td width="19%" rowspan="2" bgcolor="#CCCCCC" align="center">INTEL</td>
    <td width="54%" height="49" nowrap>TE28F016SA - OnBoard 8MB</td>
    <td width="27%" rowspan="2"><img src="Intel-flash-homeg2.gif" width="154" height="100"></td>
  </tr>
  <tr> 
    <td width="54%" nowrap>TE28F160S3 - OnBoard 8MB</td>
  </tr>
</table>
<h2>See Also:</h2>
<blockquote>
  <p><a href="c_flash.htm">flash</a> and <a href="boot.htm">boot</a> commands.</p>
</blockquote>
<p>&nbsp;
<hr>
<p> <b>Navigation:</b> <a href="pmon.htm">Document Home</a> | <a href="doctoc.htm">Document 
  Contents</a> | <a href="docindex.htm">Document Index</a></p>
<p><!--$Id: flash.htm,v 1.1.1.1 2006/09/14 01:59:06 root Exp $ --> </p>
</body>
</html>
