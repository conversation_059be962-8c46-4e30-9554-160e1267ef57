<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html>
<head>
   <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
   <meta name="GENERATOR" content="Mozilla/4.72 [en] (WinNT; U) [Netscape]">
   <title>The copy Command</title>
</head>
<body>

<h1>
copy</h1>
<!--INDEX "copy command" -->
<p>The copy command copies a specified number of bytes from one location
in memory to another.
<h2>
Format</h2>

<dl>
<dd>
The format of the copy command is:</dd>

  <pre>
<font size="+1">copy from to size
</font>
</pre>
where:
<br>&nbsp;
  <table width="95%">
    <tr bgcolor="#CCCCCC"> 
      <td WIDTH="75">from&nbsp;</td>

      <td width="518">declares the source address location.&nbsp;</td>
</tr>

<tr>
      <td width="75">to&nbsp;</td>

      <td width="518">declares the target address location.&nbsp;</td>
</tr>

    <tr bgcolor="#CCCCCC"> 
      <td width="75">size&nbsp;</td>

      <td width="518">is the size of the block of memory to be copied. This quantity 
        is specified in bytes.&nbsp;</td>
</tr>
</table>
</dl>

<h2>
Functional Description</h2>

<dl> 
  <dd> The copy command replicates a specified number of bytes from one place 
    in memory to another.</dd>
  <p>If to is less than from, copying is performed in ascending order starting 
    at from. If from is less than to, copying is performed in descending order 
    starting at from + size. 
  <p>When moving a data block down, the source data is copied from the bottom 
    of the block upwards: and when moving a data block up, the source data is 
    copied from the top of the block downwards. By this technique, there is no 
    risk of copying over data in overlapping block move operations; as the data 
    in the overlapping area is copied first.
</dl>

<h2>
Examples</h2>

<dl>
<ul>
<li>
This example shows how to copy a block of memory, 8 Kbytes in size, with
a base address of 0x80020000, to another 8-Kbyte area starting at the address
0x80060000.</li>

<pre>
PMON> copy 80020000 80060000 2000

</pre>
</ul>
</dl>

<h2>
See Also</h2>

<p><br>
<hr>
<p><b>Navigation:</b> <a href="pmon.htm">Document Home</a> | <a href="doctoc.htm">Document 
  Contents</a> | <a href="docindex.htm">Document Index</a> 
<p><!--$Id: c_copy.htm,v ******* 2006/09/14 01:59:06 root Exp $ -->
</body>
</html>
