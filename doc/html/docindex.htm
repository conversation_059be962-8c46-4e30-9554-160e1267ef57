<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html>
<head>
   <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
   <meta name="GENERATOR" content="Mozilla/4.72 [en] (WinNT; U) [Netscape]">
   <title>PMON Commands by Name</title>
</head>
<body>

<center>
  <h1> PMON/2000 Commands by Name</h1>
</center>
<!--INDEX "PMON Commands" --><!--INDEX "PMON Commands" -->
<hr>
<p>PMON/2000 (running on the Target) provides a set of powerful assembly-level 
  debugging commands. 
<p>Click <a href="pmonexmp.htm">here</a> to see an example annotated debug
session.
<br>&nbsp;
<table BORDER CELLSPACING=2 CELLPADDING=4 BGCOLOR="#CCFFFF" >
  <tr ALIGN=CENTER> 
    <td>Command</td>
    <td>Function</td>
    <td ALIGN=CENTER VALIGN=TOP>Execution <br>
      Control</td>
    <td ALIGN=CENTER VALIGN=TOP>Display &amp; <br>
      Modify</td>
    <td>Environment</td>
    <td>Misc</td>
    <td>Diagnostics</td>
  </tr>
  <tr> 
    <td><a href="c_about.htm">about</a></td>
    <td>About PMON/2000</td>
    <td>&nbsp;</td>
    <td></td>
    <td></td>
    <td><img src="Check.gif" height=23 width=19></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_b.htm">b</a></td>
    <td>Breakpoint</td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="boot.htm">boot</a></td>
    <td>Boot command (net or scsi)</td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_bt.htm">bt</a></td>
    <td>Stack backtrace</td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_c.htm">c</a></td>
    <td>Continue</td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_call.htm">call</a></td>
    <td>Call</td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_copy.htm">copy</a></td>
    <td>Copy</td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_d.htm">d</a></td>
    <td>Display</td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_date.htm">date</a></td>
    <td>Display/set date and time</td>
    <td></td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_db.htm">db</a></td>
    <td>Delete breakpoint</td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_debug.htm">debug</a></td>
    <td>Debug</td>
    <td></td>
    <td></td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_dump.htm">dump</a></td>
    <td>Upload via RS232/Ethernet</td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td>eset</td>
    <td>Edit variable</td>
    <td></td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_fill.htm">fill</a></td>
    <td>Fill</td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_flash.htm">flash</a></td>
    <td>Write or Erase a flash device area</td>
    <td></td>
    <td></td>
    <td><img src="Check.gif" height=23 width=19></td>
    <td>&nbsp;</td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_flush.htm">flush</a></td>
    <td>Flush data and/or instruction cache</td>
    <td></td>
    <td></td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_g.htm">g</a></td>
    <td>Go</td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_h.htm">h</a></td>
    <td>Help</td>
    <td></td>
    <td></td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_hi.htm">hi</a></td>
    <td>History</td>
    <td></td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_l.htm">l</a></td>
    <td>List (<a href="c_l.htm">disassemble</a>)</td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_load.htm">load</a></td>
    <td>Download via RS232/Ethernet</td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_ls.htm">ls</a></td>
    <td>List symbols</td>
    <td></td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_m.htm">m</a></td>
    <td><a href="c_m.htm">Memory</a> display/modify</td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_more.htm">more</a></td>
    <td>More</td>
    <td></td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_mt.htm">mt</a></td>
    <td>Memory test</td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
  </tr>
  <tr> 
    <td><a href="netboot.htm">netboot</a></td>
    <td>Network boot load</td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_ping.html">ping</a></td>
    <td>Network setup test</td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
  </tr>
  <tr> 
    <td><a href="c_r.htm">r</a></td>
    <td><a href="c_r.htm">Register</a> display/modify</td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_reboot.htm">reboot</a></td>
    <td>reboot PMON</td>
    <td></td>
    <td></td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_search.htm">search</a></td>
    <td>Search</td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="scsiboot.htm">scsiboot</a></td>
    <td>SCSI Disk boot load</td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_set.htm">set</a></td>
    <td>Set variable</td>
    <td></td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_sh.htm">sh</a></td>
    <td>Command shell</td>
    <td></td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_stty.htm">stty</a></td>
    <td>Display/set terminal options</td>
    <td></td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_sym.htm">sym</a></td>
    <td>Set symbolic name</td>
    <td></td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_t.htm">t</a></td>
    <td><a href="c_t.htm">Trace</a> (single step)</td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_t.htm">to</a></td>
    <td><a href="c_t.htm">Trace</a> (step over)</td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_tr.htm">tr</a></td>
    <td>Transparent mode</td>
    <td></td>
    <td></td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_set.htm">unset</a></td>
    <td>Remove variable</td>
    <td></td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
  </tr>
  <tr> 
    <td><a href="c_vers.htm">version</a></td>
    <td>Display PMON/2000 Version</td>
    <td></td>
    <td></td>
    <td><img SRC="Check.gif" height=23 width=19></td>
    <td></td>
    <td></td>
  </tr>
</table>

<p>
<hr>
<p><b>Navigation:</b> <a href="pmon.htm">Document Home</a> | <a href="doctoc.htm">Document 
  Contents</a> | Document Index <br>
  <!Comment $Id: docindex.htm,v 1.1.1.1 2006/09/14 01:59:06 root Exp $ >
</body>
</html>
