	<title>Sending a File Using a Terminal Emulator</title>

	
<h1 align="left">Sending a File Using a Terminal Emulator</h1>



<!--INDEX "Send file" "Downloading with terminal emulator" -->



You must use Motorola <a href="srec.htm">S-records</a> when using the

&quot;send File&quot; feature of your <a href="terms.htm">Terminal Emulator</a>.

In addition the Monitor's environment variable 'hostport' must be <a href="c_set.htm">set</a> to 'tty0'. In some situations it might be

necessary to use a <a href="flwctl.htm">flow-control</a> protocol in

order to guarantee that your Host does not send the data faster than

the Target is able to accept it.<p>



<p><hr>

<p><b>Navigation:</b> <a href="pmon.htm">Document Home</a> | <a href="doctoc.htm">Document 
  Contents</a> | <a href="docindex.htm">Document Index</a></p>
<p><!--$Id: sendfile.htm,v ******* 2006/09/14 01:59:06 root Exp $ --> </p>
<p>



