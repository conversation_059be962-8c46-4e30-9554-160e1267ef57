<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html>
<head>
   <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
   <meta name="GENERATOR" content="Mozilla/4.72 [en] (WinNT; U) [Netscape]">
   <title>PMON 2000</title>
</head>
<body>

<center>
  <h1 align="center"> PMON/2000 Table of Contents</h1>
</center>
<!--INDEX "PMON Commands" -->
<p>The PMON/2000 Monitor (running on the Target) provides a set of powerful assembly-level 
  debugging commands. Click <a href="pmonexmp.htm">here</a> to see an example 
  annotated debug session. 
<p> 
  <center>
    .
  </center>
  &nbsp; 
<center>
  <table 1 CELLPADDING=5 width="70%" align="left" cellspacing="4" >
    <tr> 
      <td width="155" align="right" valign="top">Downloading<br>
        Files / Images&nbsp;&nbsp;</td>
      <td width="415" bgcolor="#CCCCCC"><a href="c_load.htm">&nbsp;Load File</a>&nbsp;&nbsp;(load), 
        <br>
        <a href="boot.htm">&nbsp;Boot</a>&nbsp;&nbsp;(boot), <br>
        <a href="netboot.htm">&nbsp;Network Boot</a> &nbsp;(netboot), <br>
        <a href="scsiboot.htm">&nbsp;Disk Boot</a> &nbsp;(scsiboot), <br>
        <a href="c_sym.htm">&nbsp;Define a Symbol</a>&nbsp;&nbsp;(sym), <br>
        <a href="c_ls.htm">&nbsp;List Symbols</a>&nbsp;&nbsp;(ls),</td>
    </tr>
    <tr> 
      <td width="155" align="right" valign="top" bgcolor="#CCCCCC">Display/Set 
        Registers,&nbsp;<br>
        &nbsp;&nbsp;&nbsp;&nbsp;-------<br>
        Display/Set Memory&nbsp;&nbsp;</td>
      <td width="415"><a href="c_r.htm">&nbsp;Display/set Registers</a>&nbsp;&nbsp;(r), 
        <br>
        <a href="c_m.htm">&nbsp;Modify Memory</a>&nbsp;&nbsp;(m), <br>
        <a href="c_d.htm">&nbsp;Display Memory</a>&nbsp;&nbsp;(d), <br>
        <a href="c_l.htm">&nbsp;Disassemble Memory</a>&nbsp;&nbsp;(l), <br>
        <a href="c_fill.htm">&nbsp;Fill Memory</a>&nbsp;&nbsp;(fill), <br>
        <a href="c_copy.htm">&nbsp;Copy Memory</a>&nbsp;&nbsp;(copy), <br>
        <a href="c_search.htm">&nbsp;Search Memory&nbsp;&nbsp;</a>(search), <br>
        <a href="c_dump.htm">&nbsp;Dump Memory</a>&nbsp;&nbsp;(dump),&nbsp;</td>
    </tr>
    <tr> 
      <td width="155" align="right" valign="top">Execution Control,&nbsp;<br>
        &nbsp;&nbsp;-------<br>
        Breakpoints&nbsp;&nbsp;</td>
      <td width="415" bgcolor="#CCCCCC"><a href="c_g.htm">&nbsp;Start Execution&nbsp;&nbsp;</a>(g), 
        <br>
        <a href="c_b.htm">&nbsp;Display/Set Breakpoints</a>&nbsp;&nbsp;(b), <br>
        <a href="c_db.htm">&nbsp;Delete Breakpoint</a>&nbsp;&nbsp;(db), <br>
        <a href="c_t.htm">&nbsp;Single Step</a>&nbsp;&nbsp;(t / to), <br>
        <a href="c_bt.htm">&nbsp;Back Trace</a> &nbsp;(bt), <br>
        <a href="c_c.htm">&nbsp;Continue Execution</a>&nbsp;&nbsp;(c), <br>
        <a href="c_call.htm">&nbsp;Execute Subroutine</a>&nbsp;&nbsp;(call),</td>
    </tr>
    <tr> 
      <td width="155" align="right" valign="top" bgcolor="#CCCCCC">Miscellaneous 
        and&nbsp;&nbsp;<br>
        Environment Control&nbsp;&nbsp;</td>
      <td width="415"> 
        <p><a href="c_h.htm">&nbsp;Help</a>&nbsp;&nbsp;(h), <br>
          <a href="c_about.htm"> &nbsp;About PMON</a>&nbsp;&nbsp;(about),<br>
          <a href="c_debug.htm">&nbsp;Enter DBX mode</a>&nbsp;&nbsp;(debug), <br>
          <a href="c_hi.htm">&nbsp;History</a>&nbsp;&nbsp;(hi), <br>
          <a href="c_set.htm">&nbsp;Display/set Enviroment Variable</a>&nbsp;&nbsp;(set 
          / &nbsp;eset), <br>
          <a href="c_stty.htm">&nbsp;Set Terminal Parameters</a>&nbsp;&nbsp;(stty), 
          <br>
          <a href="c_date.htm">&nbsp;Date Set / Date Display</a> &nbsp;(date),<br>
          <a href="c_flash.htm">&nbsp;Write / Erase Flash Memory areas</a> &nbsp;(&nbsp;flash),<br>
          <a href="c_tr.htm">&nbsp;Transparent Mode</a>&nbsp;&nbsp;(tr), <br>
          <a href="c_flush.htm">&nbsp;Flush the Caches</a>&nbsp;&nbsp;(flush), 
          <br>
          <a href="c_vers.htm">&nbsp;Display the version number</a>, &nbsp;(ver)<br>
          <a href="c_sh.htm">&nbsp;The Command Shell</a>,&nbsp;&nbsp;(sh)<br>
          <a href="c_more.htm">&nbsp;Paginator</a>,&nbsp;&nbsp;(more)<br>
          <a href="c_reboot.htm">&nbsp;Reboot PMON</a>.&nbsp; (reboot)</p>
      </td>
    </tr>
    <tr>
      <td width="155" align="right" valign="top">Diagnostics&nbsp;</td>
      <td width="415" bgcolor="#CCCCCC"><a href="c_mt.htm">&nbsp;Memory Test</a> 
        &nbsp;(mt), <br>
        <a href="c_ping.html">&nbsp;Network PING</a> (ping)</td>
    </tr>
    <tr> 
      <td width="155" align="right" valign="top" bgcolor="#CCCCCC">User Help&nbsp;</td>
      <td width="415"><a href="pmon_faqs.htm">Frequently Asked Questions</a></td>
    </tr>
  </table>
</center>

<p>&nbsp;</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<p><br>
  <br>
</p>
<p>&nbsp; </p>
<hr>
<p><b>Navigation:</b> <a href="pmon.htm">Document Home</a> | Document Contents 
  | <a href="docindex.htm">Document Index</a> <br>
  <!Comment $Id: doctoc.htm,v 1.1.1.1 2006/09/14 01:59:06 root Exp $ >
</body>
</html>
