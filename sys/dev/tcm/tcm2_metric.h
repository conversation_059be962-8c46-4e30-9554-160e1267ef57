#ifndef __TCM2_METRIC_H__
#define __TCM2_METRIC_H__

#include <tcm-common.h>

/* ========================================================================
 * TCM2 度量系统配置和定义
 * ======================================================================== */

// 哈希大小定义
#define TCM2_HASH_SIZE 32

#define TCM2_METRIC_VMLINUZ      0x012ffffe
#define TCM2_METRIC_FILESYSTEM   0x012ffffd
#define TCM2_METRIC_ENV_VARS     0x012fff00

#define EMMC_BASE_PATH "/dev/fs/ext4@emmc0a/"

// eMMC 关键文件白名单配置
// 格式：{文件名, NV索引}
#define EMMC_CRITICAL_FILES_CONFIG \
    {"vmlinuz",       0x012fff00}, \
    {"daemon",        0x012fff01}, \
    {"run.sh",        0x012fff02}, \
    {"printd",        0x012fff03}, \
    {"boot.mk",       0x012fff04}, \
    {"boot.cfg",      0x012fff05}, \
    {"config.ini",    0x012fff06}

#define MAX_EMMC_FILES 16
#define MAX_FILE_PATH_LEN 256
#define MAX_FILE_NAME_LEN 64

// 文件系统度量策略配置
// 开启此宏：度量关键文件
// 关闭此宏：度量整个文件系统镜像
#define MEASURE_CRITICAL_FILES 0
#define STOP_ON_MEASURE_FAILURE 0

// eMMC 文件配置结构体
typedef struct {
    char filename[MAX_FILE_NAME_LEN];
    u32 nv_index;  // NV 空间索引
} emmc_file_config_t;

int tcm2_metric_nv_exists(u32 nv_index);
int tcm2_metric_nv_write(u32 nv_index, const u8 *hash_data);
int tcm2_metric_nv_read(u32 nv_index, u8 *hash_data);
int tcm2_metric_nv_delete(u32 nv_index);
int tcm2_metric_store_or_verify(u32 nv_index, const u8 *computed_hash);

// eMMC 文件测试函数
int tcm2_test_emmc_files(void);                    // 测试 eMMC 文件访问功能
int tcm2_list_emmc_contents(void);                 // 列出 eMMC 目录内容
int tcm2_test_emmc_sm3_hash(void);                 // 测试 eMMC 文件 SM3 哈希计算

int tcm2_measure_critical_files(int argc, char **argv);  // 关键文件度量

// 环境变量度量函数
int tcm2_env_auto_measure(void);                       // 自动环境变量度量
int tcm2_env_restore_cmd(int argc, char **argv);       // 更新环境变量基线
int tcm2_env_status_cmd(int argc, char **argv);        // 显示环境变量度量状态

#endif /* __TCM2_METRIC_H__ */
