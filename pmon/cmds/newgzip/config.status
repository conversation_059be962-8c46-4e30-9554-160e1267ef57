#!/bin/sh
# Generated automatically by configure.
# Run this file to recreate the current configuration.
# This directory was configured as follows,
# on host qiaoch:
#
# ./configure dummy gcc

for arg
do
  case "$arg" in
    -recheck | --recheck | --rechec | --reche | --rech | --rec | --re | --r)
    exec /bin/sh ./configure dummy gcc ;;
    *) echo "Usage: config.status --recheck" 2>&1; exit 1 ;;
  esac
done

trap 'rm -f Makefile; exit 1' 1 3 15
CC='gcc'
CPP='${CC-cc} -E'
INSTALL='/usr/bin/install -c'
INSTALL_PROGRAM='$(INSTALL)'
INSTALL_DATA='$(INSTALL) -m 644'
ZCAT='zcat'
G=''
CFLAGS='-O'
ASCPP='/lib/cpp -DNO_UNDERLINE'
OBJA='match.o'
SEDCMD=''
LIBS=''
srcdir='.'
DEFS=' -DASMV -DSTDC_HEADERS=1 -DHAVE_UNISTD_H=1 -DDIRENT=1'
prefix=''
exec_prefix=''
prsub=''

top_srcdir=$srcdir

# Allow make-time overrides of the generated file list.
test -n "$gen_files" || gen_files="Makefile"

for file in .. $gen_files; do if [ "x$file" != "x.." ]; then
  srcdir=$top_srcdir
  # Remove last slash and all that follows it.  Not all systems have dirname.
  dir=`echo $file|sed 's%/[^/][^/]*$%%'`
  if test "$dir" != "$file"; then
    test "$top_srcdir" != . && srcdir=$top_srcdir/$dir
    test ! -d $dir && mkdir $dir
  fi
  echo creating $file
  rm -f $file
  echo "# Generated automatically from `echo $file|sed 's|.*/||'`.in by configure." > $file
  sed -e "
$prsub
s%@CC@%$CC%g
s%@CPP@%$CPP%g
s%@INSTALL@%$INSTALL%g
s%@INSTALL_PROGRAM@%$INSTALL_PROGRAM%g
s%@INSTALL_DATA@%$INSTALL_DATA%g
s%@ZCAT@%$ZCAT%g
s%@G@%$G%g
s%@CFLAGS@%$CFLAGS%g
s%@ASCPP@%$ASCPP%g
s%@OBJA@%$OBJA%g
s%@SEDCMD@%$SEDCMD%g
s%@LIBS@%$LIBS%g
s%@srcdir@%$srcdir%g
s%@DEFS@%$DEFS%
" $top_srcdir/${file}.in >> $file
fi; done

exit 0
