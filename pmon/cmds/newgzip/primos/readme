To build GZIP for PRIMOS you should just have to type:

   r *>primos>build

while standing in the directory above this PRIMOS subdirectory.

If the files in these directories looks strange, then it might be
because they are in normal ASCII. You'll need to convert them into
PASCII before you will be able to build the GZIP executable.
 
You can find a simple ASCII to PASCII converter via anonymous FTP
from "ftp.lysator.liu.se" in the directory "pub/primos/run" as
the file "topascii.run".

You can reach me at a number of places in case there are any bugs
in this port to report...

   Email:

      <EMAIL>        Signum is a company giving support for 
                           Free Software. Call/Mail us if you're 
                           interrested! (Phone: +46-13-21-46-00)

      <EMAIL>   Lysator is a computer society at the
                           Linkoping University in Sweden.

<PERSON>, 25 May 1993
