/* Gzip build file for Primos.
/* author: <PERSON>, <EMAIL>
/*
type
type 'Building GZIP for PRIMOS - Please Wait...'
type
&if ^ [exists *>PRIMOS>OBJ -dir] &then &do
      type 'Creating *>PRIMOS>OBJ directory...'
      type
      create *>PRIMOS>OBJ
   &end

type 'Compiling...

&set_var unit := 0

/*
&set_var cdefines  :=  -debug

como *>primos>compile.como
&do file &items [wild *>@@.c -single unit]
  &set_var filebase := [before %file% .]
  &if ^  [exists *>primos>obj>%filebase%.bin] &then &do
  type 'Compiling "'%file%'"...'
  ci *>%file% -binary *>primos>obj>=.bin %cdefines% -optionsfile *>primos>ci.opts
  &end
&end
close -unit %unit%
&set_var unit := 0

&do file &items [wild *>primos>@@.c -single unit]
  &set_var filebase := [before %file% .]
  &if ^  [exists *>primos>obj>%filebase%.bin] &then &do
  type 'Compiling "'%file%'"...'
  ci *>primos>%file% -binary *>primos>obj>=.bin %cdefines% -optionsfile *>primos>ci.opts
  &end
&end
como -end

type
type
type 'Binding...'

&set_var unit := 0
&data bind
  li.w ccmain
  load *>primos>obj>gzip
  load *>primos>obj>bits
  load *>primos>obj>crypt
  load *>primos>obj>deflate
  load *>primos>obj>getopt
  load *>primos>obj>inflate
  load *>primos>obj>lzw
  load *>primos>obj>makecrc
  load *>primos>obj>primos
  load *>primos>obj>trees
  load *>primos>obj>unlzw
  load *>primos>obj>unpack
  load *>primos>obj>unlzh
  load *>primos>obj>unzip
  load *>primos>obj>util
  load *>primos>obj>zip
  li.w c_lib
  li
  dynt -all
  nwc
  nitr
  ntw
  compress
  file *>primos>gzip.run
&end

type
type
type 'All done. (Hopefully). The executable should be in *>PRIMOS>GZIP.RUN'
&return
