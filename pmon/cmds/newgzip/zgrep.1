.TH ZGREP 1
.SH NAME
zgrep \- search possibly compressed files for a regular expression
.SH SYNOPSIS
.B zgrep
[ grep_options ]
.BI  [\ -e\ ] " pattern"
.IR filename ".\|.\|."
.SH DESCRIPTION
.IR  Zgrep
is used to invoke the
.I grep
on compress'ed or gzip'ed files. All options specified are passed directly to
.I grep.
If no file is specified, then the standard input is decompressed
if necessary and fed to grep.
Otherwise the given files are uncompressed if necessary and fed to
.I grep.
.PP
If
.I zgrep
is invoked as
.I zegrep
or
.I zfgrep
then
.I egrep
or
.I fgrep
is used instead of
.I grep.
If the GREP environment variable is set,
.I zgrep
uses it as the
.I grep
program to be invoked. For example:

    for sh:  GREP=fgrep  zgrep string files
    for csh: (setenv GREP fgrep; zgrep string files)
.SH AUTHOR
<PERSON> (<EMAIL>)
.SH "SEE ALSO"
grep(1), egrep(1), fgrep(1), zdiff(1), zmore(1), znew(1), zforce(1),
gzip(1), gzexe(1)
