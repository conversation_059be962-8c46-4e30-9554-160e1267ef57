%% TeX macros to handle texinfo files

%   Copyright (C) 1985, 1986, 1988, 1990, 1991 Free Software Foundation, Inc.

%This texinfo.tex file is free software; you can redistribute it and/or
%modify it under the terms of the GNU General Public License as
%published by the Free Software Foundation; either version 2, or (at
%your option) any later version.

%This texinfo.tex file is distributed in the hope that it will be
%useful, but WITHOUT ANY WARRANTY; without even the implied warranty
%of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
%General Public License for more details.

%You should have received a copy of the GNU General Public License
%along with this texinfo.tex file; see the file COPYING.  If not, write
%to the Free Software Foundation, 675 Mass Ave, Cambridge, MA 02139,
%USA.


%In other words, you are welcome to use, share and improve this program.
%You are forbidden to forbid anyone else to use, share and improve
%what you give them.   Help stamp out software-hoarding!

\def\texinfoversion{2.73}
\message{Loading texinfo package [Version \texinfoversion]:}
\message{}

% Print the version number if in a .fmt file.
\everyjob{\message{[Texinfo version \texinfoversion]}\message{}}

% Save some parts of plain tex whose names we will redefine.

\let\ptexlbrace=\{
\let\ptexrbrace=\}
\let\ptexdots=\dots
\let\ptexdot=\.
\let\ptexstar=\*
\let\ptexend=\end
\let\ptexbullet=\bullet
\let\ptexb=\b
\let\ptexc=\c
\let\ptexi=\i
\let\ptext=\t
\let\ptexl=\l
\let\ptexL=\L

\def\tie{\penalty 10000\ }     % Save plain tex definition of ~.

\message{Basics,}
\chardef\other=12

% If this character appears in an error message or help string, it
% starts a new line in the output.
\newlinechar = `^^J

\hyphenation{ap-pen-dix}
\hyphenation{mini-buf-fer mini-buf-fers}
\hyphenation{eshell}

% Margin to add to right of even pages, to left of odd pages.
\newdimen \bindingoffset  \bindingoffset=0pt
\newdimen \normaloffset   \normaloffset=\hoffset
\newdimen\pagewidth \newdimen\pageheight
\pagewidth=\hsize \pageheight=\vsize

% Sometimes it is convenient to have everything in the transcript file
% and nothing on the terminal.  We don't just call \tracingall here,
% since that produces some useless output on the terminal.
%
\def\gloggingall{\begingroup \globaldefs = 1 \loggingall \endgroup}%
\def\loggingall{\tracingcommands2 \tracingstats2 
   \tracingpages1 \tracingoutput1 \tracinglostchars1 
   \tracingmacros2 \tracingparagraphs1 \tracingrestores1 
   \showboxbreadth\maxdimen\showboxdepth\maxdimen
}%

%---------------------Begin change-----------------------
%
%%%% For @cropmarks command.
% Dimensions to add cropmarks at corners Added by P. A. MacKay, 12 Nov. 1986
%
\newdimen\cornerlong \newdimen\cornerthick
\newdimen \topandbottommargin
\newdimen \outerhsize \newdimen \outervsize
\cornerlong=1pc\cornerthick=.3pt	% These set size of cropmarks
\outerhsize=7in
%\outervsize=9.5in
% Alternative @smallbook page size is 9.25in
\outervsize=9.25in
\topandbottommargin=.75in
%
%---------------------End change-----------------------

% \onepageout takes a vbox as an argument.  Note that \pagecontents
% does insertions itself, but you have to call it yourself.
\chardef\PAGE=255  \output={\onepageout{\pagecontents\PAGE}}
\def\onepageout#1{\hoffset=\normaloffset
\ifodd\pageno  \advance\hoffset by \bindingoffset
\else \advance\hoffset by -\bindingoffset\fi
{\escapechar=`\\\relax % makes sure backslash is used in output files.
\shipout\vbox{{\let\hsize=\pagewidth \makeheadline} \pagebody{#1}%
{\let\hsize=\pagewidth \makefootline}}}%
\advancepageno \ifnum\outputpenalty>-20000 \else\dosupereject\fi}

%%%% For @cropmarks command %%%%

% Here is a modification of the main output routine for Near East Publications
% This provides right-angle cropmarks at all four corners.
% The contents of the page are centerlined into the cropmarks,
% and any desired binding offset is added as an \hskip on either
% site of the centerlined box.  (P. A. MacKay, 12 November, 1986)
%
\def\croppageout#1{\hoffset=0pt % make sure this doesn't mess things up
		 \shipout
		 \vbox to \outervsize{\hsize=\outerhsize
                 \vbox{\line{\ewtop\hfill\ewtop}}
                 \nointerlineskip
                 \line{\vbox{\moveleft\cornerthick\nstop}
                       \hfill
                       \vbox{\moveright\cornerthick\nstop}}
                 \vskip \topandbottommargin
                 \centerline{\ifodd\pageno\hskip\bindingoffset\fi
			\vbox{
			{\let\hsize=\pagewidth \makeheadline}
			\pagebody{#1}
			{\let\hsize=\pagewidth \makefootline}}
			\ifodd\pageno\else\hskip\bindingoffset\fi}
		 \vskip \topandbottommargin plus1fill minus1fill
                 \boxmaxdepth\cornerthick
                 \line{\vbox{\moveleft\cornerthick\nsbot}
                       \hfill
                       \vbox{\moveright\cornerthick\nsbot}}
                 \nointerlineskip
                 \vbox{\line{\ewbot\hfill\ewbot}}
	}
  \advancepageno 
  \ifnum\outputpenalty>-20000 \else\dosupereject\fi}
%
% Do @cropmarks to get crop marks
\def\cropmarks{\let\onepageout=\croppageout }

\def\pagebody#1{\vbox to\pageheight{\boxmaxdepth=\maxdepth #1}}
{\catcode`\@ =11
\gdef\pagecontents#1{\ifvoid\topins\else\unvbox\topins\fi
\dimen@=\dp#1 \unvbox#1
\ifvoid\footins\else\vskip\skip\footins\footnoterule \unvbox\footins\fi
\ifr@ggedbottom \kern-\dimen@ \vfil \fi}
}

%
% Here are the rules for the cropmarks.  Note that they are
% offset so that the space between them is truly \outerhsize or \outervsize
% (P. A. MacKay, 12 November, 1986)
%
\def\ewtop{\vrule height\cornerthick depth0pt width\cornerlong}
\def\nstop{\vbox
  {\hrule height\cornerthick depth\cornerlong width\cornerthick}}
\def\ewbot{\vrule height0pt depth\cornerthick width\cornerlong}
\def\nsbot{\vbox
  {\hrule height\cornerlong depth\cornerthick width\cornerthick}}

% Parse an argument, then pass it to #1.
% The argument can be delimited with [...] or with "..." or braces
% or it can be a whole line.
% #1 should be a macro which expects
% an ordinary undelimited TeX argument.

\def\parsearg #1{\let\next=#1\begingroup\obeylines\futurelet\temp\parseargx}

\def\parseargx{%
\ifx \obeyedspace\temp \aftergroup\parseargdiscardspace \else%
\aftergroup \parseargline %
\fi \endgroup}

{\obeyspaces %
\gdef\parseargdiscardspace {\begingroup\obeylines\futurelet\temp\parseargx}}

\gdef\obeyedspace{\ }

\def\parseargline{\begingroup \obeylines \parsearglinex}
{\obeylines %
\gdef\parsearglinex #1^^M{\endgroup \next {#1}}}

\def\flushcr{\ifx\par\lisppar \def\next##1{}\else \let\next=\relax \fi \next}

%% These are used to keep @begin/@end levels from running away
%% Call \inENV within environments (after a \begingroup)
\newif\ifENV \ENVfalse \def\inENV{\ifENV\relax\else\ENVtrue\fi}
\def\ENVcheck{%
\ifENV\errmessage{Still within an environment.  Type Return to continue.}
\endgroup\fi} % This is not perfect, but it should reduce lossage

% @begin foo  is the same as @foo, for now.
\newhelp\EMsimple{Type <Return> to continue}

\outer\def\begin{\parsearg\beginxxx}

\def\beginxxx #1{%
\expandafter\ifx\csname #1\endcsname\relax
{\errhelp=\EMsimple \errmessage{Undefined command @begin #1}}\else
\csname #1\endcsname\fi}

%% @end foo executes the definition of \Efoo.
%% foo can be delimited by doublequotes or brackets.

\def\end{\parsearg\endxxx}

\def\endxxx #1{%
\expandafter\ifx\csname E#1\endcsname\relax
\expandafter\ifx\csname #1\endcsname\relax
\errmessage{Undefined command @end #1}\else
\errorE{#1}\fi\fi
\csname E#1\endcsname}
\def\errorE#1{
{\errhelp=\EMsimple \errmessage{@end #1 not within #1 environment}}}

% Single-spacing is done by various environments.

\newskip\singlespaceskip \singlespaceskip = \baselineskip
\def\singlespace{%
{\advance \baselineskip by -\singlespaceskip
\kern \baselineskip}%
\baselineskip=\singlespaceskip
}

%% Simple single-character @ commands

% @@ prints an @
% Kludge this until the fonts are right (grr).
\def\@{{\tt \char '100}}

% Define @` and @' to be the same as ` and '
% but suppressing ligatures.
\def\`{{`}}
\def\'{{'}}

% Used to generate quoted braces.

\def\mylbrace {{\tt \char '173}}
\def\myrbrace {{\tt \char '175}}
\let\{=\mylbrace
\let\}=\myrbrace

% @: forces normal size whitespace following.
\def\:{\spacefactor=1000 }

% @* forces a line break.
\def\*{\hfil\break\hbox{}\ignorespaces}

% @. is an end-of-sentence period.
\def\.{.\spacefactor=3000 }

% @w prevents a word break.  Without the \leavevmode, @w at the
% beginning of a paragraph, when TeX is still in vertical mode, would
% produce a whole line of output instead of starting the paragraph.
\def\w#1{\leavevmode\hbox{#1}}

% @group ... @end group forces ... to be all on one page, by enclosing
% it in a TeX vbox.  We use \vtop instead of \vbox to construct the box
% to keep its height that of a normal line.  According to the rules for
% \topskip (p.114 of the TeXbook), the glue inserted is
% max (\topskip - \ht (first item), 0).  If that height is large,
% therefore, no glue is inserted, and the space between the headline and
% the text is small, which looks bad.
% 
\def\group{\begingroup
  \ifnum\catcode13=\active \else
    \errhelp = \groupinvalidhelp
    \errmessage{@group invalid in context where filling is enabled}%
  \fi
  \def\Egroup{\egroup\endgroup}%
  \vtop\bgroup
}
%
% TeX puts in an \escapechar (i.e., `@') at the beginning of the help
% message, so this ends up printing `@group can only ...'.
% 
\newhelp\groupinvalidhelp{%
group can only be used in environments such as @example,^^J%
where each line of input produces a line of output.}

% @need space-in-mils
% forces a page break if there is not space-in-mils remaining.

\newdimen\mil  \mil=0.001in

\def\need{\parsearg\needx}

% Old definition--didn't work.
%\def\needx #1{\par %
%% This method tries to make TeX break the page naturally
%% if the depth of the box does not fit.
%{\baselineskip=0pt%
%\vtop to #1\mil{\vfil}\kern -#1\mil\penalty 10000
%\prevdepth=-1000pt
%}}

\def\needx#1{%
  % Go into vertical mode, so we don't make a big box in the middle of a
  % paragraph.
  \par
  %
  % Don't add any leading before our big empty box, but allow a page
  % break, since the best break might be right here.
  \allowbreak
  \nointerlineskip
  \vtop to #1\mil{\vfil}%
  % 
  % TeX does not even consider page breaks if a penalty added to the
  % main vertical list is 10000 or more.  But in order to see if the
  % empty box we just added fits on the page, we must make it consider
  % page breaks.  On the other hand, we don't want to actually break the
  % page after the empty box.  So we use a penalty of 9999.
  % 
  % There is an extremely small chance that TeX will actually break the
  % page at this \penalty, if there are no other feasible breakpoints in
  % sight.  (If the user is using lots of big @group commands, which
  % almost-but-not-quite fill up a page, TeX will have a hard time doing
  % good page breaking, for example.)  However, I could not construct an
  % example where a page broke at this \penalty; if it happens in a real
  % document, then we can reconsider our strategy.
  \penalty9999
  %
  % Back up by the size of the box, whether we did a page break or not.
  \kern -#1\mil
  %
  % Do not allow a page break right after this kern.
  \nobreak
}

% @br   forces paragraph break

\let\br = \par

% @dots{}  output some dots

\def\dots{$\ldots$}

% @page    forces the start of a new page

\def\page{\par\vfill\supereject}

% @exdent text....
% outputs text on separate line in roman font, starting at standard page margin

% This records the amount of indent in the innermost environment.
% That's how much \exdent should take out.
\newskip\exdentamount

% This defn is used inside fill environments such as @defun.
\def\exdent{\parsearg\exdentyyy}
\def\exdentyyy #1{{\hfil\break\hbox{\kern -\exdentamount{\rm#1}}\hfil\break}}

% This defn is used inside nofill environments such as @example.
\def\nofillexdent{\parsearg\nofillexdentyyy}
\def\nofillexdentyyy #1{{\advance \leftskip by -\exdentamount
\leftline{\hskip\leftskip{\rm#1}}}}

%\hbox{{\rm#1}}\hfil\break}}

% @include file    insert text of that file as input.

\def\include{\parsearg\includezzz}
\def\includezzz #1{{\def\thisfile{#1}\input #1
}}

\def\thisfile{}

% @center line   outputs that line, centered

\def\center{\parsearg\centerzzz}
\def\centerzzz #1{{\advance\hsize by -\leftskip
\advance\hsize by -\rightskip
\centerline{#1}}}

% @sp n   outputs n lines of vertical space

\def\sp{\parsearg\spxxx}
\def\spxxx #1{\par \vskip #1\baselineskip}

% @comment ...line which is ignored...
% @c is the same as @comment
% @ignore ... @end ignore  is another way to write a comment

\def\comment{\catcode 64=\other \catcode 123=\other \catcode 125=\other%
\parsearg \commentxxx}

\def\commentxxx #1{\catcode 64=0 \catcode 123=1 \catcode 125=2 }

\let\c=\comment

% Prevent errors for section commands.
% Used in @ignore and in failing conditionals.
\def\ignoresections{%
\let\chapter=\relax
\let\unnumbered=\relax
\let\top=\relax
\let\unnumberedsec=\relax
\let\unnumberedsection=\relax
\let\unnumberedsubsec=\relax
\let\unnumberedsubsection=\relax
\let\unnumberedsubsubsec=\relax
\let\unnumberedsubsubsection=\relax
\let\section=\relax
\let\subsec=\relax
\let\subsubsec=\relax
\let\subsection=\relax
\let\subsubsection=\relax
\let\appendix=\relax
\let\appendixsec=\relax
\let\appendixsection=\relax
\let\appendixsubsec=\relax
\let\appendixsubsection=\relax
\let\appendixsubsubsec=\relax
\let\appendixsubsubsection=\relax
\let\contents=\relax
\let\smallbook=\relax
\let\titlepage=\relax
}

\def\ignore{\begingroup\ignoresections
% Make sure that spaces turn into tokens that match what \ignorexxx wants.
\catcode32=10
\ignorexxx}
\long\def\ignorexxx #1\end ignore{\endgroup\ignorespaces}

\def\direntry{\begingroup\direntryxxx}
\long\def\direntryxxx #1\end direntry{\endgroup\ignorespaces}

% Conditionals to test whether a flag is set.

\def\ifset{\begingroup\ignoresections\parsearg\ifsetxxx}

\def\ifsetxxx #1{\endgroup
\expandafter\ifx\csname IF#1\endcsname\relax \let\temp=\ifsetfail
\else \let\temp=\relax \fi
\temp}
\def\Eifset{}
\def\ifsetfail{\begingroup\ignoresections\ifsetfailxxx}
\long\def\ifsetfailxxx #1\end ifset{\endgroup\ignorespaces}

\def\ifclear{\begingroup\ignoresections\parsearg\ifclearxxx}

\def\ifclearxxx #1{\endgroup
\expandafter\ifx\csname IF#1\endcsname\relax \let\temp=\relax
\else \let\temp=\ifclearfail \fi
\temp}
\def\Eifclear{}
\def\ifclearfail{\begingroup\ignoresections\ifclearfailxxx}
\long\def\ifclearfailxxx #1\end ifclear{\endgroup\ignorespaces}

% @set foo     to set the flag named foo.
% @clear foo   to clear the flag named foo.
\def\set{\parsearg\setxxx}
\def\setxxx #1{
\expandafter\let\csname IF#1\endcsname=\set}

\def\clear{\parsearg\clearxxx}
\def\clearxxx #1{
\expandafter\let\csname IF#1\endcsname=\relax}

% Some texinfo constructs that are trivial in tex

\def\iftex{}
\def\Eiftex{}
\def\ifinfo{\begingroup\ignoresections\ifinfoxxx}
\long\def\ifinfoxxx #1\end ifinfo{\endgroup\ignorespaces}

\long\def\menu #1\end menu{}
\def\asis#1{#1}

% @math means output in math mode.
% We don't use $'s directly in the definition of \math because control
% sequences like \math are expanded when the toc file is written.  Then,
% we read the toc file back, the $'s will be normal characters (as they
% should be, according to the definition of Texinfo).  So we must use a
% control sequence to switch into and out of math mode.
% 
% This isn't quite enough for @math to work properly in indices, but it
% seems unlikely it will ever be needed there.
% 
\let\implicitmath = $
\def\math#1{\implicitmath #1\implicitmath}

\def\node{\ENVcheck\parsearg\nodezzz}
\def\nodezzz#1{\nodexxx [#1,]}
\def\nodexxx[#1,#2]{\gdef\lastnode{#1}}
\let\lastnode=\relax

\def\donoderef{\ifx\lastnode\relax\else
\expandafter\expandafter\expandafter\setref{\lastnode}\fi
\let\lastnode=\relax}

\def\unnumbnoderef{\ifx\lastnode\relax\else
\expandafter\expandafter\expandafter\unnumbsetref{\lastnode}\fi
\let\lastnode=\relax}

\def\appendixnoderef{\ifx\lastnode\relax\else
\expandafter\expandafter\expandafter\appendixsetref{\lastnode}\fi
\let\lastnode=\relax}

\let\refill=\relax
  
% @setfilename is done at the beginning of every texinfo file.
% So open here the files we need to have open while reading the input.
% This makes it possible to make a .fmt file for texinfo.
\def\setfilename{%
   \readauxfile
   \opencontents
   \openindices
   \fixbackslash  % Turn off hack to swallow `\input texinfo'.
   \global\let\setfilename=\comment % Ignore extra @setfilename cmds.
   \comment % Ignore the actual filename.
}

\outer\def\bye{\pagealignmacro\tracingstats=1\ptexend}

\def\inforef #1{\inforefzzz #1,,,,**}
\def\inforefzzz #1,#2,#3,#4**{See Info file \file{\losespace#3{}},
  node \samp{\losespace#1{}}}
\def\losespace #1{#1}

\message{fonts,}

% Font-change commands.

% Texinfo supports the sans serif font style, which plain TeX does not.
% So we set up a \sf analogous to plain's \rm, etc.
\newfam\sffam
\def\sf{\fam=\sffam \tensf}
\let\li = \sf % Sometimes we call it \li, not \sf.

%% Try out Computer Modern fonts at \magstephalf
\let\mainmagstep=\magstephalf

\ifx\bigger\relax
\let\mainmagstep=\magstep1
\font\textrm=cmr12
\font\texttt=cmtt12
\else
\font\textrm=cmr10 scaled \mainmagstep
\font\texttt=cmtt10 scaled \mainmagstep
\fi
% Instead of cmb10, you many want to use cmbx10.
% cmbx10 is a prettier font on its own, but cmb10
% looks better when embedded in a line with cmr10.
\font\textbf=cmb10 scaled \mainmagstep 
\font\textit=cmti10 scaled \mainmagstep
\font\textsl=cmsl10 scaled \mainmagstep
\font\textsf=cmss10 scaled \mainmagstep
\font\textsc=cmcsc10 scaled \mainmagstep
\font\texti=cmmi10 scaled \mainmagstep
\font\textsy=cmsy10 scaled \mainmagstep

% A few fonts for @defun, etc.
\font\defbf=cmbx10 scaled \magstep1 %was 1314
\font\deftt=cmtt10 scaled \magstep1
\def\df{\let\tentt=\deftt \let\tenbf = \defbf \bf}

% Fonts for indices and small examples.
% We actually use the slanted font rather than the italic, 
% because texinfo normally uses the slanted fonts for that.
% Do not make many font distinctions in general in the index, since they
% aren't very useful.
\font\ninett=cmtt9
\font\indrm=cmr9
\font\indit=cmsl9
\let\indsl=\indit
\let\indtt=\ninett
\let\indsf=\indrm
\let\indbf=\indrm
\let\indsc=\indrm
\font\indi=cmmi9
\font\indsy=cmsy9

% Fonts for headings
\font\chaprm=cmbx12 scaled \magstep2
\font\chapit=cmti12 scaled \magstep2
\font\chapsl=cmsl12 scaled \magstep2
\font\chaptt=cmtt12 scaled \magstep2
\font\chapsf=cmss12 scaled \magstep2
\let\chapbf=\chaprm
\font\chapsc=cmcsc10 scaled\magstep3
\font\chapi=cmmi12 scaled \magstep2
\font\chapsy=cmsy10 scaled \magstep3

\font\secrm=cmbx12 scaled \magstep1
\font\secit=cmti12 scaled \magstep1
\font\secsl=cmsl12 scaled \magstep1
\font\sectt=cmtt12 scaled \magstep1
\font\secsf=cmss12 scaled \magstep1
\font\secbf=cmbx12 scaled \magstep1
\font\secsc=cmcsc10 scaled\magstep2
\font\seci=cmmi12 scaled \magstep1
\font\secsy=cmsy10 scaled \magstep2

% \font\ssecrm=cmbx10 scaled \magstep1    % This size an font looked bad.
% \font\ssecit=cmti10 scaled \magstep1    % The letters were too crowded.
% \font\ssecsl=cmsl10 scaled \magstep1
% \font\ssectt=cmtt10 scaled \magstep1
% \font\ssecsf=cmss10 scaled \magstep1

%\font\ssecrm=cmb10 scaled 1315	% Note the use of cmb rather than cmbx.
%\font\ssecit=cmti10 scaled 1315	% Also, the size is a little larger than
%\font\ssecsl=cmsl10 scaled 1315	% being scaled magstep1.
%\font\ssectt=cmtt10 scaled 1315
%\font\ssecsf=cmss10 scaled 1315

%\let\ssecbf=\ssecrm

\font\ssecrm=cmbx12 scaled \magstephalf
\font\ssecit=cmti12 scaled \magstephalf
\font\ssecsl=cmsl12 scaled \magstephalf
\font\ssectt=cmtt12 scaled \magstephalf
\font\ssecsf=cmss12 scaled \magstephalf
\font\ssecbf=cmbx12 scaled \magstephalf
\font\ssecsc=cmcsc10 scaled \magstep1 
\font\sseci=cmmi12 scaled \magstephalf
\font\ssecsy=cmsy10 scaled \magstep1
% The smallcaps and symbol fonts should actually be scaled \magstep1.5,
% but that is not a standard magnification.

% Fonts for title page:
\font\titlerm = cmbx12 scaled \magstep3
\let\authorrm = \secrm

% In order for the font changes to affect most math symbols and letters,
% we have to define the \textfont of the standard families.  Since
% texinfo doesn't allow for producing subscripts and superscripts, we
% don't bother to reset \scriptfont and \scriptscriptfont (which would
% also require loading a lot more fonts).
% 
\def\resetmathfonts{%
  \textfont0 = \tenrm \textfont1 = \teni \textfont2 = \tensy
  \textfont\itfam = \tenit \textfont\slfam = \tensl \textfont\bffam = \tenbf
  \textfont\ttfam = \tentt \textfont\sffam = \tensf
}


% The font-changing commands redefine the meanings of \tenSTYLE, instead
% of just \STYLE.  We do this so that font changes will continue to work
% in math mode, where it is the current \fam that is relevant in most
% cases, not the current.  Plain TeX does, for example,
% \def\bf{\fam=\bffam \tenbf}  By redefining \tenbf, we obviate the need
% to redefine \bf itself.  
\def\textfonts{%
  \let\tenrm=\textrm \let\tenit=\textit \let\tensl=\textsl
  \let\tenbf=\textbf \let\tentt=\texttt \let\smallcaps=\textsc
  \let\tensf=\textsf \let\teni=\texti \let\tensy=\textsy
  \resetmathfonts}
\def\chapfonts{%
  \let\tenrm=\chaprm \let\tenit=\chapit \let\tensl=\chapsl 
  \let\tenbf=\chapbf \let\tentt=\chaptt \let\smallcaps=\chapsc
  \let\tensf=\chapsf \let\teni=\chapi \let\tensy=\chapsy
  \resetmathfonts}
\def\secfonts{%
  \let\tenrm=\secrm \let\tenit=\secit \let\tensl=\secsl
  \let\tenbf=\secbf \let\tentt=\sectt \let\smallcaps=\secsc
  \let\tensf=\secsf \let\teni=\seci \let\tensy=\secsy
  \resetmathfonts}
\def\subsecfonts{%
  \let\tenrm=\ssecrm \let\tenit=\ssecit \let\tensl=\ssecsl
  \let\tenbf=\ssecbf \let\tentt=\ssectt \let\smallcaps=\ssecsc
  \let\tensf=\ssecsf \let\teni=\sseci \let\tensy=\ssecsy
  \resetmathfonts}
\def\indexfonts{%
  \let\tenrm=\indrm \let\tenit=\indit \let\tensl=\indsl
  \let\tenbf=\indbf \let\tentt=\indtt \let\smallcaps=\indsc
  \let\tensf=\indsf \let\teni=\indi \let\tensy=\indsy
  \resetmathfonts}

% Set up the default fonts, so we can use them for creating boxes.
% 
\textfonts

% Count depth in font-changes, for error checks
\newcount\fontdepth \fontdepth=0

% Fonts for short table of contents.
\font\shortcontrm=cmr12
\font\shortcontbf=cmbx12
\font\shortcontsl=cmsl12

%% Add scribe-like font environments, plus @l for inline lisp (usually sans
%% serif) and @ii for TeX italic

% \smartitalic{ARG} outputs arg in italics, followed by an italic correction
% unless the following character is such as not to need one.
\def\smartitalicx{\ifx\next,\else\ifx\next-\else\ifx\next.\else\/\fi\fi\fi}
\def\smartitalic#1{{\sl #1}\futurelet\next\smartitalicx}

\let\i=\smartitalic
\let\var=\smartitalic
\let\dfn=\smartitalic
\let\emph=\smartitalic
\let\cite=\smartitalic

\def\b#1{{\bf #1}}
\let\strong=\b

\def\t#1{{\tt \exhyphenpenalty=10000\rawbackslash \frenchspacing #1}\null}
\let\ttfont = \t
%\def\samp #1{`{\tt \rawbackslash \frenchspacing #1}'\null}
\def\samp #1{`\tclose{#1}'\null}
\def\key #1{{\tt \exhyphenpenalty=10000\uppercase{#1}}\null}
\def\ctrl #1{{\tt \rawbackslash \hat}#1}

\let\file=\samp

% @code is a modification of @t,
% which makes spaces the same size as normal in the surrounding text.
\newdimen\tclosesave
\newdimen\tcloserm
\def\tclose#1{{\rm \tcloserm=\fontdimen2\font \tt \tclosesave=\fontdimen2\font
\fontdimen2\font=\tcloserm
% prevent breaking lines at hyphens.
\exhyphenpenalty=10000
\def\ {{\fontdimen2\font=\tclosesave{} }}%
 \rawbackslash \frenchspacing #1\fontdimen2\font=\tclosesave}\null}
\let\code=\tclose
%\let\exp=\tclose  %Was temporary

% @kbd is like @code, except that if the argument is just one @key command, 
% then @kbd has no effect.

\def\xkey{\key}
\def\kbdfoo#1#2#3\par{\def\one{#1}\def\three{#3}\def\threex{??}%
\ifx\one\xkey\ifx\threex\three \key{#2}%
\else\tclose{\look}\fi
\else\tclose{\look}\fi}

% Typeset a dimension, e.g., `in' or `pt'.  The only reason for the
% argument is to make the input look right: @dmn{pt} instead of
% @dmn{}pt.
% 
\def\dmn#1{\thinspace #1}

\def\kbd#1{\def\look{#1}\expandafter\kbdfoo\look??\par}

\def\l#1{{\li #1}\null}		% 

\def\r#1{{\rm #1}}		% roman font
% Use of \lowercase was suggested.
\def\sc#1{{\smallcaps#1}}	% smallcaps font
\def\ii#1{{\it #1}}		% italic font

\message{page headings,}

\newskip\titlepagetopglue \titlepagetopglue = 1.5in
\newskip\titlepagebottomglue \titlepagebottomglue = 2pc

% First the title page.  Must do @settitle before @titlepage.
\def\titlefont#1{{\titlerm #1}}

\newtoks\realeverypar
\newif\ifseenauthor
\newif\iffinishedtitlepage

\def\titlepage{\begingroup \parindent=0pt \textfonts
   \let\subtitlerm=\tenrm
% I deinstalled the following change because \cmr12 is undefined.
% This change was not in the ChangeLog anyway.  --rms.
%   \let\subtitlerm=\cmr12
   \def\subtitlefont{\subtitlerm \normalbaselineskip = 13pt \normalbaselines}%
   %
   \def\authorfont{\authorrm \normalbaselineskip = 16pt \normalbaselines}%
   %
   % Leave some space at the very top of the page.
   \vglue\titlepagetopglue
   %
   % Now you can print the title using @title.
   \def\title{\parsearg\titlezzz}%
   \def\titlezzz##1{\leftline{\titlefont{##1}}
		    % print a rule at the page bottom also.
		    \finishedtitlepagefalse
		    \vskip4pt \hrule height 4pt \vskip4pt}%
   % No rule at page bottom unless we print one at the top with @title.
   \finishedtitlepagetrue
   %
   % Now you can put text using @subtitle.
   \def\subtitle{\parsearg\subtitlezzz}%
   \def\subtitlezzz##1{{\subtitlefont \rightline{##1}}}%
   %
   % <AUTHOR> come last, but may come many times.
   \def\author{\parsearg\authorzzz}%
   \def\authorzzz##1{\ifseenauthor\else\vskip 0pt plus 1filll\seenauthortrue\fi
      {\authorfont \leftline{##1}}}%
   %  
   % Most title ``pages'' are actually two pages long, with space
   % at the top of the second.  We don't want the ragged left on the second.
   \let\oldpage = \page
   \def\page{%
      \iffinishedtitlepage\else
	 \finishtitlepage
      \fi
      \oldpage
      \let\page = \oldpage
      \hbox{}}%
%   \def\page{\oldpage \hbox{}}
}

\def\Etitlepage{%
   \iffinishedtitlepage\else
      \finishtitlepage
   \fi
   % It is important to do the page break before ending the group,
   % because the headline and footline are only empty inside the group.
   % If we use the new definition of \page, we always get a blank page
   % after the title page, which we certainly don't want.
   \oldpage
   \endgroup
   \HEADINGSon
}

\def\finishtitlepage{%
   \vskip4pt \hrule height 2pt
   \vskip\titlepagebottomglue
   \finishedtitlepagetrue
}

%%% Set up page headings and footings.

\let\thispage=\folio

\newtoks \evenheadline    % Token sequence for heading line of even pages
\newtoks \oddheadline     % Token sequence for heading line of odd pages
\newtoks \evenfootline    % Token sequence for footing line of even pages
\newtoks \oddfootline     % Token sequence for footing line of odd pages

% Now make Tex use those variables
\headline={{\textfonts\rm \ifodd\pageno \the\oddheadline
                            \else \the\evenheadline \fi}}
\footline={{\textfonts\rm \ifodd\pageno \the\oddfootline
                            \else \the\evenfootline \fi}\HEADINGShook}
\let\HEADINGShook=\relax

% Commands to set those variables.
% For example, this is what  @headings on  does
% @evenheading @thistitle|@thispage|@thischapter
% @oddheading @thischapter|@thispage|@thistitle
% @evenfooting @thisfile||
% @oddfooting ||@thisfile

\def\evenheading{\parsearg\evenheadingxxx}
\def\oddheading{\parsearg\oddheadingxxx}
\def\everyheading{\parsearg\everyheadingxxx}

\def\evenfooting{\parsearg\evenfootingxxx}
\def\oddfooting{\parsearg\oddfootingxxx}
\def\everyfooting{\parsearg\everyfootingxxx}

{\catcode`\@=0 %

\gdef\evenheadingxxx #1{\evenheadingyyy #1@|@|@|@|\finish}
\gdef\evenheadingyyy #1@|#2@|#3@|#4\finish{%
\global\evenheadline={\rlap{\centerline{#2}}\line{#1\hfil#3}}}

\gdef\oddheadingxxx #1{\oddheadingyyy #1@|@|@|@|\finish}
\gdef\oddheadingyyy #1@|#2@|#3@|#4\finish{%
\global\oddheadline={\rlap{\centerline{#2}}\line{#1\hfil#3}}}

\gdef\everyheadingxxx #1{\everyheadingyyy #1@|@|@|@|\finish}
\gdef\everyheadingyyy #1@|#2@|#3@|#4\finish{%
\global\evenheadline={\rlap{\centerline{#2}}\line{#1\hfil#3}}
\global\oddheadline={\rlap{\centerline{#2}}\line{#1\hfil#3}}}

\gdef\evenfootingxxx #1{\evenfootingyyy #1@|@|@|@|\finish}
\gdef\evenfootingyyy #1@|#2@|#3@|#4\finish{%
\global\evenfootline={\rlap{\centerline{#2}}\line{#1\hfil#3}}}

\gdef\oddfootingxxx #1{\oddfootingyyy #1@|@|@|@|\finish}
\gdef\oddfootingyyy #1@|#2@|#3@|#4\finish{%
\global\oddfootline={\rlap{\centerline{#2}}\line{#1\hfil#3}}}

\gdef\everyfootingxxx #1{\everyfootingyyy #1@|@|@|@|\finish}
\gdef\everyfootingyyy #1@|#2@|#3@|#4\finish{%
\global\evenfootline={\rlap{\centerline{#2}}\line{#1\hfil#3}}
\global\oddfootline={\rlap{\centerline{#2}}\line{#1\hfil#3}}}
%
}% unbind the catcode of @.

% @headings double	turns headings on for double-sided printing.
% @headings single	turns headings on for single-sided printing.
% @headings off		turns them off.
% @headings on		same as @headings double, retained for compatibility.
% @headings after	turns on double-sided headings after this page.
% @headings doubleafter	turns on double-sided headings after this page.
% @headings singleafter turns on single-sided headings after this page.
% By default, they are off.

\def\headings #1 {\csname HEADINGS#1\endcsname}

\def\HEADINGSoff{
\global\evenheadline={\hfil} \global\evenfootline={\hfil}
\global\oddheadline={\hfil} \global\oddfootline={\hfil}}
\HEADINGSoff
% When we turn headings on, set the page number to 1.
% For double-sided printing, put current file name in lower left corner,
% chapter name on inside top of right hand pages, document
% title on inside top of left hand pages, and page numbers on outside top
% edge of all pages.
\def\HEADINGSdouble{
%\pagealignmacro
\global\pageno=1
\global\evenfootline={\hfil}
\global\oddfootline={\hfil}
\global\evenheadline={\line{\folio\hfil\thistitle}}
\global\oddheadline={\line{\thischapter\hfil\folio}}
}
% For single-sided printing, chapter title goes across top left of page,
% page number on top right.
\def\HEADINGSsingle{
%\pagealignmacro
\global\pageno=1
\global\evenfootline={\hfil}
\global\oddfootline={\hfil}
\global\evenheadline={\line{\thischapter\hfil\folio}}
\global\oddheadline={\line{\thischapter\hfil\folio}}
}
\def\HEADINGSon{\HEADINGSdouble}

\def\HEADINGSafter{\let\HEADINGShook=\HEADINGSdoublex}
\let\HEADINGSdoubleafter=\HEADINGSafter
\def\HEADINGSdoublex{%
\global\evenfootline={\hfil}
\global\oddfootline={\hfil}
\global\evenheadline={\line{\folio\hfil\thistitle}}
\global\oddheadline={\line{\thischapter\hfil\folio}}
}

\def\HEADINGSsingleafter{\let\HEADINGShook=\HEADINGSsinglex}
\def\HEADINGSsinglex{%
\global\evenfootline={\hfil}
\global\oddfootline={\hfil}
\global\evenheadline={\line{\thischapter\hfil\folio}}
\global\oddheadline={\line{\thischapter\hfil\folio}}
}

% Subroutines used in generating headings
% Produces Day Month Year style of output.
\def\today{\number\day\space
\ifcase\month\or
January\or February\or March\or April\or May\or June\or
July\or August\or September\or October\or November\or December\fi
\space\number\year}

% Use this if you want the Month Day, Year style of output.
%\def\today{\ifcase\month\or
%January\or February\or March\or April\or May\or June\or
%July\or August\or September\or October\or November\or December\fi
%\space\number\day, \number\year}

% @settitle line...  specifies the title of the document, for headings
% It generates no output of its own

\def\thistitle{No Title}
\def\settitle{\parsearg\settitlezzz}
\def\settitlezzz #1{\gdef\thistitle{#1}}

\message{tables,}

% @tabs -- simple alignment

% These don't work.  For one thing, \+ is defined as outer.
% So these macros cannot even be defined.

%\def\tabs{\parsearg\tabszzz}
%\def\tabszzz #1{\settabs\+#1\cr}
%\def\tabline{\parsearg\tablinezzz}
%\def\tablinezzz #1{\+#1\cr}
%\def\&{&}

% Tables -- @table, @ftable, @vtable, @item(x), @kitem(x), @xitem(x).

% default indentation of table text
\newdimen\tableindent \tableindent=.8in
% default indentation of @itemize and @enumerate text
\newdimen\itemindent  \itemindent=.3in
% margin between end of table item and start of table text.
\newdimen\itemmargin  \itemmargin=.1in

% used internally for \itemindent minus \itemmargin
\newdimen\itemmax

% Note @table, @vtable, and @vtable define @item, @itemx, etc., with
% these defs.
% They also define \itemindex
% to index the item name in whatever manner is desired (perhaps none).

\def\internalBitem{\smallbreak \parsearg\itemzzz}
\def\internalBitemx{\par \parsearg\itemzzz}

\def\internalBxitem "#1"{\def\xitemsubtopix{#1} \smallbreak \parsearg\xitemzzz}
\def\internalBxitemx "#1"{\def\xitemsubtopix{#1} \par \parsearg\xitemzzz}

\def\internalBkitem{\smallbreak \parsearg\kitemzzz}
\def\internalBkitemx{\par \parsearg\kitemzzz}

\def\kitemzzz #1{\dosubind {kw}{\code{#1}}{for {\bf \lastfunction}}%
                 \itemzzz {#1}}

\def\xitemzzz #1{\dosubind {kw}{\code{#1}}{for {\bf \xitemsubtopic}}%
                 \itemzzz {#1}}

\def\itemzzz #1{\begingroup %
  \advance\hsize by -\rightskip
  \advance\hsize by -\tableindent
  \setbox0=\hbox{\itemfont{#1}}%
  \itemindex{#1}%
  \nobreak % This prevents a break before @itemx.
  %
  % Be sure we are not still in the middle of a paragraph.
  \parskip=0in
  \par
  %
  % If the item text does not fit in the space we have, put it on a line
  % by itself, and do not allow a page break either before or after that
  % line.  We do not start a paragraph here because then if the next
  % command is, e.g., @kindex, the whatsit would get put into the
  % horizontal list on a line by itself, resulting in extra blank space.
  \ifdim \wd0>\itemmax
    \setbox0=\hbox{\hskip \leftskip \hskip -\tableindent \unhbox0}\box0
    \nobreak
  \else
    % The item text fits into the space.  Start a paragraph, so that the
    % following text (if any) will end up on the same line.  Since that
    % text will be indented by \tableindent, we make the item text be in
    % a zero-width box.
    \noindent
    \rlap{\hskip -\tableindent\box0}%
  \fi
  \endgroup
}

\def\item{\errmessage{@item while not in a table}}
\def\itemx{\errmessage{@itemx while not in a table}}
\def\kitem{\errmessage{@kitem while not in a table}}
\def\kitemx{\errmessage{@kitemx while not in a table}}
\def\xitem{\errmessage{@xitem while not in a table}}
\def\xitemx{\errmessage{@xitemx while not in a table}}

%% Contains a kludge to get @end[description] to work
\def\description{\tablez{\dontindex}{1}{}{}{}{}}

\def\table{\begingroup\inENV\obeylines\obeyspaces\tablex}
{\obeylines\obeyspaces%
\gdef\tablex #1^^M{%
\tabley\dontindex#1        \endtabley}}

\def\ftable{\begingroup\inENV\obeylines\obeyspaces\ftablex}
{\obeylines\obeyspaces%
\gdef\ftablex #1^^M{%
\tabley\fnitemindex#1        \endtabley
\def\Eftable{\endgraf\endgroup\afterenvbreak}%
\let\Etable=\relax}}

\def\vtable{\begingroup\inENV\obeylines\obeyspaces\vtablex}
{\obeylines\obeyspaces%
\gdef\vtablex #1^^M{%
\tabley\vritemindex#1        \endtabley
\def\Evtable{\endgraf\endgroup\afterenvbreak}%
\let\Etable=\relax}}

\def\dontindex #1{}
\def\fnitemindex #1{\doind {fn}{\code{#1}}}%
\def\vritemindex #1{\doind {vr}{\code{#1}}}%

{\obeyspaces %
\gdef\tabley#1#2 #3 #4 #5 #6 #7\endtabley{\endgroup%
\tablez{#1}{#2}{#3}{#4}{#5}{#6}}}

\def\tablez #1#2#3#4#5#6{%
\aboveenvbreak %
\begingroup %
\def\Edescription{\Etable}% Neccessary kludge.
\let\itemindex=#1%
\ifnum 0#3>0 \advance \leftskip by #3\mil \fi %
\ifnum 0#4>0 \tableindent=#4\mil \fi %
\ifnum 0#5>0 \advance \rightskip by #5\mil \fi %
\def\itemfont{#2}%
\itemmax=\tableindent %
\advance \itemmax by -\itemmargin %
\advance \leftskip by \tableindent %
\exdentamount=\tableindent
\parindent = 0pt
\parskip = \smallskipamount
\ifdim \parskip=0pt \parskip=2pt \fi%
\def\Etable{\endgraf\endgroup\afterenvbreak}%
\let\item = \internalBitem %
\let\itemx = \internalBitemx %
\let\kitem = \internalBkitem %
\let\kitemx = \internalBkitemx %
\let\xitem = \internalBxitem %
\let\xitemx = \internalBxitemx %
}

% This is the counter used by @enumerate, which is really @itemize

\newcount \itemno

\def\itemize{\parsearg\itemizezzz}

\def\itemizezzz #1{%
  \begingroup % ended by the @end itemsize
  \itemizey {#1}{\Eitemize}
}

\def\itemizey #1#2{%
\aboveenvbreak %
\itemmax=\itemindent %
\advance \itemmax by -\itemmargin %
\advance \leftskip by \itemindent %
\exdentamount=\itemindent
\parindent = 0pt %
\parskip = \smallskipamount %
\ifdim \parskip=0pt \parskip=2pt \fi%
\def#2{\endgraf\endgroup\afterenvbreak}%
\def\itemcontents{#1}%
\let\item=\itemizeitem}

\def\bullet{$\ptexbullet$}
\def\minus{$-$}

% Set sfcode to normal for the chars that usually have another value.
% These are `.?!:;,'
\def\frenchspacing{\sfcode46=1000 \sfcode63=1000 \sfcode33=1000
  \sfcode58=1000 \sfcode59=1000 \sfcode44=1000 }

% \splitoff TOKENS\endmark defines \first to be the first token in
% TOKENS, and \rest to be the remainder.
% 
\def\splitoff#1#2\endmark{\def\first{#1}\def\rest{#2}}%

% Allow an optional argument of an uppercase letter, lowercase letter,
% or number, to specify the first label in the enumerated list.  No
% argument is the same as `1'.
% 
\def\enumerate{\parsearg\enumeratezzz}
\def\enumeratezzz #1{\enumeratey #1  \endenumeratey}
\def\enumeratey #1 #2\endenumeratey{%
  \begingroup % ended by the @end enumerate
  %
  % If we were given no argument, pretend we were given `1'.
  \def\thearg{#1}%
  \ifx\thearg\empty \def\thearg{1}\fi
  %
  % Detect if the argument is a single token.  If so, it might be a
  % letter.  Otherwise, the only valid thing it can be is a number.
  % (We will always have one token, because of the test we just made.
  % This is a good thing, since \splitoff doesn't work given nothing at
  % all -- the first parameter is undelimited.)
  \expandafter\splitoff\thearg\endmark
  \ifx\rest\empty
    % Only one token in the argument.  It could still be anything.
    % A ``lowercase letter'' is one whose \lccode is nonzero.
    % An ``uppercase letter'' is one whose \lccode is both nonzero, and
    %   not equal to itself.
    % Otherwise, we assume it's a number.
    % 
    % We need the \relax at the end of the \ifnum lines to stop TeX from
    % continuing to look for a <number>.
    % 
    \ifnum\lccode\expandafter`\thearg=0\relax 
      \numericenumerate % a number (we hope)
    \else
      % It's a letter.
      \ifnum\lccode\expandafter`\thearg=\expandafter`\thearg\relax
        \lowercaseenumerate % lowercase letter
      \else
        \uppercaseenumerate % uppercase letter
      \fi
    \fi
  \else
    % Multiple tokens in the argument.  We hope it's a number.
    \numericenumerate
  \fi
}

% An @enumerate whose labels are integers.  The starting integer is
% given in \thearg.
% 
\def\numericenumerate{%
  \itemno = \thearg
  \startenumeration{\the\itemno}%
}

% The starting (lowercase) letter is in \thearg.
\def\lowercaseenumerate{%
  \itemno = \expandafter`\thearg
  \startenumeration{%
    % Be sure we're not beyond the end of the alphabet.
    \ifnum\itemno=0
      \errmessage{No more lowercase letters in @enumerate; get a bigger
                  alphabet}%
    \fi
    \char\lccode\itemno
  }%
}

% The starting (uppercase) letter is in \thearg.
\def\uppercaseenumerate{%
  \itemno = \expandafter`\thearg
  \startenumeration{%
    % Be sure we're not beyond the end of the alphabet.
    \ifnum\itemno=0
      \errmessage{No more uppercase letters in @enumerate; get a bigger
                  alphabet}
    \fi
    \char\uccode\itemno
  }%
}

% Call itemizey, adding a period to the first argument and supplying the
% common last two arguments.  Also subtract one from the initial value in
% \itemno, since @item increments \itemno.
% 
\def\startenumeration#1{%
  \advance\itemno by -1
  \itemizey{#1.}\Eenumerate\flushcr
}

% @alphaenumerate and @capsenumerate are abbreviations for giving an arg
% to @enumerate.
% 
\def\alphaenumerate{\enumerate{a}}
\def\capsenumerate{\enumerate{A}}
\def\Ealphaenumerate{\Eenumerate}
\def\Ecapsenumerate{\Eenumerate}

% Definition of @item while inside @itemize.

\def\itemizeitem{%
\advance\itemno by 1
{\let\par=\endgraf \smallbreak}%
\ifhmode \errmessage{\in hmode at itemizeitem}\fi
{\parskip=0in \hskip 0pt
\hbox to 0pt{\hss \itemcontents\hskip \itemmargin}%
\vadjust{\penalty 1200}}%
\flushcr}

\message{indexing,}
% Index generation facilities

% Define \newwrite to be identical to plain tex's \newwrite
% except not \outer, so it can be used within \newindex.
{\catcode`\@=11
\gdef\newwrite{\alloc@7\write\chardef\sixt@@n}}

% \newindex {foo} defines an index named foo.
% It automatically defines \fooindex such that
% \fooindex ...rest of line... puts an entry in the index foo.
% It also defines \fooindfile to be the number of the output channel for
% the file that	accumulates this index.  The file's extension is foo.
% The name of an index should be no more than 2 characters long
% for the sake of vms.

\def\newindex #1{
\expandafter\newwrite \csname#1indfile\endcsname% Define number for output file
\openout \csname#1indfile\endcsname \jobname.#1	% Open the file
\expandafter\xdef\csname#1index\endcsname{%	% Define \xxxindex
\noexpand\doindex {#1}}
}

% @defindex foo  ==  \newindex{foo}

\def\defindex{\parsearg\newindex}

% Define @defcodeindex, like @defindex except put all entries in @code.

\def\newcodeindex #1{
\expandafter\newwrite \csname#1indfile\endcsname% Define number for output file
\openout \csname#1indfile\endcsname \jobname.#1	% Open the file
\expandafter\xdef\csname#1index\endcsname{%	% Define \xxxindex
\noexpand\docodeindex {#1}}
}

\def\defcodeindex{\parsearg\newcodeindex}

% @synindex foo bar    makes index foo feed into index bar.
% Do this instead of @defindex foo if you don't want it as a separate index.
\def\synindex #1 #2 {%
\expandafter\let\expandafter\synindexfoo\expandafter=\csname#2indfile\endcsname
\expandafter\let\csname#1indfile\endcsname=\synindexfoo
\expandafter\xdef\csname#1index\endcsname{%	% Define \xxxindex
\noexpand\doindex {#2}}%
}

% @syncodeindex foo bar   similar, but put all entries made for index foo
% inside @code.
\def\syncodeindex #1 #2 {%
\expandafter\let\expandafter\synindexfoo\expandafter=\csname#2indfile\endcsname
\expandafter\let\csname#1indfile\endcsname=\synindexfoo
\expandafter\xdef\csname#1index\endcsname{%	% Define \xxxindex
\noexpand\docodeindex {#2}}%
}

% Define \doindex, the driver for all \fooindex macros.
% Argument #1 is generated by the calling \fooindex macro,
%  and it is "foo", the name of the index.

% \doindex just uses \parsearg; it calls \doind for the actual work.
% This is because \doind is more useful to call from other macros.

% There is also \dosubind {index}{topic}{subtopic}
% which makes an entry in a two-level index such as the operation index.

\def\doindex#1{\edef\indexname{#1}\parsearg\singleindexer}
\def\singleindexer #1{\doind{\indexname}{#1}}

% like the previous two, but they put @code around the argument.
\def\docodeindex#1{\edef\indexname{#1}\parsearg\singlecodeindexer}
\def\singlecodeindexer #1{\doind{\indexname}{\code{#1}}}

\def\indexdummies{%
\def\_{{\realbackslash _}}%
\def\w{\realbackslash w }%
\def\bf{\realbackslash bf }%
\def\rm{\realbackslash rm }%
\def\sl{\realbackslash sl }%
\def\sf{\realbackslash sf}%
\def\tt{\realbackslash tt}%
\def\gtr{\realbackslash gtr}%
\def\less{\realbackslash less}%
\def\hat{\realbackslash hat}%
\def\char{\realbackslash char}%
\def\TeX{\realbackslash TeX}%
\def\dots{\realbackslash dots }%
\def\copyright{\realbackslash copyright }%
\def\tclose##1{\realbackslash tclose {##1}}%
\def\code##1{\realbackslash code {##1}}%
\def\samp##1{\realbackslash samp {##1}}%
\def\t##1{\realbackslash r {##1}}%
\def\r##1{\realbackslash r {##1}}%
\def\i##1{\realbackslash i {##1}}%
\def\b##1{\realbackslash b {##1}}%
\def\cite##1{\realbackslash cite {##1}}%
\def\key##1{\realbackslash key {##1}}%
\def\file##1{\realbackslash file {##1}}%
\def\var##1{\realbackslash var {##1}}%
\def\kbd##1{\realbackslash kbd {##1}}%
}

% \indexnofonts no-ops all font-change commands.
% This is used when outputting the strings to sort the index by.
\def\indexdummyfont#1{#1}
\def\indexdummytex{TeX}
\def\indexdummydots{...}

\def\indexnofonts{%
\let\w=\indexdummyfont
\let\t=\indexdummyfont
\let\r=\indexdummyfont
\let\i=\indexdummyfont
\let\b=\indexdummyfont
\let\emph=\indexdummyfont
\let\strong=\indexdummyfont
\let\cite=\indexdummyfont
\let\sc=\indexdummyfont
%Don't no-op \tt, since it isn't a user-level command
% and is used in the definitions of the active chars like <, >, |...
%\let\tt=\indexdummyfont
\let\tclose=\indexdummyfont
\let\code=\indexdummyfont
\let\file=\indexdummyfont
\let\samp=\indexdummyfont
\let\kbd=\indexdummyfont
\let\key=\indexdummyfont
\let\var=\indexdummyfont
\let\TeX=\indexdummytex
\let\dots=\indexdummydots
}

% To define \realbackslash, we must make \ not be an escape.
% We must first make another character (@) an escape
% so we do not become unable to do a definition.

{\catcode`\@=0 \catcode`\\=\other
@gdef@realbackslash{\}}

\let\indexbackslash=0  %overridden during \printindex.

\def\doind #1#2{%
{\count10=\lastpenalty %
{\indexdummies % Must do this here, since \bf, etc expand at this stage
\escapechar=`\\%
{\let\folio=0% Expand all macros now EXCEPT \folio
\def\rawbackslashxx{\indexbackslash}% \indexbackslash isn't defined now
% so it will be output as is; and it will print as backslash in the indx.
%
% Now process the index-string once, with all font commands turned off,
% to get the string to sort the index by.
{\indexnofonts
\xdef\temp1{#2}%
}%
% Now produce the complete index entry.  We process the index-string again,
% this time with font commands expanded, to get what to print in the index.
\edef\temp{%
\write \csname#1indfile\endcsname{%
\realbackslash entry {\temp1}{\folio}{#2}}}%
\temp }%
}\penalty\count10}}

\def\dosubind #1#2#3{%
{\count10=\lastpenalty %
{\indexdummies % Must do this here, since \bf, etc expand at this stage
\escapechar=`\\%
{\let\folio=0%
\def\rawbackslashxx{\indexbackslash}%
%
% Now process the index-string once, with all font commands turned off,
% to get the string to sort the index by.
{\indexnofonts
\xdef\temp1{#2 #3}%
}%
% Now produce the complete index entry.  We process the index-string again,
% this time with font commands expanded, to get what to print in the index.
\edef\temp{%
\write \csname#1indfile\endcsname{%
\realbackslash entry {\temp1}{\folio}{#2}{#3}}}%
\temp }%
}\penalty\count10}}

% The index entry written in the file actually looks like
%  \entry {sortstring}{page}{topic}
% or
%  \entry {sortstring}{page}{topic}{subtopic}
% The texindex program reads in these files and writes files
% containing these kinds of lines:
%  \initial {c}
%     before the first topic whose initial is c
%  \entry {topic}{pagelist}
%     for a topic that is used without subtopics
%  \primary {topic}
%     for the beginning of a topic that is used with subtopics
%  \secondary {subtopic}{pagelist}
%     for each subtopic.

% Define the user-accessible indexing commands 
% @findex, @vindex, @kindex, @cindex.

\def\findex {\fnindex}
\def\kindex {\kyindex}
\def\cindex {\cpindex}
\def\vindex {\vrindex}
\def\tindex {\tpindex}
\def\pindex {\pgindex}

\def\cindexsub {\begingroup\obeylines\cindexsub}
{\obeylines %
\gdef\cindexsub "#1" #2^^M{\endgroup %
\dosubind{cp}{#2}{#1}}}

% Define the macros used in formatting output of the sorted index material.

% This is what you call to cause a particular index to get printed.
% Write
% @unnumbered Function Index
% @printindex fn

\def\printindex{\parsearg\doprintindex}

\def\doprintindex#1{%
  \tex
  \dobreak \chapheadingskip {10000}
  \catcode`\%=\other\catcode`\&=\other\catcode`\#=\other
  \catcode`\$=\other\catcode`\_=\other
  \catcode`\~=\other
  %
  % The following don't help, since the chars were translated
  % when the raw index was written, and their fonts were discarded
  % due to \indexnofonts.
  %\catcode`\"=\active
  %\catcode`\^=\active
  %\catcode`\_=\active
  %\catcode`\|=\active
  %\catcode`\<=\active
  %\catcode`\>=\active
  % %
  \def\indexbackslash{\rawbackslashxx}
  \indexfonts\rm \tolerance=9500 \advance\baselineskip -1pt
  \begindoublecolumns
  %
  % See if the index file exists and is nonempty.
  \openin 1 \jobname.#1s
  \ifeof 1 
    % \enddoublecolumns gets confused if there is no text in the index,
    % and it loses the chapter title and the aux file entries for the
    % index.  The easiest way to prevent this problem is to make sure
    % there is some text.
    (Index is nonexistent)
    \else
    %
    % If the index file exists but is empty, then \openin leaves \ifeof
    % false.  We have to make TeX try to read something from the file, so
    % it can discover if there is anything in it.
    \read 1 to \temp
    \ifeof 1
      (Index is empty)
    \else
      \input \jobname.#1s
    \fi
  \fi
  \closein 1
  \enddoublecolumns
  \Etex
}

% These macros are used by the sorted index file itself.
% Change them to control the appearance of the index.

% Same as \bigskipamount except no shrink.
% \balancecolumns gets confused if there is any shrink.
\newskip\initialskipamount \initialskipamount 12pt plus4pt

\def\initial #1{%
{\let\tentt=\sectt \let\tt=\sectt \let\sf=\sectt
\ifdim\lastskip<\initialskipamount
\removelastskip \penalty-200 \vskip \initialskipamount\fi
\line{\secbf#1\hfill}\kern 2pt\penalty10000}}

\def\entry #1#2{\begingroup
  \parfillskip=0in \parskip=0in \parindent=0in
  %
  % \hangindent is only relevant when the page number and the entry text
  % don't fit on one line.  In that case, bob suggests starting the dots
  % pretty far over on the line.
  % \hangafter is reset to 1 at the start of each paragraph.
  \hangindent=.75\hsize
  \noindent
  %
  % Don't break the text of the index entry.
  \hbox{#1}%
  %
  % If we must, put the page number on a line of its own, and fill out
  % this line with blank space.  (The \hfil is overwhelmed with the
  % fill leaders glue in \indexdotfill if the page number does fit.)
  \hfil\penalty50
  \null\nobreak\indexdotfill % Have leaders before the page number.
  %
  % The `\ ' here is removed by the implicit \unskip that TeX does as
  % part of (the primitive) \par.  Without, a spurious underfull \hbox ensues.
  \ #2% The page number ends the paragraph.
  \par
\endgroup}

% Like \dotfill except takes at least 1 em.
\def\indexdotfill{\cleaders
  \hbox{$\mathsurround=0pt \mkern1.5mu . \mkern1.5mu$}\hskip 1em plus 1fill}

\def\primary #1{\line{#1\hfil}}

\newskip\secondaryindent \secondaryindent=0.5cm

\def\secondary #1#2{
{\parfillskip=0in \parskip=0in
\hangindent =1in \hangafter=1
\noindent\hskip\secondaryindent\hbox{#1}\indexdotfill #2\par
}}

%% Define two-column mode, which is used in indexes.
%% Adapted from the TeXBook, page 416
\catcode `\@=11

\newbox\partialpage

\newdimen\doublecolumnhsize  \doublecolumnhsize = 3.11in
\newdimen\doublecolumnvsize  \doublecolumnvsize = 19.1in
\newdimen\availdimen@

\def\begindoublecolumns{\begingroup
  \output={\global\setbox\partialpage=
    \vbox{\unvbox255\kern -\topskip \kern \baselineskip}}\eject
  \output={\doublecolumnout}%
  \hsize=\doublecolumnhsize \vsize=\doublecolumnvsize}
\def\enddoublecolumns{\output={\balancecolumns}\eject
  \endgroup \pagegoal=\vsize}

\def\doublecolumnout{\splittopskip=\topskip \splitmaxdepth=\maxdepth
  \dimen@=\pageheight \advance\dimen@ by-\ht\partialpage
  \setbox0=\vsplit255 to\dimen@ \setbox2=\vsplit255 to\dimen@
  \onepageout\pagesofar \unvbox255 \penalty\outputpenalty}
\def\pagesofar{\unvbox\partialpage %
  \hsize=\doublecolumnhsize % have to restore this since output routine
%	      changes it to set cropmarks (P. A. MacKay, 12 Nov. 1986)
  \wd0=\hsize \wd2=\hsize \hbox to\pagewidth{\box0\hfil\box2}}
\def\balancecolumns{%
% Unset the glue.
  \setbox255=\vbox{\unvbox255}
  \dimen@=\ht255
  \advance\dimen@ by\topskip \advance\dimen@ by-\baselineskip
  \divide\dimen@ by2
  \availdimen@=\pageheight \advance\availdimen@ by-\ht\partialpage
% If the remaining data is too big for one page,
% output one page normally, then work with what remains.
  \ifdim \dimen@>\availdimen@
   {
     \splittopskip=\topskip \splitmaxdepth=\maxdepth
     \dimen@=\pageheight \advance\dimen@ by-\ht\partialpage
     \setbox0=\vsplit255 to\dimen@ \setbox2=\vsplit255 to\dimen@
     \onepageout\pagesofar
   }
% Recompute size of what remains, in case we just output some of it.
  \dimen@=\ht255
  \advance\dimen@ by\topskip \advance\dimen@ by-\baselineskip
  \divide\dimen@ by2
  \fi
  \setbox0=\vbox{\unvbox255}
  \splittopskip=\topskip
  {\vbadness=10000 \loop \global\setbox3=\copy0
    \global\setbox1=\vsplit3 to\dimen@
    \ifdim\ht3>\dimen@ \global\advance\dimen@ by1pt \repeat}
  \setbox0=\vbox to\dimen@{\unvbox1}  \setbox2=\vbox to\dimen@{\unvbox3}
  \pagesofar}

\catcode `\@=\other
\message{sectioning,}
% Define chapters, sections, etc.

\newcount \chapno
\newcount \secno        \secno=0
\newcount \subsecno     \subsecno=0
\newcount \subsubsecno  \subsubsecno=0

% This counter is funny since it counts through charcodes of letters A, B, ...
\newcount \appendixno  \appendixno = `\@
\def\appendixletter{\char\the\appendixno}

\newwrite \contentsfile
% This is called from \setfilename.
\def\opencontents{\openout \contentsfile = \jobname.toc}

% Each @chapter defines this as the name of the chapter.
% page headings and footings can use it.  @section does likewise

\def\thischapter{} \def\thissection{}
\def\seccheck#1{\if \pageno<0 %
\errmessage{@#1 not allowed after generating table of contents}\fi
%
}

\def\chapternofonts{%
\let\rawbackslash=\relax%
\let\frenchspacing=\relax%
\def\result{\realbackslash result}
\def\equiv{\realbackslash equiv}
\def\expansion{\realbackslash expansion}
\def\print{\realbackslash print}
\def\TeX{\realbackslash TeX}
\def\dots{\realbackslash dots}
\def\copyright{\realbackslash copyright}
\def\tt{\realbackslash tt}
\def\bf{\realbackslash bf }
\def\w{\realbackslash w}
\def\less{\realbackslash less}
\def\gtr{\realbackslash gtr}
\def\hat{\realbackslash hat}
\def\char{\realbackslash char}
\def\tclose##1{\realbackslash tclose {##1}}
\def\code##1{\realbackslash code {##1}}
\def\samp##1{\realbackslash samp {##1}}
\def\r##1{\realbackslash r {##1}}
\def\b##1{\realbackslash b {##1}}
\def\key##1{\realbackslash key {##1}}
\def\file##1{\realbackslash file {##1}}
\def\kbd##1{\realbackslash kbd {##1}}
% These are redefined because @smartitalic wouldn't work inside xdef.
\def\i##1{\realbackslash i {##1}}
\def\cite##1{\realbackslash cite {##1}}
\def\var##1{\realbackslash var {##1}}
\def\emph##1{\realbackslash emph {##1}}
\def\dfn##1{\realbackslash dfn {##1}}
}

\def\thischaptername{No Chapter Title}
\outer\def\chapter{\parsearg\chapterzzz}
\def\chapterzzz #1{\seccheck{chapter}%
\secno=0 \subsecno=0 \subsubsecno=0
\global\advance \chapno by 1 \message{Chapter \the\chapno}%
\chapmacro {#1}{\the\chapno}%
\gdef\thissection{#1}%
\gdef\thischaptername{#1}%
% We don't substitute the actual chapter name into \thischapter
% because we don't want its macros evaluated now.
\xdef\thischapter{Chapter \the\chapno: \noexpand\thischaptername}%
{\chapternofonts%
\edef\temp{{\realbackslash chapentry {#1}{\the\chapno}{\noexpand\folio}}}%
\escapechar=`\\%
\write \contentsfile \temp  %
\donoderef %
\global\let\section = \numberedsec
\global\let\subsection = \numberedsubsec
\global\let\subsubsection = \numberedsubsubsec
}}

\outer\def\appendix{\parsearg\appendixzzz}
\def\appendixzzz #1{\seccheck{appendix}%
\secno=0 \subsecno=0 \subsubsecno=0
\global\advance \appendixno by 1 \message{Appendix \appendixletter}%
\chapmacro {#1}{Appendix \appendixletter}%
\gdef\thissection{#1}%
\gdef\thischaptername{#1}%
\xdef\thischapter{Appendix \appendixletter: \noexpand\thischaptername}%
{\chapternofonts%
\edef\temp{{\realbackslash chapentry 
  {#1}{Appendix \appendixletter}{\noexpand\folio}}}%
\escapechar=`\\%
\write \contentsfile \temp  %
\appendixnoderef %
\global\let\section = \appendixsec
\global\let\subsection = \appendixsubsec
\global\let\subsubsection = \appendixsubsubsec
}}

\outer\def\top{\parsearg\unnumberedzzz}
\outer\def\unnumbered{\parsearg\unnumberedzzz}
\def\unnumberedzzz #1{\seccheck{unnumbered}%
\secno=0 \subsecno=0 \subsubsecno=0 \message{(#1)}
\unnumbchapmacro {#1}%
\gdef\thischapter{#1}\gdef\thissection{#1}%
{\chapternofonts%
\edef\temp{{\realbackslash unnumbchapentry {#1}{\noexpand\folio}}}%
\escapechar=`\\%
\write \contentsfile \temp  %
\unnumbnoderef %
\global\let\section = \unnumberedsec
\global\let\subsection = \unnumberedsubsec
\global\let\subsubsection = \unnumberedsubsubsec
}}

\outer\def\numberedsec{\parsearg\seczzz}
\def\seczzz #1{\seccheck{section}%
\subsecno=0 \subsubsecno=0 \global\advance \secno by 1 %
\gdef\thissection{#1}\secheading {#1}{\the\chapno}{\the\secno}%
{\chapternofonts%
\edef\temp{{\realbackslash secentry %
{#1}{\the\chapno}{\the\secno}{\noexpand\folio}}}%
\escapechar=`\\%
\write \contentsfile \temp %
\donoderef %
\penalty 10000 %
}}

\outer\def\appendixsection{\parsearg\appendixsectionzzz}
\outer\def\appendixsec{\parsearg\appendixsectionzzz}
\def\appendixsectionzzz #1{\seccheck{appendixsection}%
\subsecno=0 \subsubsecno=0 \global\advance \secno by 1 %
\gdef\thissection{#1}\secheading {#1}{\appendixletter}{\the\secno}%
{\chapternofonts%
\edef\temp{{\realbackslash secentry %
{#1}{\appendixletter}{\the\secno}{\noexpand\folio}}}%
\escapechar=`\\%
\write \contentsfile \temp %
\appendixnoderef %
\penalty 10000 %
}}

\outer\def\unnumberedsec{\parsearg\unnumberedseczzz}
\def\unnumberedseczzz #1{\seccheck{unnumberedsec}%
\plainsecheading {#1}\gdef\thissection{#1}%
{\chapternofonts%
\edef\temp{{\realbackslash unnumbsecentry{#1}{\noexpand\folio}}}%
\escapechar=`\\%
\write \contentsfile \temp %
\unnumbnoderef %
\penalty 10000 %
}}

\outer\def\numberedsubsec{\parsearg\numberedsubseczzz}
\def\numberedsubseczzz #1{\seccheck{subsection}%
\gdef\thissection{#1}\subsubsecno=0 \global\advance \subsecno by 1 %
\subsecheading {#1}{\the\chapno}{\the\secno}{\the\subsecno}%
{\chapternofonts%
\edef\temp{{\realbackslash subsecentry %
{#1}{\the\chapno}{\the\secno}{\the\subsecno}{\noexpand\folio}}}%
\escapechar=`\\%
\write \contentsfile \temp %
\donoderef %
\penalty 10000 %
}}

\outer\def\appendixsubsec{\parsearg\appendixsubseczzz}
\def\appendixsubseczzz #1{\seccheck{appendixsubsec}%
\gdef\thissection{#1}\subsubsecno=0 \global\advance \subsecno by 1 %
\subsecheading {#1}{\appendixletter}{\the\secno}{\the\subsecno}%
{\chapternofonts%
\edef\temp{{\realbackslash subsecentry %
{#1}{\appendixletter}{\the\secno}{\the\subsecno}{\noexpand\folio}}}%
\escapechar=`\\%
\write \contentsfile \temp %
\appendixnoderef %
\penalty 10000 %
}}

\outer\def\unnumberedsubsec{\parsearg\unnumberedsubseczzz}
\def\unnumberedsubseczzz #1{\seccheck{unnumberedsubsec}%
\plainsecheading {#1}\gdef\thissection{#1}%
{\chapternofonts%
\edef\temp{{\realbackslash unnumbsubsecentry{#1}{\noexpand\folio}}}%
\escapechar=`\\%
\write \contentsfile \temp %
\unnumbnoderef %
\penalty 10000 %
}}

\outer\def\numberedsubsubsec{\parsearg\numberedsubsubseczzz}
\def\numberedsubsubseczzz #1{\seccheck{subsubsection}%
\gdef\thissection{#1}\global\advance \subsubsecno by 1 %
\subsubsecheading {#1}
  {\the\chapno}{\the\secno}{\the\subsecno}{\the\subsubsecno}%
{\chapternofonts%
\edef\temp{{\realbackslash subsubsecentry %
  {#1}
  {\the\chapno}{\the\secno}{\the\subsecno}{\the\subsubsecno}
  {\noexpand\folio}}}%
\escapechar=`\\%
\write \contentsfile \temp %
\donoderef %
\penalty 10000 %
}}

\outer\def\appendixsubsubsec{\parsearg\appendixsubsubseczzz}
\def\appendixsubsubseczzz #1{\seccheck{appendixsubsubsec}%
\gdef\thissection{#1}\global\advance \subsubsecno by 1 %
\subsubsecheading {#1}
  {\appendixletter}{\the\secno}{\the\subsecno}{\the\subsubsecno}%
{\chapternofonts%
\edef\temp{{\realbackslash subsubsecentry{#1}%
  {\appendixletter}
  {\the\secno}{\the\subsecno}{\the\subsubsecno}{\noexpand\folio}}}%
\escapechar=`\\%
\write \contentsfile \temp %
\appendixnoderef %
\penalty 10000 %
}}

\outer\def\unnumberedsubsubsec{\parsearg\unnumberedsubsubseczzz}
\def\unnumberedsubsubseczzz #1{\seccheck{unnumberedsubsubsec}%
\plainsecheading {#1}\gdef\thissection{#1}%
{\chapternofonts%
\edef\temp{{\realbackslash unnumbsubsubsecentry{#1}{\noexpand\folio}}}%
\escapechar=`\\%
\write \contentsfile \temp %
\unnumbnoderef %
\penalty 10000 %
}}

% These are variants which are not "outer", so they can appear in @ifinfo.
% Actually, they should now be obsolete; ordinary section commands should work.
\def\infotop{\parsearg\unnumberedzzz}
\def\infounnumbered{\parsearg\unnumberedzzz}
\def\infounnumberedsec{\parsearg\unnumberedseczzz}
\def\infounnumberedsubsec{\parsearg\unnumberedsubseczzz}
\def\infounnumberedsubsubsec{\parsearg\unnumberedsubsubseczzz}

\def\infoappendix{\parsearg\appendixzzz}
\def\infoappendixsec{\parsearg\appendixseczzz}
\def\infoappendixsubsec{\parsearg\appendixsubseczzz}
\def\infoappendixsubsubsec{\parsearg\appendixsubsubseczzz}

\def\infochapter{\parsearg\chapterzzz}
\def\infosection{\parsearg\sectionzzz}
\def\infosubsection{\parsearg\subsectionzzz}
\def\infosubsubsection{\parsearg\subsubsectionzzz}

% These macros control what the section commands do, according
% to what kind of chapter we are in (ordinary, appendix, or unnumbered).
% Define them by default for a numbered chapter.
\global\let\section = \numberedsec
\global\let\subsection = \numberedsubsec
\global\let\subsubsection = \numberedsubsubsec

% Define @majorheading, @heading and @subheading

% NOTE on use of \vbox for chapter headings, section headings, and
% such:
%	1) We use \vbox rather than the earlier \line to permit
%	   overlong headings to fold.
%	2) \hyphenpenalty is set to 10000 because hyphenation in a
%	   heading is obnoxious; this forbids it.
%       3) Likewise, headings look best if no \parindent is used, and
%          if justification is not attempted.  Hence \raggedright.


\def\majorheading{\parsearg\majorheadingzzz}
\def\majorheadingzzz #1{%
{\advance\chapheadingskip by 10pt \chapbreak }%
{\chapfonts \vbox{\hyphenpenalty=10000\tolerance=5000
                  \parindent=0pt\raggedright
                  \rm #1\hfill}}\bigskip \par\penalty 200}

\def\chapheading{\parsearg\chapheadingzzz}
\def\chapheadingzzz #1{\chapbreak %
{\chapfonts \vbox{\hyphenpenalty=10000\tolerance=5000
                  \parindent=0pt\raggedright
                  \rm #1\hfill}}\bigskip \par\penalty 200}

\def\heading{\parsearg\secheadingi}

\def\subheading{\parsearg\subsecheadingi}

\def\subsubheading{\parsearg\subsubsecheadingi}

% These macros generate a chapter, section, etc. heading only
% (including whitespace, linebreaking, etc. around it),
% given all the information in convenient, parsed form.

%%% Args are the skip and penalty (usually negative)
\def\dobreak#1#2{\par\ifdim\lastskip<#1\removelastskip\penalty#2\vskip#1\fi}

\def\setchapterstyle #1 {\csname CHAPF#1\endcsname}

%%% Define plain chapter starts, and page on/off switching for it
% Parameter controlling skip before chapter headings (if needed)

\newskip \chapheadingskip \chapheadingskip = 30pt plus 8pt minus 4pt

\def\chapbreak{\dobreak \chapheadingskip {-4000}}
\def\chappager{\par\vfill\supereject}
\def\chapoddpage{\chappager \ifodd\pageno \else \hbox to 0pt{} \chappager\fi}

\def\setchapternewpage #1 {\csname CHAPPAG#1\endcsname}

\def\CHAPPAGoff{
\global\let\pchapsepmacro=\chapbreak
\global\let\pagealignmacro=\chappager}

\def\CHAPPAGon{
\global\let\pchapsepmacro=\chappager
\global\let\pagealignmacro=\chappager
\global\def\HEADINGSon{\HEADINGSsingle}}

\def\CHAPPAGodd{
\global\let\pchapsepmacro=\chapoddpage
\global\let\pagealignmacro=\chapoddpage
\global\def\HEADINGSon{\HEADINGSdouble}}

\CHAPPAGon

\def\CHAPFplain{
\global\let\chapmacro=\chfplain
\global\let\unnumbchapmacro=\unnchfplain}

\def\chfplain #1#2{%
  \pchapsepmacro
  {%
    \chapfonts \vbox{\hyphenpenalty=10000\tolerance=5000
                     \parindent=0pt\raggedright
                     \rm #2\enspace #1}%
  }%
  \bigskip
  \penalty5000
}

\def\unnchfplain #1{%
\pchapsepmacro %
{\chapfonts \vbox{\hyphenpenalty=10000\tolerance=5000
                  \parindent=0pt\raggedright
                  \rm #1\hfill}}\bigskip \par\penalty 10000 %
}
\CHAPFplain % The default

\def\unnchfopen #1{%
\chapoddpage {\chapfonts \vbox{\hyphenpenalty=10000\tolerance=5000
                       \parindent=0pt\raggedright
                       \rm #1\hfill}}\bigskip \par\penalty 10000 %
}

\def\chfopen #1#2{\chapoddpage {\chapfonts
\vbox to 3in{\vfil \hbox to\hsize{\hfil #2} \hbox to\hsize{\hfil #1} \vfil}}%
\par\penalty 5000 %
}

\def\CHAPFopen{
\global\let\chapmacro=\chfopen
\global\let\unnumbchapmacro=\unnchfopen}

% Parameter controlling skip before section headings.

\newskip \subsecheadingskip  \subsecheadingskip = 17pt plus 8pt minus 4pt
\def\subsecheadingbreak{\dobreak \subsecheadingskip {-500}}

\newskip \secheadingskip  \secheadingskip = 21pt plus 8pt minus 4pt
\def\secheadingbreak{\dobreak \secheadingskip {-1000}}

% @paragraphindent  is defined for the Info formatting commands only.
\let\paragraphindent=\comment

% Section fonts are the base font at magstep2, which produces
% a size a bit more than 14 points in the default situation.	

\def\secheading #1#2#3{\secheadingi {#2.#3\enspace #1}}
\def\plainsecheading #1{\secheadingi {#1}}
\def\secheadingi #1{{\advance \secheadingskip by \parskip %
\secheadingbreak}%
{\secfonts \vbox{\hyphenpenalty=10000\tolerance=5000
                 \parindent=0pt\raggedright
                 \rm #1\hfill}}%
\ifdim \parskip<10pt \kern 10pt\kern -\parskip\fi \penalty 10000 }


% Subsection fonts are the base font at magstep1, 
% which produces a size of 12 points.

\def\subsecheading #1#2#3#4{\subsecheadingi {#2.#3.#4\enspace #1}}
\def\subsecheadingi #1{{\advance \subsecheadingskip by \parskip %
\subsecheadingbreak}%
{\subsecfonts \vbox{\hyphenpenalty=10000\tolerance=5000
                     \parindent=0pt\raggedright
                     \rm #1\hfill}}%
\ifdim \parskip<10pt \kern 10pt\kern -\parskip\fi \penalty 10000 }

\def\subsubsecfonts{\subsecfonts} % Maybe this should change:
				  % Perhaps make sssec fonts scaled
				  % magstep half
\def\subsubsecheading #1#2#3#4#5{\subsubsecheadingi {#2.#3.#4.#5\enspace #1}}
\def\subsubsecheadingi #1{{\advance \subsecheadingskip by \parskip %
\subsecheadingbreak}%
{\subsubsecfonts \vbox{\hyphenpenalty=10000\tolerance=5000
                       \parindent=0pt\raggedright
                       \rm #1\hfill}}%
\ifdim \parskip<10pt \kern 10pt\kern -\parskip\fi \penalty 10000}


\message{toc printing,}

% Finish up the main text and prepare to read what we've written
% to \contentsfile.

\newskip\contentsrightmargin \contentsrightmargin=1in
\def\startcontents#1{%
   \pagealignmacro
   \immediate\closeout \contentsfile
   \ifnum \pageno>0
      \pageno = -1		% Request roman numbered pages.
   \fi
   % Don't need to put `Contents' or `Short Contents' in the headline. 
   % It is abundantly clear what they are.
   \unnumbchapmacro{#1}\def\thischapter{}%
   \begingroup   		% Set up to handle contents files properly.
      \catcode`\\=0  \catcode`\{=1  \catcode`\}=2  \catcode`\@=11
      \raggedbottom             % Worry more about breakpoints than the bottom.
      \advance\hsize by -\contentsrightmargin % Don't use the full line length.
}

  
% Normal (long) toc.
\outer\def\contents{%
   \startcontents{Table of Contents}%
      \input \jobname.toc
   \endgroup
   \vfill \eject
}

% And just the chapters.
\outer\def\summarycontents{%
   \startcontents{Short Contents}%
      %
      \let\chapentry = \shortchapentry
      \let\unnumbchapentry = \shortunnumberedentry
      % We want a true roman here for the page numbers.
      \secfonts
      \let\rm=\shortcontrm \let\bf=\shortcontbf \let\sl=\shortcontsl
      \rm
      \advance\baselineskip by 1pt % Open it up a little.
      \def\secentry ##1##2##3##4{}
      \def\unnumbsecentry ##1##2{}
      \def\subsecentry ##1##2##3##4##5{}
      \def\unnumbsubsecentry ##1##2{}
      \def\subsubsecentry ##1##2##3##4##5##6{}
      \def\unnumbsubsubsecentry ##1##2{}
      \input \jobname.toc
   \endgroup
   \vfill \eject
}
\let\shortcontents = \summarycontents

% These macros generate individual entries in the table of contents.
% The first argument is the chapter or section name.
% The last argument is the page number.
% The arguments in between are the chapter number, section number, ...

% Chapter-level things, for both the long and short contents.
\def\chapentry#1#2#3{\dochapentry{#2\labelspace#1}{#3}}

% See comments in \dochapentry re vbox and related settings
\def\shortchapentry#1#2#3{%
   \vbox{\hyphenpenalty=10000\tolerance=5000
    \parindent=0pt\strut\raggedright
    {#2\labelspace #1}\dotfill\doshortpageno{#3}}%
}

\def\unnumbchapentry#1#2{\dochapentry{#1}{#2}}
\def\shortunnumberedentry#1#2{%
   \vbox{\hyphenpenalty=10000\tolerance=5000
    \parindent=0pt\strut\raggedright
    #1\dotfill\doshortpageno{#2}}%
}

% Sections.
\def\secentry#1#2#3#4{\dosecentry{#2.#3\labelspace#1}{#4}}
\def\unnumbsecentry#1#2{\dosecentry{#1}{#2}}

% Subsections.
\def\subsecentry#1#2#3#4#5{\dosubsecentry{#2.#3.#4\labelspace#1}{#5}}
\def\unnumbsubsecentry#1#2{\dosubsecentry{#1}{#2}}

% And subsubsections.
\def\subsubsecentry#1#2#3#4#5#6{%
  \dosubsubsecentry{#2.#3.#4.#5\labelspace#1}{#6}}
\def\unnumbsubsubsecentry#1#2{\dosubsubsecentry{#1}{#2}}


% This parameter controls the indentation of the various levels.
\newdimen\tocindent \tocindent = 3pc

% Now for the actual typesetting. In all these, #1 is the text and #2 is the 
% page number.
%
% If the toc has to be broken over pages, we would want to be at chapters 
% if at all possible; hence the \penalty.
\def\dochapentry#1#2{%
   \penalty-300 \vskip\baselineskip
   % This \vbox (and similar ones in dosecentry etc.) used to be a
   % \line; changed to permit linebreaks for long headings.  See
   % comments above \majorheading.  Here we also use \strut to
   % keep the top end of the vbox from jamming up against the previous
   % entry in the table of contents.
   \vbox{\chapentryfonts
     \hyphenpenalty=10000\tolerance=5000 % this line and next introduced
     \parindent=0pt\strut\raggedright    % with \line -> \vbox change
     #1\dotfill
     \dopageno{#2}}%
   \nobreak\vskip .25\baselineskip
}

\def\dosecentry#1#2{%
   \vbox{\secentryfonts \leftskip=\tocindent
    \hyphenpenalty=10000\tolerance=5000
    \parindent=0pt\strut\raggedright #1\dotfill
    \dopageno{#2}}%
}

\def\dosubsecentry#1#2{%
   \vbox{\subsecentryfonts \leftskip=2\tocindent
    \hyphenpenalty=10000\tolerance=5000
    \parindent=0pt\strut\raggedright #1\dotfill
    \dopageno{#2}}%
}

\def\dosubsubsecentry#1#2{%
   \vbox{\subsubsecentryfonts \leftskip=3\tocindent
    \hyphenpenalty=10000\tolerance=5000
    \parindent=0pt\strut\raggedright #1\dotfill
    \dopageno{#2}}%
}

% Space between chapter (or whatever) number and the title.
\def\labelspace{\hskip1em \relax}

\def\dopageno#1{{\rm #1}}
\def\doshortpageno#1{{\rm #1}}

\def\chapentryfonts{\secfonts \rm}
\def\secentryfonts{\textfonts}
\let\subsecentryfonts = \textfonts
\let\subsubsecentryfonts = \textfonts


\message{environments,}

% Since these characters are used in examples, it should be an even number of 
% \tt widths. Each \tt character is 1en, so two makes it 1em.
% Furthermore, these definitions must come after we define our fonts.
\newbox\dblarrowbox    \newbox\longdblarrowbox
\newbox\pushcharbox    \newbox\bullbox
\newbox\equivbox       \newbox\errorbox

\let\ptexequiv = \equiv

%{\tentt
%\global\setbox\dblarrowbox = \hbox to 1em{\hfil$\Rightarrow$\hfil}
%\global\setbox\longdblarrowbox = \hbox to 1em{\hfil$\mapsto$\hfil}
%\global\setbox\pushcharbox = \hbox to 1em{\hfil$\dashv$\hfil}
%\global\setbox\equivbox = \hbox to 1em{\hfil$\ptexequiv$\hfil}
% Adapted from the manmac format (p.420 of TeXbook)
%\global\setbox\bullbox = \hbox to 1em{\kern.15em\vrule height .75ex width .85ex
%                                      depth .1ex\hfil}
%}

\def\point{$\star$}

\def\result{\leavevmode\raise.15ex\hbox to 1em{\hfil$\Rightarrow$\hfil}}
\def\expansion{\leavevmode\raise.1ex\hbox to 1em{\hfil$\mapsto$\hfil}}
\def\print{\leavevmode\lower.1ex\hbox to 1em{\hfil$\dashv$\hfil}}

\def\equiv{\leavevmode\lower.1ex\hbox to 1em{\hfil$\ptexequiv$\hfil}}

% Adapted from the TeXbook's \boxit.
{\tentt \global\dimen0 = 3em}% Width of the box.
\dimen2 = .55pt % Thickness of rules
% The text. (`r' is open on the right, `e' somewhat less so on the left.)
\setbox0 = \hbox{\kern-.75pt \tensf error\kern-1.5pt}

\global\setbox\errorbox=\hbox to \dimen0{\hfil
   \hsize = \dimen0 \advance\hsize by -5.8pt % Space to left+right.
   \advance\hsize by -2\dimen2 % Rules.
   \vbox{
      \hrule height\dimen2
      \hbox{\vrule width\dimen2 \kern3pt          % Space to left of text.
         \vtop{\kern2.4pt \box0 \kern2.4pt}% Space above/below.
         \kern3pt\vrule width\dimen2}% Space to right.
      \hrule height\dimen2}
    \hfil}

% The @error{} command.
\def\error{\leavevmode\lower.7ex\copy\errorbox}

% @tex ... @end tex    escapes into raw Tex temporarily.
% One exception: @ is still an escape character, so that @end tex works.
% But \@ or @@ will get a plain tex @ character.

\def\tex{\begingroup
\catcode `\\=0 \catcode `\{=1 \catcode `\}=2
\catcode `\$=3 \catcode `\&=4 \catcode `\#=6
\catcode `\^=7 \catcode `\_=8 \catcode `\~=13 \let~=\tie
\catcode `\%=14
\catcode 43=12
\catcode`\"=12
\catcode`\==12
\catcode`\|=12
\catcode`\<=12
\catcode`\>=12
\escapechar=`\\
%
\let\{=\ptexlbrace
\let\}=\ptexrbrace
\let\.=\ptexdot
\let\*=\ptexstar
\let\dots=\ptexdots
\def\@{@}%
\let\bullet=\ptexbullet
\let\b=\ptexb \let\c=\ptexc \let\i=\ptexi \let\t=\ptext \let\l=\ptexl
\let\L=\ptexL
%
\let\Etex=\endgroup}

% Define @lisp ... @endlisp.
% @lisp does a \begingroup so it can rebind things,
% including the definition of @endlisp (which normally is erroneous).

% Amount to narrow the margins by for @lisp.
\newskip\lispnarrowing \lispnarrowing=0.4in

% This is the definition that ^M gets inside @lisp
% phr: changed space to \null, to avoid overfull hbox problems.
{\obeyspaces%
\gdef\lisppar{\null\endgraf}}

% Cause \obeyspaces to make each Space cause a word-separation
% rather than the default which is that it acts punctuation.
% This is because space in tt font looks funny.
{\obeyspaces %
\gdef\sepspaces{\def {\ }}}

\newskip\aboveenvskipamount \aboveenvskipamount= 0pt
\def\aboveenvbreak{{\advance\aboveenvskipamount by \parskip
\endgraf \ifdim\lastskip<\aboveenvskipamount
\removelastskip \penalty-50 \vskip\aboveenvskipamount \fi}}

\def\afterenvbreak{\endgraf \ifdim\lastskip<\aboveenvskipamount
\removelastskip \penalty-50 \vskip\aboveenvskipamount \fi}

% \nonarrowing is a flag.  If "set", @lisp etc don't narrow margins.
\let\nonarrowing=\relax

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% \cartouche: draw rectangle w/rounded corners around argument
\font\circle=lcircle10
\newdimen\circthick
\newdimen\cartouter\newdimen\cartinner
\newskip\normbskip\newskip\normpskip\newskip\normlskip
\circthick=\fontdimen8\circle
%
\def\ctl{{\circle\char'013\hskip -6pt}}% 6pt from pl file: 1/2charwidth
\def\ctr{{\hskip 6pt\circle\char'010}}
\def\cbl{{\circle\char'012\hskip -6pt}}
\def\cbr{{\hskip 6pt\circle\char'011}}
\def\carttop{\hbox to \cartouter{\hskip\lskip
	\ctl\leaders\hrule height\circthick\hfil\ctr
	\hskip\rskip}}
\def\cartbot{\hbox to \cartouter{\hskip\lskip
	\cbl\leaders\hrule height\circthick\hfil\cbr
	\hskip\rskip}}
%
\newskip\lskip\newskip\rskip

\long\def\cartouche{%
\begingroup
	\lskip=\leftskip \rskip=\rightskip
	\leftskip=0pt\rightskip=0pt %we want these *outside*.
	\cartinner=\hsize \advance\cartinner by-\lskip 
		 	  \advance\cartinner by-\rskip
	\cartouter=\hsize
	\advance\cartouter by 18pt % allow for 3pt kerns on either
%				     side, and for 6pt waste from
%				     each corner char
	\normbskip=\baselineskip \normpskip=\parskip \normlskip=\lineskip
	% Flag to tell @lisp, etc., not to narrow margin.
	\let\nonarrowing=\comment
	\vbox\bgroup
		\baselineskip=0pt\parskip=0pt\lineskip=0pt
		\carttop
		\hbox\bgroup
			\hskip\lskip
			\vrule\kern3pt
			\vbox\bgroup
				\hsize=\cartinner
				\kern3pt
				\begingroup
					\baselineskip=\normbskip
					\lineskip=\normlskip
					\parskip=\normpskip
					\vskip -\parskip
\def\Ecartouche{%
				\endgroup
				\kern3pt
			\egroup
			\kern3pt\vrule
			\hskip\rskip
		\egroup
		\cartbot
	\egroup
\endgroup
}}	

\def\lisp{\aboveenvbreak
\begingroup\inENV % This group ends at the end of the @lisp body
\hfuzz=12truept % Don't be fussy
% Make spaces be word-separators rather than space tokens.
\sepspaces %
% Single space lines
\singlespace %
% The following causes blank lines not to be ignored
% by adding a space to the end of each line.
\let\par=\lisppar
\def\Elisp{\endgroup\afterenvbreak}%
\parskip=0pt
% @cartouche defines \nonarrowing to inhibit narrowing
% at next level down.
\ifx\nonarrowing\relax
\advance \leftskip by \lispnarrowing
\exdentamount=\lispnarrowing
\let\exdent=\nofillexdent
\let\nonarrowing=\relax
\fi
\parindent=0pt
\obeyspaces \obeylines \tt \rawbackslash
\def\next##1{}\next}


\let\example=\lisp
\def\Eexample{\Elisp}

\let\smallexample=\lisp
\def\Esmallexample{\Elisp}

% Macro for 9 pt. examples, necessary to print with 5" lines.
% From Pavel@xerox.  This is not really used unless the
% @smallbook command is given.

\def\smalllispx{\aboveenvbreak\begingroup\inENV
%			This group ends at the end of the @lisp body
\hfuzz=12truept % Don't be fussy
% Make spaces be word-separators rather than space tokens.
\sepspaces %
% Single space lines
\singlespace %
% The following causes blank lines not to be ignored
% by adding a space to the end of each line.
\let\par=\lisppar
\def\Esmalllisp{\endgroup\afterenvbreak}%
%%%% Smaller baseline skip for small examples.
\baselineskip 10pt
\parskip=0pt
% @cartouche defines \nonarrowing to inhibit narrowing
% at next level down.
\ifx\nonarrowing\relax
\advance \leftskip by \lispnarrowing
\exdentamount=\lispnarrowing
\let\exdent=\nofillexdent
\let\nonarrowing=\relax
\fi
\parindent=0pt
\obeyspaces \obeylines \ninett \indexfonts \rawbackslash
\def\next##1{}\next}

% This is @display; same as @lisp except use roman font.

\def\display{\begingroup\inENV %This group ends at the end of the @display body
\aboveenvbreak
% Make spaces be word-separators rather than space tokens.
\sepspaces %
% Single space lines
\singlespace %
% The following causes blank lines not to be ignored
% by adding a space to the end of each line.
\let\par=\lisppar
\def\Edisplay{\endgroup\afterenvbreak}%
\parskip=0pt
% @cartouche defines \nonarrowing to inhibit narrowing
% at next level down.
\ifx\nonarrowing\relax
\advance \leftskip by \lispnarrowing
\exdentamount=\lispnarrowing
\let\exdent=\nofillexdent
\let\nonarrowing=\relax
\fi
\parindent=0pt
\obeyspaces \obeylines
\def\next##1{}\next}

% This is @format; same as @lisp except use roman font and don't narrow margins

\def\format{\begingroup\inENV %This group ends at the end of the @format body
\aboveenvbreak
% Make spaces be word-separators rather than space tokens.
\sepspaces %
\singlespace %
% The following causes blank lines not to be ignored
% by adding a space to the end of each line.
\let\par=\lisppar
\def\Eformat{\endgroup\afterenvbreak}
\parskip=0pt \parindent=0pt
\obeyspaces \obeylines
\def\next##1{}\next}

% @flushleft and @flushright

\def\flushleft{%
\begingroup\inENV %This group ends at the end of the @format body
\aboveenvbreak
% Make spaces be word-separators rather than space tokens.
\sepspaces %
% The following causes blank lines not to be ignored
% by adding a space to the end of each line.
% This also causes @ to work when the directive name
% is terminated by end of line.
\let\par=\lisppar
\def\Eflushleft{\endgroup\afterenvbreak}%
\parskip=0pt \parindent=0pt
\obeyspaces \obeylines
\def\next##1{}\next}

\def\flushright{%
\begingroup\inENV %This group ends at the end of the @format body
\aboveenvbreak
% Make spaces be word-separators rather than space tokens.
\sepspaces %
% The following causes blank lines not to be ignored
% by adding a space to the end of each line.
% This also causes @ to work when the directive name
% is terminated by end of line.
\let\par=\lisppar
\def\Eflushright{\endgroup\afterenvbreak}%
\parskip=0pt \parindent=0pt
\advance \leftskip by 0pt plus 1fill
\obeyspaces \obeylines
\def\next##1{}\next}

% @quotation - narrow the margins.

\def\quotation{%
\begingroup\inENV %This group ends at the end of the @quotation body
{\parskip=0pt  % because we will skip by \parskip too, later
\aboveenvbreak}%
\singlespace
\parindent=0pt
\def\Equotation{\par\endgroup\afterenvbreak}%
% @cartouche defines \nonarrowing to inhibit narrowing
% at next level down.
\ifx\nonarrowing\relax
\advance \leftskip by \lispnarrowing
\advance \rightskip by \lispnarrowing
\exdentamount=\lispnarrowing
\let\nonarrowing=\relax
\fi}

\message{defuns,}
% Define formatter for defuns
% First, allow user to change definition object font (\df) internally
\def\setdeffont #1 {\csname DEF#1\endcsname}

\newskip\defbodyindent \defbodyindent=.4in
\newskip\defargsindent \defargsindent=50pt
\newskip\deftypemargin \deftypemargin=12pt
\newskip\deflastargmargin \deflastargmargin=18pt

\newcount\parencount
% define \functionparens, which makes ( and ) and & do special things.
% \functionparens affects the group it is contained in.
\def\activeparens{%
\catcode`\(=\active \catcode`\)=\active \catcode`\&=\active
\catcode`\[=\active \catcode`\]=\active}
{\activeparens % Now, smart parens don't turn on until &foo (see \amprm)
\gdef\functionparens{\boldbrax\let&=\amprm\parencount=0 }
\gdef\boldbrax{\let(=\opnr\let)=\clnr\let[=\lbrb\let]=\rbrb}

% Definitions of (, ) and & used in args for functions.
% This is the definition of ( outside of all parentheses.
\gdef\oprm#1 {{\rm\char`\(}#1 \bf \let(=\opnested %
\global\advance\parencount by 1 }
%
% This is the definition of ( when already inside a level of parens.
\gdef\opnested{\char`\(\global\advance\parencount by 1 }
%
\gdef\clrm{% Print a paren in roman if it is taking us back to depth of 0.
% also in that case restore the outer-level definition of (.
\ifnum \parencount=1 {\rm \char `\)}\sl \let(=\oprm \else \char `\) \fi
\global\advance \parencount by -1 }
% If we encounter &foo, then turn on ()-hacking afterwards
\gdef\amprm#1 {{\rm\&#1}\let(=\oprm \let)=\clrm\ }
%
\gdef\normalparens{\boldbrax\let&=\ampnr}
} % End of definition inside \activeparens
%% These parens (in \boldbrax) actually are a little bolder than the
%% contained text.  This is especially needed for [ and ]
\def\opnr{{\sf\char`\(}} \def\clnr{{\sf\char`\)}} \def\ampnr{\&}
\def\lbrb{{\bf\char`\[}} \def\rbrb{{\bf\char`\]}}

% First, defname, which formats the header line itself.
% #1 should be the function name.
% #2 should be the type of definition, such as "Function".

\def\defname #1#2{%
% Get the values of \leftskip and \rightskip as they were
% outside the @def...
\dimen2=\leftskip
\advance\dimen2 by -\defbodyindent
\dimen3=\rightskip
\advance\dimen3 by -\defbodyindent
\noindent        %
\setbox0=\hbox{\hskip \deflastargmargin{\rm #2}\hskip \deftypemargin}%
\dimen0=\hsize \advance \dimen0 by -\wd0 % compute size for first line
\dimen1=\hsize \advance \dimen1 by -\defargsindent %size for continuations
\parshape 2 0in \dimen0 \defargsindent \dimen1     %
% Now output arg 2 ("Function" or some such)
% ending at \deftypemargin from the right margin,
% but stuck inside a box of width 0 so it does not interfere with linebreaking
{% Adjust \hsize to exclude the ambient margins,
% so that \rightline will obey them.
\advance \hsize by -\dimen2 \advance \hsize by -\dimen3
\rlap{\rightline{{\rm #2}\hskip \deftypemargin}}}%
% Make all lines underfull and no complaints:
\tolerance=10000 \hbadness=10000    
\advance\leftskip by -\defbodyindent
\exdentamount=\defbodyindent
{\df #1}\enskip        % Generate function name
}

% Actually process the body of a definition
% #1 should be the terminating control sequence, such as \Edefun.
% #2 should be the "another name" control sequence, such as \defunx.
% #3 should be the control sequence that actually processes the header,
%    such as \defunheader.

\def\defparsebody #1#2#3{\begingroup\inENV% Environment for definitionbody
\medbreak %
% Define the end token that this defining construct specifies
% so that it will exit this group.
\def#1{\endgraf\endgroup\medbreak}%
\def#2{\begingroup\obeylines\activeparens\spacesplit#3}%
\parindent=0in
\advance\leftskip by \defbodyindent \advance \rightskip by \defbodyindent
\exdentamount=\defbodyindent
\begingroup %
\catcode 61=\active %
\obeylines\activeparens\spacesplit#3}

\def\defmethparsebody #1#2#3#4 {\begingroup\inENV %
\medbreak %
% Define the end token that this defining construct specifies
% so that it will exit this group.
\def#1{\endgraf\endgroup\medbreak}%
\def#2##1 {\begingroup\obeylines\activeparens\spacesplit{#3{##1}}}%
\parindent=0in
\advance\leftskip by \defbodyindent \advance \rightskip by \defbodyindent
\exdentamount=\defbodyindent
\begingroup\obeylines\activeparens\spacesplit{#3{#4}}}

\def\defopparsebody #1#2#3#4#5 {\begingroup\inENV %
\medbreak %
% Define the end token that this defining construct specifies
% so that it will exit this group.
\def#1{\endgraf\endgroup\medbreak}%
\def#2##1 ##2 {\def#4{##1}%
\begingroup\obeylines\activeparens\spacesplit{#3{##2}}}%
\parindent=0in
\advance\leftskip by \defbodyindent \advance \rightskip by \defbodyindent
\exdentamount=\defbodyindent
\begingroup\obeylines\activeparens\spacesplit{#3{#5}}}

% These parsing functions are similar to the preceding ones
% except that they do not make parens into active characters.
% These are used for "variables" since they have no arguments.

\def\defvarparsebody #1#2#3{\begingroup\inENV% Environment for definitionbody
\medbreak %
% Define the end token that this defining construct specifies
% so that it will exit this group.
\def#1{\endgraf\endgroup\medbreak}%
\def#2{\begingroup\obeylines\spacesplit#3}%
\parindent=0in
\advance\leftskip by \defbodyindent \advance \rightskip by \defbodyindent
\exdentamount=\defbodyindent
\begingroup %
\catcode 61=\active %
\obeylines\spacesplit#3}

\def\defvrparsebody #1#2#3#4 {\begingroup\inENV %
\medbreak %
% Define the end token that this defining construct specifies
% so that it will exit this group.
\def#1{\endgraf\endgroup\medbreak}%
\def#2##1 {\begingroup\obeylines\spacesplit{#3{##1}}}%
\parindent=0in
\advance\leftskip by \defbodyindent \advance \rightskip by \defbodyindent
\exdentamount=\defbodyindent
\begingroup\obeylines\spacesplit{#3{#4}}}

\def\defopvarparsebody #1#2#3#4#5 {\begingroup\inENV %
\medbreak %
% Define the end token that this defining construct specifies
% so that it will exit this group.
\def#1{\endgraf\endgroup\medbreak}%
\def#2##1 ##2 {\def#4{##1}%
\begingroup\obeylines\spacesplit{#3{##2}}}%
\parindent=0in
\advance\leftskip by \defbodyindent \advance \rightskip by \defbodyindent
\exdentamount=\defbodyindent
\begingroup\obeylines\spacesplit{#3{#5}}}

% Split up #2 at the first space token.
% call #1 with two arguments:
%  the first is all of #2 before the space token,
%  the second is all of #2 after that space token.
% If #2 contains no space token, all of it is passed as the first arg
% and the second is passed as empty.

{\obeylines
\gdef\spacesplit#1#2^^M{\endgroup\spacesplitfoo{#1}#2 \relax\spacesplitfoo}%
\long\gdef\spacesplitfoo#1#2 #3#4\spacesplitfoo{%
\ifx\relax #3%
#1{#2}{}\else #1{#2}{#3#4}\fi}}

% So much for the things common to all kinds of definitions.

% Define @defun.

% First, define the processing that is wanted for arguments of \defun
% Use this to expand the args and terminate the paragraph they make up

\def\defunargs #1{\functionparens \sl
% Expand, preventing hyphenation at `-' chars.
% Note that groups don't affect changes in \hyphenchar.
\hyphenchar\tensl=0
#1%
\hyphenchar\tensl=45
\ifnum\parencount=0 \else \errmessage{unbalanced parens in @def arguments}\fi%
\interlinepenalty=10000
\advance\rightskip by 0pt plus 1fil
\endgraf\penalty 10000\vskip -\parskip\penalty 10000%
}

\def\deftypefunargs #1{%
% Expand, preventing hyphenation at `-' chars.
% Note that groups don't affect changes in \hyphenchar.
\functionparens
\code{#1}%
\interlinepenalty=10000
\advance\rightskip by 0pt plus 1fil
\endgraf\penalty 10000\vskip -\parskip\penalty 10000%
}

% Do complete processing of one @defun or @defunx line already parsed.

% @deffn Command forward-char nchars

\def\deffn{\defmethparsebody\Edeffn\deffnx\deffnheader}

\def\deffnheader #1#2#3{\doind {fn}{\code{#2}}%
\begingroup\defname {#2}{#1}\defunargs{#3}\endgroup %
\catcode 61=\other % Turn off change made in \defparsebody
}

% @defun == @deffn Function

\def\defun{\defparsebody\Edefun\defunx\defunheader}

\def\defunheader #1#2{\doind {fn}{\code{#1}}% Make entry in function index
\begingroup\defname {#1}{Function}%
\defunargs {#2}\endgroup %
\catcode 61=\other % Turn off change made in \defparsebody
}

% @deftypefun int foobar (int @var{foo}, float @var{bar})

\def\deftypefun{\defparsebody\Edeftypefun\deftypefunx\deftypefunheader}

% #1 is the data type.  #2 is the name and args.
\def\deftypefunheader #1#2{\deftypefunheaderx{#1}#2 \relax}
% #1 is the data type, #2 the name, #3 the args.
\def\deftypefunheaderx #1#2 #3\relax{%
\doind {fn}{\code{#2}}% Make entry in function index
\begingroup\defname {\code{#1} #2}{Function}%
\deftypefunargs {#3}\endgroup %
\catcode 61=\other % Turn off change made in \defparsebody
}

% @deftypefn {Library Function} int foobar (int @var{foo}, float @var{bar})

\def\deftypefn{\defmethparsebody\Edeftypefn\deftypefnx\deftypefnheader}

% #1 is the classification.  #2 is the data type.  #3 is the name and args.
\def\deftypefnheader #1#2#3{\deftypefnheaderx{#1}{#2}#3 \relax}
% #1 is the classification, #2 the data type, #3 the name, #4 the args.
\def\deftypefnheaderx #1#2#3 #4\relax{%
\doind {fn}{\code{#3}}% Make entry in function index
\begingroup\defname {\code{#2} #3}{#1}%
\deftypefunargs {#4}\endgroup %
\catcode 61=\other % Turn off change made in \defparsebody
}

% @defmac == @deffn Macro

\def\defmac{\defparsebody\Edefmac\defmacx\defmacheader}

\def\defmacheader #1#2{\doind {fn}{\code{#1}}% Make entry in function index
\begingroup\defname {#1}{Macro}%
\defunargs {#2}\endgroup %
\catcode 61=\other % Turn off change made in \defparsebody
}

% @defspec == @deffn Special Form

\def\defspec{\defparsebody\Edefspec\defspecx\defspecheader}

\def\defspecheader #1#2{\doind {fn}{\code{#1}}% Make entry in function index
\begingroup\defname {#1}{Special Form}%
\defunargs {#2}\endgroup %
\catcode 61=\other % Turn off change made in \defparsebody
}

% This definition is run if you use @defunx
% anywhere other than immediately after a @defun or @defunx.

\def\deffnx #1 {\errmessage{@deffnx in invalid context}}
\def\defunx #1 {\errmessage{@defunx in invalid context}}
\def\defmacx #1 {\errmessage{@defmacx in invalid context}}
\def\defspecx #1 {\errmessage{@defspecx in invalid context}}
\def\deftypefnx #1 {\errmessage{@deftypefnx in invalid context}}
\def\deftypeunx #1 {\errmessage{@deftypeunx in invalid context}}

% @defmethod, and so on

% @defop {Funny Method} foo-class frobnicate argument

\def\defop #1 {\def\defoptype{#1}%
\defopparsebody\Edefop\defopx\defopheader\defoptype}

\def\defopheader #1#2#3{%
\dosubind {fn}{\code{#2}}{on #1}% Make entry in function index
\begingroup\defname {#2}{\defoptype{} on #1}%
\defunargs {#3}\endgroup %
}

% @defmethod == @defop Method

\def\defmethod{\defmethparsebody\Edefmethod\defmethodx\defmethodheader}

\def\defmethodheader #1#2#3{%
\dosubind {fn}{\code{#2}}{on #1}% entry in function index
\begingroup\defname {#2}{Method on #1}%
\defunargs {#3}\endgroup %
}

% @defcv {Class Option} foo-class foo-flag

\def\defcv #1 {\def\defcvtype{#1}%
\defopvarparsebody\Edefcv\defcvx\defcvarheader\defcvtype}

\def\defcvarheader #1#2#3{%
\dosubind {vr}{\code{#2}}{of #1}% Make entry in var index
\begingroup\defname {#2}{\defcvtype{} of #1}%
\defvarargs {#3}\endgroup %
}

% @defivar == @defcv {Instance Variable}

\def\defivar{\defvrparsebody\Edefivar\defivarx\defivarheader}

\def\defivarheader #1#2#3{%
\dosubind {vr}{\code{#2}}{of #1}% Make entry in var index
\begingroup\defname {#2}{Instance Variable of #1}%
\defvarargs {#3}\endgroup %
}

% These definitions are run if you use @defmethodx, etc.,
% anywhere other than immediately after a @defmethod, etc.

\def\defopx #1 {\errmessage{@defopx in invalid context}}
\def\defmethodx #1 {\errmessage{@defmethodx in invalid context}}
\def\defcvx #1 {\errmessage{@defcvx in invalid context}}
\def\defivarx #1 {\errmessage{@defivarx in invalid context}}

% Now @defvar

% First, define the processing that is wanted for arguments of @defvar.
% This is actually simple: just print them in roman.
% This must expand the args and terminate the paragraph they make up
\def\defvarargs #1{\normalparens #1%
\interlinepenalty=10000
\endgraf\penalty 10000\vskip -\parskip\penalty 10000}

% @defvr Counter foo-count

\def\defvr{\defvrparsebody\Edefvr\defvrx\defvrheader}

\def\defvrheader #1#2#3{\doind {vr}{\code{#2}}%
\begingroup\defname {#2}{#1}\defvarargs{#3}\endgroup}

% @defvar == @defvr Variable

\def\defvar{\defvarparsebody\Edefvar\defvarx\defvarheader}

\def\defvarheader #1#2{\doind {vr}{\code{#1}}% Make entry in var index
\begingroup\defname {#1}{Variable}%
\defvarargs {#2}\endgroup %
}

% @defopt == @defvr {User Option}

\def\defopt{\defvarparsebody\Edefopt\defoptx\defoptheader}

\def\defoptheader #1#2{\doind {vr}{\code{#1}}% Make entry in var index
\begingroup\defname {#1}{User Option}%
\defvarargs {#2}\endgroup %
}

% @deftypevar int foobar

\def\deftypevar{\defvarparsebody\Edeftypevar\deftypevarx\deftypevarheader}

% #1 is the data type.  #2 is the name.
\def\deftypevarheader #1#2{%
\doind {vr}{\code{#2}}% Make entry in variables index
\begingroup\defname {\code{#1} #2}{Variable}%
\interlinepenalty=10000
\endgraf\penalty 10000\vskip -\parskip\penalty 10000
\endgroup}

% @deftypevr {Global Flag} int enable

\def\deftypevr{\defvrparsebody\Edeftypevr\deftypevrx\deftypevrheader}

\def\deftypevrheader #1#2#3{\doind {vr}{\code{#3}}%
\begingroup\defname {\code{#2} #3}{#1}
\interlinepenalty=10000
\endgraf\penalty 10000\vskip -\parskip\penalty 10000
\endgroup}

% This definition is run if you use @defvarx
% anywhere other than immediately after a @defvar or @defvarx.

\def\defvrx #1 {\errmessage{@defvrx in invalid context}}
\def\defvarx #1 {\errmessage{@defvarx in invalid context}}
\def\defoptx #1 {\errmessage{@defoptx in invalid context}}
\def\deftypevarx #1 {\errmessage{@deftypevarx in invalid context}}
\def\deftypevrx #1 {\errmessage{@deftypevrx in invalid context}}

% Now define @deftp
% Args are printed in bold, a slight difference from @defvar.

\def\deftpargs #1{\bf \defvarargs{#1}}

% @deftp Class window height width ...

\def\deftp{\defvrparsebody\Edeftp\deftpx\deftpheader}

\def\deftpheader #1#2#3{\doind {tp}{\code{#2}}%
\begingroup\defname {#2}{#1}\deftpargs{#3}\endgroup}

% This definition is run if you use @deftpx, etc
% anywhere other than immediately after a @deftp, etc.

\def\deftpx #1 {\errmessage{@deftpx in invalid context}}

\message{cross reference,}
% Define cross-reference macros
\newwrite \auxfile

\newif\ifhavexrefs  % True if xref values are known.
\newif\ifwarnedxrefs  % True if we warned once that they aren't known.

% \setref{foo} defines a cross-reference point named foo.

\def\setref#1{%
%\dosetq{#1-title}{Ytitle}%
\dosetq{#1-pg}{Ypagenumber}%
\dosetq{#1-snt}{Ysectionnumberandtype}}

\def\unnumbsetref#1{%
%\dosetq{#1-title}{Ytitle}%
\dosetq{#1-pg}{Ypagenumber}%
\dosetq{#1-snt}{Ynothing}}

\def\appendixsetref#1{%
%\dosetq{#1-title}{Ytitle}%
\dosetq{#1-pg}{Ypagenumber}%
\dosetq{#1-snt}{Yappendixletterandtype}}

% \xref, \pxref, and \ref generate cross-references to specified points.
% For \xrefX, #1 is the node name, #2 the name of the Info
% cross-reference, #3 the printed node name, #4 the name of the Info
% file, #5 the name of the printed manual.  All but the node name can be
% omitted.
% 
\def\pxref#1{see \xrefX[#1,,,,,,,]}
\def\xref#1{See \xrefX[#1,,,,,,,]}
\def\ref#1{\xrefX[#1,,,,,,,]}
\def\xrefX[#1,#2,#3,#4,#5,#6]{\begingroup%
\def\printedmanual{\ignorespaces #5}%
\def\printednodename{\ignorespaces #3}%
%
\setbox1=\hbox{\printedmanual}%
\setbox0=\hbox{\printednodename}%
\ifdim \wd0=0pt%
\def\printednodename{\ignorespaces #1}%
%%% Uncommment the following line to make the actual chapter or section title
%%% appear inside the square brackets.
%\def\printednodename{#1-title}%
\fi%
%
%
% If we use \unhbox0 and \unhbox1 to print the node names, TeX does
% not insert empty discretionaries after hyphens, which means that it
% will not find a line break at a hyphen in a node names.  Since some
% manuals are best written with fairly long node names, containing
% hyphens, this is a loss.  Therefore, we simply give the text of
% the node name again, so it is as if TeX is seeing it for the first
% time.
\ifdim \wd1>0pt
section ``\printednodename'' in \cite{\printedmanual}%
\else%
\turnoffactive%
\refx{#1-snt}{} [\printednodename], page\tie\refx{#1-pg}{}%
\fi
\endgroup}

% \dosetq is the interface for calls from other macros

% Use \turnoffactive so that punctuation chars such as underscore
% work in node names.
\def\dosetq #1#2{{\let\folio=0 \turnoffactive%
\edef\next{\write\auxfile{\internalsetq {#1}{#2}}}%
\next}}

% \internalsetq {foo}{page} expands into
% CHARACTERS 'xrdef {foo}{...expansion of \Ypage...}
% When the aux file is read, ' is the escape character

\def\internalsetq #1#2{'xrdef {#1}{\csname #2\endcsname}}

% Things to be expanded by \internalsetq

\def\Ypagenumber{\folio}

\def\Ytitle{\thischapter}

\def\Ynothing{}

\def\Ysectionnumberandtype{%
\ifnum\secno=0 Chapter\xreftie\the\chapno %
\else \ifnum \subsecno=0 Section\xreftie\the\chapno.\the\secno %
\else \ifnum \subsubsecno=0 %
Section\xreftie\the\chapno.\the\secno.\the\subsecno %
\else %
Section\xreftie\the\chapno.\the\secno.\the\subsecno.\the\subsubsecno %
\fi \fi \fi }

\def\Yappendixletterandtype{%
\ifnum\secno=0 Appendix\xreftie'char\the\appendixno{}%
\else \ifnum \subsecno=0 Section\xreftie'char\the\appendixno.\the\secno %
\else \ifnum \subsubsecno=0 %
Section\xreftie'char\the\appendixno.\the\secno.\the\subsecno %
\else %
Section\xreftie'char\the\appendixno.\the\secno.\the\subsecno.\the\subsubsecno %
\fi \fi \fi }

\gdef\xreftie{'tie}

% Use TeX 3.0's \inputlineno to get the line number, for better error
% messages, but if we're using an old version of TeX, don't do anything.
% 
\ifx\inputlineno\thisisundefined
  \let\linenumber = \empty % Non-3.0.
\else
  \def\linenumber{\the\inputlineno:\space}
\fi

% Define \refx{NAME}{SUFFIX} to reference a cross-reference string named NAME.
% If its value is nonempty, SUFFIX is output afterward.

\def\refx#1#2{%
  \expandafter\ifx\csname X#1\endcsname\relax
    % If not defined, say something at least.
    $\langle$un\-de\-fined$\rangle$%
    \ifhavexrefs
      \message{\linenumber Undefined cross reference `#1'.}%
    \else
      \ifwarnedxrefs\else
        \global\warnedxrefstrue
        \message{Cross reference values unknown; you must run TeX again.}%
      \fi
    \fi
  \else
    % It's defined, so just use it.
    \csname X#1\endcsname
  \fi
  #2% Output the suffix in any case.
}

% Read the last existing aux file, if any.  No error if none exists.

% This is the macro invoked by entries in the aux file.
\def\xrdef #1#2{
{\catcode`\'=\other\expandafter \gdef \csname X#1\endcsname {#2}}}

\def\readauxfile{%
\begingroup
\catcode `\^^@=\other
\catcode `\=\other
\catcode `\=\other
\catcode `\^^C=\other
\catcode `\^^D=\other
\catcode `\^^E=\other
\catcode `\^^F=\other
\catcode `\^^G=\other
\catcode `\^^H=\other
\catcode `\=\other
\catcode `\^^L=\other
\catcode `\=\other
\catcode `\=\other
\catcode `\=\other
\catcode `\=\other
\catcode `\=\other
\catcode `\=\other
\catcode `\=\other
\catcode `\=\other
\catcode `\=\other
\catcode `\=\other
\catcode `\=\other
\catcode `\=\other
\catcode 26=\other
\catcode `\^^[=\other
\catcode `\^^\=\other
\catcode `\^^]=\other
\catcode `\^^^=\other
\catcode `\^^_=\other
\catcode `\@=\other
\catcode `\^=\other
\catcode `\~=\other
\catcode `\[=\other
\catcode `\]=\other
\catcode`\"=\other
\catcode`\_=\other
\catcode`\|=\other
\catcode`\<=\other
\catcode`\>=\other
\catcode `\$=\other
\catcode `\#=\other
\catcode `\&=\other
% the aux file uses ' as the escape.
% Turn off \ as an escape so we do not lose on
% entries which were dumped with control sequences in their names.
% For example, 'xrdef {$\leq $-fun}{page ...} made by @defun ^^
% Reference to such entries still does not work the way one would wish,
% but at least they do not bomb out when the aux file is read in.
\catcode `\{=1 \catcode `\}=2
\catcode `\%=\other
\catcode `\'=0
\catcode `\\=\other
\openin 1 \jobname.aux
\ifeof 1 \else \closein 1 \input \jobname.aux \global\havexrefstrue
\fi
% Open the new aux file.  Tex will close it automatically at exit.
\openout \auxfile=\jobname.aux
\endgroup}


% Footnotes.

\newcount \footnoteno

% The trailing space in the following definition for supereject is
% vital for proper filling; pages come out unaligned when you do a
% pagealignmacro call if that space before the closing brace is
% removed.
\def\supereject{\par\penalty -20000\footnoteno =0 }

% @footnotestyle is meaningful for info output only..
\let\footnotestyle=\comment

\let\ptexfootnote=\footnote

{\catcode `\@=11
\long\gdef\footnote #1{\global\advance \footnoteno by \@ne
\unskip
\edef\thisfootno{$^{\the\footnoteno}$}%
\let\@sf\empty
\ifhmode\edef\@sf{\spacefactor\the\spacefactor}\/\fi
\thisfootno\@sf \footnotezzz{#1}}
% \parsearg\footnotezzz}

\long\gdef\footnotezzz #1{\insert\footins{
\interlinepenalty\interfootnotelinepenalty
\splittopskip\ht\strutbox % top baseline for broken footnotes
\splitmaxdepth\dp\strutbox \floatingpenalty\@MM
\leftskip\z@skip \rightskip\z@skip \spaceskip\z@skip \xspaceskip\z@skip
\footstrut\parindent=\defaultparindent\hang\textindent{\thisfootno}#1\strut}}

}%end \catcode `\@=11

% End of control word definitions.

\message{and turning on texinfo input format.}

\def\openindices{%
   \newindex{cp}%
   \newcodeindex{fn}%
   \newcodeindex{vr}%
   \newcodeindex{tp}%
   \newcodeindex{ky}%
   \newcodeindex{pg}%
}

% Set some numeric style parameters, for 8.5 x 11 format.

%\hsize = 6.5in
\newdimen\defaultparindent \defaultparindent = 15pt
\parindent = \defaultparindent
\parskip 18pt plus 1pt
\baselineskip 15pt
\advance\topskip by 1.2cm

% Prevent underfull vbox error messages.
\vbadness=10000

% Following George Bush, just get rid of widows and orphans.
\widowpenalty=10000
\clubpenalty=10000

% Use TeX 3.0's \emergencystretch to help line breaking, but if we're
% using an old version of TeX, don't do anything.  We want the amount of
% stretch added to depend on the line length, hence the dependence on
% \hsize.  This makes it come to about 9pt for the 8.5x11 format.
% 
\ifx\emergencystretch\thisisundefined \else
  \emergencystretch = \hsize
  \divide\emergencystretch by 45
\fi

% Use @smallbook to reset parameters for 7x9.5 format  (or else 7x9.25)
\def\smallbook{
\global\lispnarrowing = 0.3in
\global\baselineskip 12pt
\advance\topskip by -1cm
\global\parskip 3pt plus 1pt
\global\hsize = 5in
\global\doublecolumnhsize=2.4in \global\doublecolumnvsize=15.0in
\global\vsize=7.5in
\global\tolerance=700
\global\hfuzz=1pt
\global\contentsrightmargin=0pt

\global\pagewidth=\hsize
\global\pageheight=\vsize

\global\let\smalllisp=\smalllispx
\global\let\smallexample=\smalllispx
\global\def\Esmallexample{\Esmalllisp}
}

% Use @afourpaper to print on European A4 paper.
\def\afourpaper{
\global\tolerance=700
\global\hfuzz=1pt
\global\baselineskip=12pt
\global\parskip 15pt plus 1pt

\global\vsize= 53\baselineskip
\advance\vsize by \topskip
%\global\hsize=   5.85in     % A4 wide 10pt
\global\hsize=  6.5in
\global\outerhsize=\hsize
\global\advance\outerhsize by 0.5in
\global\outervsize=\vsize
\global\advance\outervsize by 0.6in
\global\doublecolumnhsize=\hsize
\global\divide\doublecolumnhsize by 2
\global\advance\doublecolumnhsize by -0.1in
\global\doublecolumnvsize=\vsize
\global\multiply\doublecolumnvsize by 2
\global\advance\doublecolumnvsize by 0.1in

\global\pagewidth=\hsize
\global\pageheight=\vsize
}

%% For a final copy, take out the rectangles
%% that mark overfull boxes (in case you have decided
%% that the text looks ok even though it passes the margin).
\def\finalout{\overfullrule=0pt}

% Define macros to output various characters with catcode for normal text.
\catcode`\"=\other
\catcode`\~=\other
\catcode`\^=\other
\catcode`\_=\other
\catcode`\|=\other
\catcode`\<=\other
\catcode`\>=\other
\catcode`\+=\other
\def\normaldoublequote{"}
\def\normaltilde{~}
\def\normalcaret{^}
\def\normalunderscore{_}
\def\normalverticalbar{|}
\def\normalless{<}
\def\normalgreater{>}
\def\normalplus{+}

% This macro is used to make a character print one way in ttfont
% where it can probably just be output, and another way in other fonts,
% where something hairier probably needs to be done.
%
% #1 is what to print if we are indeed using \tt; #2 is what to print
% otherwise.  Since all the Computer Modern typewriter fonts have zero
% interword stretch (and shrink), and it is reasonable to expect all
% typewriter fonts to have this, we can check that font parameter.
% 
\def\ifusingtt#1#2{\ifdim \fontdimen3\the\font=0pt #1\else #2\fi}

% Turn off all special characters except @
% (and those which the user can use as if they were ordinary).
% Most of these we simply print from the \tt font, but for some, we can
% use math or other variants that look better in normal text.

\catcode`\"=\active
\def\activedoublequote{{\tt \char '042}}
\let"=\activedoublequote
\catcode`\~=\active
\def~{{\tt \char '176}}
\chardef\hat=`\^
\catcode`\^=\active
\def^{{\tt \hat}}

\catcode`\_=\active
\def_{\ifusingtt\normalunderscore\_}
% Subroutine for the previous macro.
\def\_{\lvvmode \kern.06em \vbox{\hrule width.3em height.1ex}}

% \lvvmode is equivalent in function to \leavevmode.
% Using \leavevmode runs into trouble when written out to
% an index file due to the expansion of \leavevmode into ``\unhbox
% \voidb@x'' ---which looks to TeX like ``\unhbox \voidb\x'' due to our
% magic tricks with @.
\def\lvvmode{\vbox to 0pt{}}

\catcode`\|=\active
\def|{{\tt \char '174}}
\chardef \less=`\<
\catcode`\<=\active
\def<{{\tt \less}}
\chardef \gtr=`\>
\catcode`\>=\active
\def>{{\tt \gtr}}
\catcode`\+=\active
\def+{{\tt \char 43}}
%\catcode 27=\active
%\def^^[{$\diamondsuit$}

% Used sometimes to turn off (effectively) the active characters
% even after parsing them.
\def\turnoffactive{\let"=\normaldoublequote
\let~=\normaltilde
\let^=\normalcaret
\let_=\normalunderscore
\let|=\normalverticalbar
\let<=\normalless
\let>=\normalgreater
\let+=\normalplus}

% Set up an active definition for =, but don't enable it most of the time.
{\catcode`\==\active
\global\def={{\tt \char 61}}}

\catcode`\@=0

% \rawbackslashxx output one backslash character in current font
\global\chardef\rawbackslashxx=`\\
%{\catcode`\\=\other
%@gdef@rawbackslashxx{\}}

% \rawbackslash redefines \ as input to do \rawbackslashxx.
{\catcode`\\=\active
@gdef@rawbackslash{@let\=@rawbackslashxx }}

% \normalbackslash outputs one backslash in fixed width font.
\def\normalbackslash{{\tt\rawbackslashxx}}

% Say @foo, not \foo, in error messages.
\escapechar=`\@

% \catcode 17=0   % Define control-q
\catcode`\\=\active

% If a .fmt file is being used, we don't want the `\input texinfo' to show up.
% That is what \eatinput is for; after that, the `\' should revert to printing 
% a backslash.
%
@gdef@eatinput input texinfo{@fixbackslash}
@global@let\ = @eatinput

% On the other hand, perhaps the file did not have a `\input texinfo'. Then
% the first `\{ in the file would cause an error. This macro tries to fix 
% that, assuming it is called before the first `\' could plausibly occur.
% 
@gdef@fixbackslash{@ifx\@eatinput @let\ = @normalbackslash @fi}

%% These look ok in all fonts, so just make them not special.  The @rm below
%% makes sure that the current font starts out as the newly loaded cmr10
@catcode`@$=@other @catcode`@%=@other @catcode`@&=@other @catcode`@#=@other

@textfonts
@rm

@c Local variables:
@c page-delimiter: "^\\\\message"
@c End:
