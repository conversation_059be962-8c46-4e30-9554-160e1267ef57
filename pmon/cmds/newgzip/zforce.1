.TH ZFORCE 1
.SH NAME
zforce \- force a '.gz' extension on all gzip files
.SH SYNOPSIS
.B zforce
[ name ...  ]
.SH DESCRIPTION
.I  zforce
forces a .gz extension on all
.I gzip
files so that
.I gzip
will not compress them twice.
This can be useful for files with names truncated after a file transfer.
On systems with a 14 char limitation on file names, the original name
is truncated to make room for the .gz suffix. For example,
12345678901234 is renamed to 12345678901.gz. A file name such as foo.tgz
is left intact.
.SH "SEE ALSO"
gzip(1), znew(1), zmore(1), zgrep(1), zdiff(1), gzexe(1)
