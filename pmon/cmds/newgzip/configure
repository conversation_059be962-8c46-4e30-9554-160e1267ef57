#!/bin/sh
# Guess values for system-dependent variables and create Makefiles.
# Generated automatically using autoconf.
# Copyright (C) 1991, 1992, 1993 Free Software Foundation, Inc.

# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 2, or (at your option)
# any later version.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.

# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 675 Mass Ave, Cambridge, MA 02139, USA.

# Usage: configure [--srcdir=DIR] [--host=HOST] [--gas] [--nfp] [--no-create]
#        [--prefix=PREFIX] [--exec-prefix=PREFIX] [--with-PACKAGE] [TARGET]
# Ignores all args except --srcdir, --prefix, --exec-prefix, --no-create, and
# --with-<PERSON><PERSON><PERSON><PERSON> unless this script has special code to handle it.


for arg
do
  # Handle --exec-prefix with a space before the argument.
  if test x$next_exec_prefix = xyes; then exec_prefix=$arg; next_exec_prefix=
  # Handle --host with a space before the argument.
  elif test x$next_host = xyes; then next_host=
  # Handle --prefix with a space before the argument.
  elif test x$next_prefix = xyes; then prefix=$arg; next_prefix=
  # Handle --srcdir with a space before the argument.
  elif test x$next_srcdir = xyes; then srcdir=$arg; next_srcdir=
  else
    case $arg in
     # For backward compatibility, also recognize exact --exec_prefix.
     -exec-prefix=* | --exec_prefix=* | --exec-prefix=* | --exec-prefi=* | --exec-pref=* | --exec-pre=* | --exec-pr=* | --exec-p=* | --exec-=* | --exec=* | --exe=* | --ex=* | --e=*)
	exec_prefix=`echo $arg | sed 's/[-a-z_]*=//'` ;;
     -exec-prefix | --exec_prefix | --exec-prefix | --exec-prefi | --exec-pref | --exec-pre | --exec-pr | --exec-p | --exec- | --exec | --exe | --ex | --e)
	next_exec_prefix=yes ;;

     -gas | --gas | --ga | --g) ;;

     -host=* | --host=* | --hos=* | --ho=* | --h=*) ;;
     -host | --host | --hos | --ho | --h)
	next_host=yes ;;

     -nfp | --nfp | --nf) ;;

     -no-create | --no-create | --no-creat | --no-crea | --no-cre | --no-cr | --no-c | --no- | --no)
        no_create=1 ;;

     -prefix=* | --prefix=* | --prefi=* | --pref=* | --pre=* | --pr=* | --p=*)
	prefix=`echo $arg | sed 's/[-a-z_]*=//'` ;;
     -prefix | --prefix | --prefi | --pref | --pre | --pr | --p)
	next_prefix=yes ;;

     -srcdir=* | --srcdir=* | --srcdi=* | --srcd=* | --src=* | --sr=* | --s=*)
	srcdir=`echo $arg | sed 's/[-a-z_]*=//'` ;;
     -srcdir | --srcdir | --srcdi | --srcd | --src | --sr | --s)
	next_srcdir=yes ;;

     -with-* | --with-*)
       package=`echo $arg|sed 's/-*with-//'`
       # Delete all the valid chars; see if any are left.
       if test -n "`echo $package|sed 's/[-a-zA-Z0-9_]*//g'`"; then
         echo "configure: $package: invalid package name" >&2; exit 1
       fi
       eval "with_`echo $package|sed s/-/_/g`=1" ;;

     -v | -verbose | --verbose | --verbos | --verbo | --verb | --ver | --ve | --v)
       verbose=yes ;;

     *) ;;
    esac
  fi
done

trap 'rm -f conftest* core; exit 1' 1 3 15

# Needed for some versions of `tr' so that character classes in `[]' work.
if test "${LANG+set}" = "set" ; then
   LANG=C
   LC_ALL=C
   export LANG LC_ALL
fi

rm -f conftest*
compile='${CC-cc} $CFLAGS $DEFS conftest.c -o conftest $LIBS >/dev/null 2>&1'

# A filename unique to this package, relative to the directory that
# configure is in, which we can look for to find out if srcdir is correct.
unique_file=gzip.c

# Find the source files, if location was not specified.
if test -z "$srcdir"; then
  srcdirdefaulted=yes
  # Try the directory containing this script, then `..'.
  prog=$0
  confdir=`echo $prog|sed 's%/[^/][^/]*$%%'`
  test "X$confdir" = "X$prog" && confdir=.
  srcdir=$confdir
  if test ! -r $srcdir/$unique_file; then
    srcdir=..
  fi
fi
if test ! -r $srcdir/$unique_file; then
  if test x$srcdirdefaulted = xyes; then
    echo "configure: Can not find sources in \`${confdir}' or \`..'." 1>&2
  else
    echo "configure: Can not find sources in \`${srcdir}'." 1>&2
  fi
  exit 1
fi
# Preserve a srcdir of `.' to avoid automounter screwups with pwd.
# But we can't avoid them for `..', to make subdirectories work.
case $srcdir in
  .|/*|~*) ;;
  *) srcdir=`cd $srcdir; pwd` ;; # Make relative path absolute.
esac

if test -z "$CC"; then
  # Extract the first word of `gcc', so it can be a program name with args.
  set dummy gcc; word=$2
  echo checking for $word
  IFS="${IFS= 	}"; saveifs="$IFS"; IFS="${IFS}:"
  for dir in $PATH; do
    test -z "$dir" && dir=.
    if test -f $dir/$word; then
      CC="gcc"
      break
    fi
  done
  IFS="$saveifs"
fi
test -z "$CC" && CC="cc"
test -n "$CC" -a -n "$verbose" && echo "	setting CC to $CC"

# Find out if we are using GNU C, under whatever name.
cat > conftest.c <<EOF
#ifdef __GNUC__
  yes
#endif
EOF
${CC-cc} -E conftest.c > conftest.out 2>&1
if egrep yes conftest.out >/dev/null 2>&1; then
  GCC=1 # For later tests.
  CFLAGS="${CFLAGS--O}"
fi
rm -f conftest*

echo checking how to run the C preprocessor
if test -z "$CPP"; then
  CPP='${CC-cc} -E'
  cat > conftest.c <<EOF
#include <stdio.h>
EOF
err=`eval "($CPP \$DEFS conftest.c >/dev/null) 2>&1"`
if test -z "$err"; then
  :
else
  CPP=/lib/cpp
fi
rm -f conftest*
fi

echo checking for underline in external names
test -z "$ASCPP" -a -f /lib/cpp && ASCPP=/lib/cpp
test -z "$ASCPP" && ASCPP="$CC -E"
cat > conftest.c <<EOF
int foo() {return 0;}
EOF
eval "$CC -c conftest.c > /dev/null 2>&1"
if nm conftest.o | grep _foo > /dev/null 2>&1 ; then
  :
else
  ASCPP="${ASCPP} -DNO_UNDERLINE"
fi
rm -f _match.o conftest.c conftest.o
if echo "$DEFS" | grep NO_ASM >/dev/null; then
  :
else
  echo checking for assembler
  OBJA=""
  if eval "$ASCPP $srcdir/match.S > _match.s 2>/dev/null"; then
    if test ! -s _match.s || grep error < _match.s > /dev/null; then
      :
    elif eval "$CC -c _match.s >/dev/null 2>&1" && test -f _match.o; then
      DEFS="${DEFS} -DASMV"
      OBJA=match.o
    fi
  fi
  rm -f _match.s _match.o
fi
# Make sure to not get the incompatible SysV /etc/install and
# /usr/sbin/install, which might be in PATH before a BSD-like install,
# or the SunOS /usr/etc/install directory, or the AIX /bin/install,
# or the AFS install, which mishandles nonexistent args, or
# /usr/ucb/install on SVR4, which tries to use the nonexistent group
# `staff'.  On most BSDish systems install is in /usr/bin, not /usr/ucb
# anyway.  Sigh.
if test "z${INSTALL}" = "z" ; then
  echo checking for install
  IFS="${IFS= 	}"; saveifs="$IFS"; IFS="${IFS}:"
  for dir in $PATH; do
    test -z "$dir" && dir=.
    case $dir in
    /etc|/usr/sbin|/usr/etc|/usr/afsws/bin|/usr/ucb) ;;
    *)
      if test -f $dir/installbsd; then
	INSTALL="$dir/installbsd -c" # OSF1
	INSTALL_PROGRAM='$(INSTALL)'
	INSTALL_DATA='$(INSTALL) -m 644'
	break
      fi
      if test -f $dir/install; then
	if grep dspmsg $dir/install >/dev/null 2>&1; then
	  : # AIX
	else
	  INSTALL="$dir/install -c"
	  INSTALL_PROGRAM='$(INSTALL)'
	  INSTALL_DATA='$(INSTALL) -m 644'
	  break
	fi
      fi
      ;;
    esac
  done
  IFS="$saveifs"
fi
INSTALL=${INSTALL-cp}
INSTALL_PROGRAM=${INSTALL_PROGRAM-'$(INSTALL)'}
INSTALL_DATA=${INSTALL_DATA-'$(INSTALL)'}

echo checking for AIX
cat > conftest.c <<EOF
#ifdef _AIX
  yes
#endif

EOF
eval "$CPP \$DEFS conftest.c > conftest.out 2>&1"
if egrep "yes" conftest.out >/dev/null 2>&1; then
  {
test -n "$verbose" && \
echo '	defining' _ALL_SOURCE
DEFS="$DEFS -D_ALL_SOURCE=1"
}

fi
rm -f conftest*


echo checking for minix/config.h
cat > conftest.c <<EOF
#include <minix/config.h>
EOF
err=`eval "($CPP \$DEFS conftest.c >/dev/null) 2>&1"`
if test -z "$err"; then
  MINIX=1
fi
rm -f conftest*

# The Minix shell can't assign to the same variable on the same line!
if test -n "$MINIX"; then
  {
test -n "$verbose" && \
echo '	defining' _POSIX_SOURCE
DEFS="$DEFS -D_POSIX_SOURCE=1"
}

  {
test -n "$verbose" && \
echo '	defining' _POSIX_1_SOURCE to be '2'
DEFS="$DEFS -D_POSIX_1_SOURCE=2"
}

  {
test -n "$verbose" && \
echo '	defining' _MINIX
DEFS="$DEFS -D_MINIX=1"
}

fi

echo checking for POSIXized ISC
if test -d /etc/conf/kconfig.d &&
  grep _POSIX_VERSION /usr/include/sys/unistd.h >/dev/null 2>&1
then
  ISC=1 # If later tests want to check for ISC.
  {
test -n "$verbose" && \
echo '	defining' _POSIX_SOURCE
DEFS="$DEFS -D_POSIX_SOURCE=1"
}

  if test -n "$GCC"; then
    CC="$CC -posix"
  else
    CC="$CC -Xp"
  fi
fi

echo checking for DYNIX/ptx libseq
cat > conftest.c <<EOF
#if defined(_SEQUENT_)
  yes
#endif

EOF
eval "$CPP \$DEFS conftest.c > conftest.out 2>&1"
if egrep "yes" conftest.out >/dev/null 2>&1; then
  SEQUENT=1
fi
rm -f conftest*

test -n "$SEQUENT" && test -f /usr/lib/libseq.a &&
  LIBS="$LIBS -lseq"

echo checking for ANSI C header files
cat > conftest.c <<EOF
#include <stdlib.h>
#include <stdarg.h>
#include <string.h>
#include <float.h>
EOF
err=`eval "($CPP \$DEFS conftest.c >/dev/null) 2>&1"`
if test -z "$err"; then
  # SunOS 4.x string.h does not declare mem*, contrary to ANSI.
echo '#include <string.h>' > conftest.c
eval "$CPP \$DEFS conftest.c > conftest.out 2>&1"
if egrep "memchr" conftest.out >/dev/null 2>&1; then
  # SGI's /bin/cc from Irix-4.0.5 gets non-ANSI ctype macros unless using -ansi.
cat > conftest.c <<EOF
#include <ctype.h>
#define ISLOWER(c) ('a' <= (c) && (c) <= 'z')
#define TOUPPER(c) (ISLOWER(c) ? 'A' + ((c) - 'a') : (c))
#define XOR(e,f) (((e) && !(f)) || (!(e) && (f)))
int main () { int i; for (i = 0; i < 256; i++)
if (XOR (islower (i), ISLOWER (i)) || toupper (i) != TOUPPER (i)) exit(2);
exit (0); }

EOF
eval $compile
if test -s conftest && (./conftest; exit) 2>/dev/null; then
  {
test -n "$verbose" && \
echo '	defining' STDC_HEADERS
DEFS="$DEFS -DSTDC_HEADERS=1"
}

fi
rm -f conftest*
fi
rm -f conftest*

fi
rm -f conftest*

echo checking for string.h
cat > conftest.c <<EOF
#include <string.h>
EOF
err=`eval "($CPP \$DEFS conftest.c >/dev/null) 2>&1"`
if test -z "$err"; then
  :
else
  {
test -n "$verbose" && \
echo '	defining' NO_STRING_H
DEFS="$DEFS -DNO_STRING_H=1"
}

fi
rm -f conftest*

echo checking for stdlib.h
cat > conftest.c <<EOF
#include <stdlib.h>
EOF
err=`eval "($CPP \$DEFS conftest.c >/dev/null) 2>&1"`
if test -z "$err"; then
  :
else
  {
test -n "$verbose" && \
echo '	defining' NO_STDLIB_H
DEFS="$DEFS -DNO_STDLIB_H=1"
}

fi
rm -f conftest*

echo checking for memory.h
cat > conftest.c <<EOF
#include <memory.h>
EOF
err=`eval "($CPP \$DEFS conftest.c >/dev/null) 2>&1"`
if test -z "$err"; then
  :
else
  {
test -n "$verbose" && \
echo '	defining' NO_MEMORY_H
DEFS="$DEFS -DNO_MEMORY_H=1"
}

fi
rm -f conftest*

echo checking for fcntl.h
cat > conftest.c <<EOF
#include <fcntl.h>
EOF
err=`eval "($CPP \$DEFS conftest.c >/dev/null) 2>&1"`
if test -z "$err"; then
  :
else
  {
test -n "$verbose" && \
echo '	defining' NO_FCNTL_H
DEFS="$DEFS -DNO_FCNTL_H=1"
}

fi
rm -f conftest*

echo checking for time.h
cat > conftest.c <<EOF
#include <time.h>
EOF
err=`eval "($CPP \$DEFS conftest.c >/dev/null) 2>&1"`
if test -z "$err"; then
  :
else
  {
test -n "$verbose" && \
echo '	defining' NO_TIME_H
DEFS="$DEFS -DNO_TIME_H=1"
}

fi
rm -f conftest*

for hdr in unistd.h
do
trhdr=HAVE_`echo $hdr | tr '[a-z]./' '[A-Z]__'`
echo checking for ${hdr}
cat > conftest.c <<EOF
#include <${hdr}>
EOF
err=`eval "($CPP \$DEFS conftest.c >/dev/null) 2>&1"`
if test -z "$err"; then
  {
test -n "$verbose" && \
echo '	defining' ${trhdr}
DEFS="$DEFS -D${trhdr}=1"
}

fi
rm -f conftest*
done

utime=0
echo checking for utime.h
cat > conftest.c <<EOF
#include <utime.h>
EOF
err=`eval "($CPP \$DEFS conftest.c >/dev/null) 2>&1"`
if test -z "$err"; then
  utime=1 
else
  {
test -n "$verbose" && \
echo '	defining' NO_UTIME_H
DEFS="$DEFS -DNO_UTIME_H=1"
}

fi
rm -f conftest*

if test $utime -eq 0; then
  for hdr in sys/utime.h
do
trhdr=HAVE_`echo $hdr | tr '[a-z]./' '[A-Z]__'`
echo checking for ${hdr}
cat > conftest.c <<EOF
#include <${hdr}>
EOF
err=`eval "($CPP \$DEFS conftest.c >/dev/null) 2>&1"`
if test -z "$err"; then
  {
test -n "$verbose" && \
echo '	defining' ${trhdr}
DEFS="$DEFS -D${trhdr}=1"
}

fi
rm -f conftest*
done

fi
echo checking for directory library header
dirheader=
if test -z "$dirheader"; then
  echo checking for dirent.h
cat > conftest.c <<EOF
#include <sys/types.h>
#include <dirent.h>
int main() { exit(0); }
int t() { DIR *dirp = opendir ("/"); }
EOF
if eval $compile; then
  {
test -n "$verbose" && \
echo '	defining' DIRENT
DEFS="$DEFS -DDIRENT=1"
}
 dirheader=dirent.h
fi
rm -f conftest*
fi
if test -z "$dirheader"; then
  echo checking for sys/ndir.h
cat > conftest.c <<EOF
#include <sys/types.h>
#include <sys/ndir.h>
int main() { exit(0); }
int t() { DIR *dirp = opendir ("/"); }
EOF
if eval $compile; then
  {
test -n "$verbose" && \
echo '	defining' SYSNDIR
DEFS="$DEFS -DSYSNDIR=1"
}
 dirheader=sys/ndir.h
fi
rm -f conftest*
fi
if test -z "$dirheader"; then
  echo checking for sys/dir.h
cat > conftest.c <<EOF
#include <sys/types.h>
#include <sys/dir.h>
int main() { exit(0); }
int t() { DIR *dirp = opendir ("/"); }
EOF
if eval $compile; then
  {
test -n "$verbose" && \
echo '	defining' SYSDIR
DEFS="$DEFS -DSYSDIR=1"
}
 dirheader=sys/dir.h
fi
rm -f conftest*
fi
if test -z "$dirheader"; then
  echo checking for ndir.h
cat > conftest.c <<EOF
#include <sys/types.h>
#include <ndir.h>
int main() { exit(0); }
int t() { DIR *dirp = opendir ("/"); }
EOF
if eval $compile; then
  {
test -n "$verbose" && \
echo '	defining' NDIR
DEFS="$DEFS -DNDIR=1"
}
 dirheader=ndir.h
fi
rm -f conftest*
fi

echo checking for closedir return value
cat > conftest.c <<EOF
#include <sys/types.h>
#include <$dirheader>
int closedir(); main() { exit(closedir(opendir(".")) != 0); }
EOF
eval $compile
if test -s conftest && (./conftest; exit) 2>/dev/null; then
  :
else
  {
test -n "$verbose" && \
echo '	defining' VOID_CLOSEDIR
DEFS="$DEFS -DVOID_CLOSEDIR=1"
}

fi
rm -f conftest*

echo checking for Xenix
cat > conftest.c <<EOF
#if defined(M_XENIX) && !defined(M_UNIX)
  yes
#endif

EOF
eval "$CPP \$DEFS conftest.c > conftest.out 2>&1"
if egrep "yes" conftest.out >/dev/null 2>&1; then
  XENIX=1
fi
rm -f conftest*

if test -n "$XENIX"; then
  LIBS="$LIBS -lx"
  case "$DEFS" in
  *SYSNDIR*) ;;
  *) LIBS="-ldir $LIBS" ;; # Make sure -ldir precedes any -lx.
  esac
fi

echo checking for return type of signal handlers
cat > conftest.c <<EOF
#include <sys/types.h>
#include <signal.h>
#ifdef signal
#undef signal
#endif
extern void (*signal ()) ();
int main() { exit(0); }
int t() { int i; }
EOF
if eval $compile; then
  :
else
  {
test -n "$verbose" && \
echo '	defining' RETSIGTYPE to be 'int'
DEFS="$DEFS -DRETSIGTYPE=int"
}

fi
rm -f conftest*


echo checking for size_t in sys/types.h
echo '#include <sys/types.h>' > conftest.c
eval "$CPP \$DEFS conftest.c > conftest.out 2>&1"
if egrep "size_t" conftest.out >/dev/null 2>&1; then
  :
else
  {
test -n "$verbose" && \
echo '	defining' size_t to be 'unsigned'
DEFS="$DEFS -Dsize_t=unsigned"
}

fi
rm -f conftest*

echo '#include <sys/types.h>' > conftest.c
eval "$CPP \$DEFS conftest.c > conftest.out 2>&1"
if egrep "off_t" conftest.out >/dev/null 2>&1; then
  :
else
  {
test -n "$verbose" && \
echo '	defining' NO_OFF_T
DEFS="$DEFS -DNO_OFF_T=1"
}

fi
rm -f conftest*

echo "checking if \`#!' works in shell scripts"
cat <<'__EOF__' > conftest.csh
#!/bin/csh -f
setenv SHELL /bin/csh
# Avoid tcsh bug 'Bad Hertz Value':
setenv HZ 60
# Make sure foo doesn't get exported into the environment
# Astoundingly, some versions of csh don't have unsetenv.
if (${?foo}) unsetenv foo >& /dev/null
if (${?foo}) unset foo
set output="`./conftest.sh`"
if ( "$output" == "foo=bar" ) then
   exit 0
endif
exit 1
__EOF__
cat <<'__EOF__' > conftest.sh
#!/bin/sh
set foo=bar
echo "$*"
__EOF__
chmod 777 conftest.csh conftest.sh
(csh -f ./conftest.csh) 2> /dev/null
if test $? = 0 ; then
   :; SEDCMD="1d"
else
   :; SEDCMD=""
fi
rm -f conftest.csh conftest.sh

if test -z "$prefix"
then
  echo checking for gzip to derive installation directory prefix
  IFS="${IFS= 	}"; saveifs="$IFS"; IFS="$IFS:"
  for dir in $PATH; do
    test -z "$dir" && dir=.
    if test $dir != . && test -f $dir/gzip; then
      # Not all systems have dirname.
      prefix=`echo $dir|sed 's%/[^/][^/]*$%%'`
      break
    fi
  done
  IFS="$saveifs"
  echo "	chose installation directory prefix ${prefix}"
fi

if test -z "$G" -a -n "$prefix" -a -f $prefix/bin/gznew; then
  G=g
fi
if test -z "$ZCAT"; then
  if test -n "$prefix" -a -f $prefix/bin/gzcat; then
    ZCAT=gzcat
  else
    ZCAT=${G}zcat
  fi
fi
if test -n "$prefix"; then
  test -z "$exec_prefix" && exec_prefix='${prefix}'
  prsub="s%^prefix\\([ 	]*\\)=\\([ 	]*\\).*$%prefix\\1=\\2$prefix%"
fi
if test -n "$exec_prefix"; then
  prsub="$prsub
s%^exec_prefix\\([ 	]*\\)=\\([ 	]*\\).*$%\
exec_prefix\\1=\\2$exec_prefix%"
fi
cat >conftest.def <<EOF
$DEFS
EOF
escape_ampersand_and_backslash='s%[&\\]%\\&%g'
DEFS=`sed "$escape_ampersand_and_backslash" <conftest.def`
rm -f conftest.def

trap 'rm -f config.status; exit 1' 1 3 15
echo creating config.status
rm -f config.status
cat > config.status <<EOF
#!/bin/sh
# Generated automatically by configure.
# Run this file to recreate the current configuration.
# This directory was configured as follows,
# on host `(hostname || uname -n) 2>/dev/null | sed 1q`:
#
# $0 $*

for arg
do
  case "\$arg" in
    -recheck | --recheck | --rechec | --reche | --rech | --rec | --re | --r)
    exec /bin/sh $0 $* ;;
    *) echo "Usage: config.status --recheck" 2>&1; exit 1 ;;
  esac
done

trap 'rm -f Makefile; exit 1' 1 3 15
CC='$CC'
CPP='$CPP'
INSTALL='$INSTALL'
INSTALL_PROGRAM='$INSTALL_PROGRAM'
INSTALL_DATA='$INSTALL_DATA'
ZCAT='$ZCAT'
G='$G'
CFLAGS='$CFLAGS'
ASCPP='$ASCPP'
OBJA='$OBJA'
SEDCMD='$SEDCMD'
LIBS='$LIBS'
srcdir='$srcdir'
DEFS='$DEFS'
prefix='$prefix'
exec_prefix='$exec_prefix'
prsub='$prsub'
EOF
cat >> config.status <<\EOF

top_srcdir=$srcdir

# Allow make-time overrides of the generated file list.
test -n "$gen_files" || gen_files="Makefile"

for file in .. $gen_files; do if [ "x$file" != "x.." ]; then
  srcdir=$top_srcdir
  # Remove last slash and all that follows it.  Not all systems have dirname.
  dir=`echo $file|sed 's%/[^/][^/]*$%%'`
  if test "$dir" != "$file"; then
    test "$top_srcdir" != . && srcdir=$top_srcdir/$dir
    test ! -d $dir && mkdir $dir
  fi
  echo creating $file
  rm -f $file
  echo "# Generated automatically from `echo $file|sed 's|.*/||'`.in by configure." > $file
  sed -e "
$prsub
s%@CC@%$CC%g
s%@CPP@%$CPP%g
s%@INSTALL@%$INSTALL%g
s%@INSTALL_PROGRAM@%$INSTALL_PROGRAM%g
s%@INSTALL_DATA@%$INSTALL_DATA%g
s%@ZCAT@%$ZCAT%g
s%@G@%$G%g
s%@CFLAGS@%$CFLAGS%g
s%@ASCPP@%$ASCPP%g
s%@OBJA@%$OBJA%g
s%@SEDCMD@%$SEDCMD%g
s%@LIBS@%$LIBS%g
s%@srcdir@%$srcdir%g
s%@DEFS@%$DEFS%
" $top_srcdir/${file}.in >> $file
fi; done

exit 0
EOF
chmod +x config.status
test -n "$no_create" || ./config.status

