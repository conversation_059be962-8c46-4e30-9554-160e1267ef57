:
#!/bin/sh

PATH="BINDIR:$PATH"; export PATH
check=0
pipe=0
opt=
files=
keep=0
res=0
old=0
new=0
block=1024
# block is the disk block size (best guess, need not be exact)

warn="(does not preserve modes and timestamp)"
tmp=/tmp/zfoo.$$
echo hi > $tmp.1
echo hi > $tmp.2
if test -z "`(${CPMOD-cpmod} $tmp.1 $tmp.2) 2>&1`"; then
  cpmod=${CPMOD-cpmod}
  warn=""
fi

if test -z "$cpmod" && ${TOUCH-touch} -r $tmp.1 $tmp.2 2>/dev/null; then
  cpmod="${TOUCH-touch}"
  cpmodarg="-r"
  warn="(does not preserve file modes)"
fi

# check if GZIP env. variable uses -S or --suffix
gzip -q $tmp.1
ext=`echo $tmp.1* | sed "s|$tmp.1||"`
rm -f $tmp.[12]*
if test -z "$ext"; then
  echo znew: error determining gzip extension
  exit 1
fi
if test "$ext" = ".Z"; then
  echo znew: cannot use .Z as gzip extension.
  exit 1
fi

for arg
do
  case "$arg" in
  -*)     opt="$opt $arg"; shift;;
   *)     break;;
  esac
done

if test $# -eq 0; then
  echo "recompress .Z files into $ext (gzip) files"
  echo usage: `echo $0 | sed 's,^.*/,,'` "[-tv9KP]" file.Z...
  echo "  -t tests the new files before deleting originals"
  echo "  -v be verbose"
  echo "  -9 use the slowest compression method (optimal compression)"
  echo "  -K keep a .Z file when it is smaller than the $ext file"
  echo "  -P use pipes for the conversion $warn"
  exit 1
fi

opt=`echo "$opt" | sed -e 's/ //g' -e 's/-//g'`
case "$opt" in
  *t*) check=1; opt=`echo "$opt" | sed 's/t//g'`
esac
case "$opt" in
  *K*) keep=1; opt=`echo "$opt" | sed 's/K//g'`
esac
case "$opt" in
  *P*) pipe=1; opt=`echo "$opt" | sed 's/P//g'`
esac
if test -n "$opt"; then
  opt="-$opt"
fi

for i do
  n=`echo $i | sed 's/.Z$//'`
  if test ! -f "$n.Z" ; then
    echo $n.Z not found
    res=1; continue
  fi
  test $keep -eq 1 && old=`wc -c < "$n.Z"`
  if test $pipe -eq 1; then
    if gzip -d < "$n.Z" | gzip $opt > "$n$ext"; then
      # Copy file attributes from old file to new one, if possible.
      test -n "$cpmod" && $cpmod $cpmodarg "$n.Z" "$n$ext" 2> /dev/null
    else
      echo error while recompressing $n.Z
      res=1; continue
    fi
  else
    if test $check -eq 1; then
      if cp -p "$n.Z" "$n.$$" 2> /dev/null || cp "$n.Z" "$n.$$"; then
	:
      else
	echo cannot backup "$n.Z"
        res=1; continue
      fi
    fi
    if gzip -d "$n.Z"; then
      :
    else
      test $check -eq 1 && mv "$n.$$" "$n.Z"
      echo error while uncompressing $n.Z
      res=1; continue
    fi
    if gzip $opt "$n"; then
      :
    else
      if test $check -eq 1; then
	mv "$n.$$" "$n.Z" && rm -f "$n"
        echo error while recompressing $n
      else
	# compress $n  (might be dangerous if disk full)
        echo error while recompressing $n, left uncompressed
      fi
      res=1; continue
    fi
  fi
  test $keep -eq 1 && new=`wc -c < "$n$ext"`
  if test $keep -eq 1 -a `expr \( $old + $block - 1 \) / $block` -lt \
                         `expr \( $new + $block - 1 \) / $block`; then
    if test $pipe -eq 1; then
      rm -f "$n$ext"
    elif test $check -eq 1; then
      mv "$n.$$" "$n.Z" && rm -f "$n$ext"
    else
      gzip -d "$n$ext" && compress "$n" && rm -f "$n$ext"
    fi
    echo "$n.Z smaller than $n$ext -- unchanged"

  elif test $check -eq 1; then
    if gzip -t "$n$ext" ; then
      rm -f "$n.$$" "$n.Z"
    else
      test $pipe -eq 0 && mv "$n.$$" "$n.Z"
      rm -f "$n$ext"
      echo error while testing $n$ext, $n.Z unchanged
      res=1; continue
    fi
  elif test $pipe -eq 1; then
    rm -f "$n.Z"
  fi
done
exit $res
