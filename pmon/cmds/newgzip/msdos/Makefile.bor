# Makefile for gzip
# Borland (Turbo) C++.
# Warning: this file is not suitable for Turbo C 2.0. In this case, read
# then invoke the file doturboc.bat.

# To use, do "make -fmakefile.bor"

# Turbo C++ 1.0 seems to have a buggy exit() function. You must add
# to CFLAGS: -Dexit=_exit

# WARNING: the small model is not supported. The compact model is used
# here. If you want to use the large model, add -D__LARGE__ to ASFLAGS
# Add -DSMALL_MEM to CFLAGS if you wish to reduce the memory
# requirements. Add -DNO_ASM to CFLAGS and remove match.obj from OBJA if
# you do not have tasm.

# ------------- Turbo C++, Borland C++ -------------
MODEL=-mc
#CFLAGS=-w -w-eff -w-def -w-sig -w-cln -a -d -G -O -Z $(MODEL)
CFLAGS=-O2 -Z $(MODEL)
CC=bcc
LD=bcc
#   replace bcc with tcc for Turbo C++ 1.0
LDFLAGS=$(MODEL)
AS=tasm
ASFLAGS=-ml -t -DDYN_ALLOC -DSS_NEQ_DS
LIB = c:\bcc\lib

# ------------- Common declarations:
STRIP=rem
#    If you don't have lzexe, get it (by ftp on wuarchive.wustl.edu
#    in /mirrors/msdos/filutl/lzexe91e.zip). Then define:
#STRIP=lzexe
#    Or if you've registered PKLITE, then define:
#STRIP=pklite
#    This makes a big difference in .exe size (and possibly load time)

O=.obj
OBJA=match$(O) tailor$(O) $(LIB)\wildargs.obj

# ------------- Used by install rule
# set BIN to the directory you want to install the executables to
BIN = c:\bin

# variables
OBJ1 = gzip$(O) zip$(O) deflate$(O) trees$(O) bits$(O) unzip$(O) inflate$(O) \
       util$(O)
OBJ2 = crypt$(O) lzw$(O) unlzw$(O) unpack$(O) unlzh$(O) getopt$(O) $(OBJA)

all: gzip.exe

gzip.obj: gzip.c gzip.h tailor.h crypt.h revision.h lzw.h
	$(CC) -c $(CFLAGS) $*.c

zip.obj: zip.c gzip.h tailor.h crypt.h
	$(CC) -c $(CFLAGS) $*.c

deflate.obj: deflate.c gzip.h tailor.h
	$(CC) -c $(CFLAGS) $*.c

trees.obj: trees.c gzip.h tailor.h
	$(CC) -c $(CFLAGS) $*.c

bits.obj: bits.c gzip.h tailor.h crypt.h
	$(CC) -c $(CFLAGS) $*.c

unzip.obj: unzip.c gzip.h tailor.h crypt.h
	$(CC) -c $(CFLAGS) $*.c

inflate.obj: inflate.c gzip.h tailor.h
	$(CC) -c $(CFLAGS) $*.c

util.obj: util.c gzip.h tailor.h crypt.h
	$(CC) -c $(CFLAGS) $*.c

crypt.obj: crypt.c gzip.h tailor.h crypt.h
	$(CC) -c $(CFLAGS) $*.c

lzw.obj: lzw.c gzip.h tailor.h
	$(CC) -c $(CFLAGS) $*.c

unlzw.obj: unlzw.c gzip.h tailor.h lzw.h
	$(CC) -c $(CFLAGS) $*.c

unpack.obj: unpack.c gzip.h tailor.h
	$(CC) -c $(CFLAGS) $*.c

unlzh.obj: unlzh.c gzip.h tailor.h
	$(CC) -c $(CFLAGS) $*.c

tailor.obj: msdos\tailor.c gzip.h tailor.h
	$(CC) -c $(CFLAGS) msdos\$*.c

getopt.obj: getopt.c getopt.h
	$(CC) -c $(CFLAGS) $*.c

match.obj:	msdos\match.asm
	$(AS) $(ASFLAGS) msdos\match, match;

# we must cut the command line to fit in the MS/DOS 128 byte limit:
gzip.exe: $(OBJ1) $(OBJ2)
	echo $(OBJ1) > gzip.rsp
	echo $(OBJ2) >> gzip.rsp
	$(LD) $(LDFLAGS) @gzip.rsp
	del gzip.rsp
	$(STRIP) gzip.exe
# If you use Borland make, you can use instead:
#	$(LD) $(LDFLAGS) @&&|
#$**
#|

install: gzip.exe
	copy /b gzip.exe $(BIN)
	copy /b gzip.exe $(BIN)\gunzip.exe

clean:
	del *.obj
	del *.exe
