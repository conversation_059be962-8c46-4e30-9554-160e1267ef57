# Makefile for gzip (GNU zip)    -*- Indented-Text -*-
# Copyright (C) 1992-1993 <PERSON><PERSON><PERSON><PERSON> and the Free Software Foundation
# Modified for GNUish make/MS_SH200/DJGPP 1.09+ by <PERSON>

# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 2, or (at your option)
# any later version.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.

# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 675 Mass Ave, Cambridge, MA 02139, USA.

# $Id: Makefile.djg,v ******* 2006/09/14 01:59:08 root Exp $

#### Start of system configuration section. ####

.SUFFIXES: .o
LONGARGS = gcc:rm

srcdir = .
VPATH = .

CC = gcc
CPP = gcc -O -E

INSTALL = install -c
INSTALL_PROGRAM = $(INSTALL)
INSTALL_DATA = $(INSTALL) -m 644

# tailor.h defines things for DJGPP so nothing is needed here
DEFS =
LIBS =

# additional assembly sources for particular systems may be required.
OBJA = match.o

SEDCMD = 1d

CFLAGS = -O2 -finline-functions -fomit-frame-pointer
LDFLAGS = -s

X=.exe
# For OS/2 or MSDOS, use: X=.exe

O=.o
# For OS/2 or MSDOS, use: O=.obj

prefix = /usr/local
exec_prefix = $(prefix)

bindir = $(exec_prefix)/bin
datadir = $(prefix)/lib
libdir = $(prefix)/lib
infodir = $(prefix)/info

# Extension (not including `.') for the installed manual page filenames.
manext = 1
# Where to install the manual pages.
mandir = $(prefix)/man/man$(manext)

#### End of system configuration section. ####

SHELL = /bin/sh

LOADLIBES = $(LIBS)

TAR = tar

SRCS = gzip.c zip.c deflate.c trees.c bits.c unzip.c inflate.c util.c crypt.c\
       lzw.c unlzw.c unpack.c unlzh.c makecrc.c getopt.c match.S

OBJ1 = gzip$(O) zip$(O) deflate$(O) trees$(O) bits$(O) unzip$(O) inflate$(O) \
       util$(O)
OBJ2 = crypt$(O) lzw$(O) unlzw$(O) unpack$(O) unlzh$(O) getopt$(O) $(OBJA)

HDRS = gzip.h lzw.h tailor.h revision.h crypt.h getopt.h

.c$O:
	$(CC) -c $(DEFS) $(CFLAGS) $<

#.PHONY: default all force test check

default:  all
all:	gzip$X zcmp zdiff zmore znew zforce gzexe
force:

install: all
	for f in gzip$X zcmp zdiff zmore znew zforce gzexe; do \
	  $(INSTALL_PROGRAM) $${f} $(bindir)/$${f}; done
	for f in gunzip$X zcat$X ; do \
	  rm -f $(bindir)/$${f}; ln $(bindir)/gzip$X $(bindir)/$${f}; done
	-cd $(srcdir); \
	  for f in gzip gunzip zcat zcmp zmore znew zforce gzexe; do \
	  rm -f $(mandir)/$${f}.$(manext); \
	  $(INSTALL_DATA) $${f}.1 $(mandir)/$${f}.$(manext); done
	-cd $(srcdir); for f in gzip.info* ; do $(INSTALL_DATA) $${f} \
	  $(infodir)/$${f}; done

uninstall: force
	-cd $(bindir); rm -f gzip$X gunzip$X zcat$X \
	  zcmp zdiff zmore znew zforce gzexe
	-for f in gzip gunzip zcat zcmp zmore znew gzexe; do \
	  rm -f $(mandir)/$${f}.$(manext); done
	-cd $(infodir); rm -f gzip.info*

# install all files and replace compress (not recommended)
install_compress: install
	-test -f $(bindir)/compress.old || \
	  mv $(bindir)/compress$X $(bindir)/compress.old
	ln $(bindir)/gzip$X $(bindir)/compress$X
	rm -f $(bindir)/uncompress$X
	ln $(bindir)/gzip$X $(bindir)/uncompress$X

test: check
check:	all
	./gzip -5 < $(srcdir)/texinfo.tex > _gztest.z
	# Either GNUish make or MS_SH has trouble with backquotes
	#@if test `wc -c < _gztest.z` -ne 30890; then \
	#   echo FAILED gzip test: incorrect size; rm -f _gztest*; exit 1; \
	#   else :; fi
	@echo Should be 30890:\\c
	@wc -c < _gztest.z
	rm -f _gztest
	./gzip -d _gztest.z
	@if cmp _gztest $(srcdir)/texinfo.tex; then \
	   echo gzip test OK; \
	else \
	   echo FAILED gzip test: incorrect decompress; \
	fi
	rm -f _gztest*

TAGS: $(SRCS) $(HDRS)
	cd $(srcdir); etags $(SRCS) $(HDRS)

Makefile: Makefile.in ./config.status
	./config.status

./config.status: configure
	$(srcdir)/configure --srcdir=$(srcdir) --no-create

configure: configure.in
	@echo Warning: configure is out of date
#	cd $(srcdir); autoconf

clean:
	rm -f *$O gzip$X gunzip$X zcat$X a.out core gzip
	rm -f zcmp zdiff zmore znew zforce gzexe _gztest*
	rm -f *.aux *.cp *.cps *.dvi *.fn *.fns *.ky *.kys *.log
	rm -f *.pg *.pgs *.toc *.tp *.tps *.vr *.vrs

mostlyclean: clean

distclean: clean
	rm -f Makefile config.status

realclean: distclean
	rm -f TAGS gzip.info*

# Actual build-related targets

gzip$X:	gzip
	aout2exe gzip

gzip:	$(OBJ1) $(OBJ2)
        echo $(OBJ1) > gzip.rsp
        echo $(OBJ2) $(LIBS) >> gzip.rsp
	$(CC) $(LDFLAGS) -o $@ @gzip.rsp
	del gzip.rsp

gzip$O zip$O deflate$O trees$O bits$O unzip$O inflate$O: gzip.h tailor.h
util$O lzw$O unlzw$O unpack$O unlzh$O crypt$O: gzip.h tailor.h

gzip$O unlzw$O: revision.h lzw.h

bits$O unzip$O util$O zip$O: crypt.h

gzip$O getopt$O: getopt.h

match$O: match.S
	$(CC) -c match.S

zcmp: zcmp.in
	sed "$(SEDCMD)" $(srcdir)/zcmp.in > zcmp
	chmod 755 zcmp

zdiff: zdiff.in
	sed "$(SEDCMD)" $(srcdir)/zdiff.in > zdiff
	chmod 755 zdiff

zmore: zmore.in
	sed "$(SEDCMD)" $(srcdir)/zmore.in > zmore
	chmod 755 zmore

znew: znew.in
	sed "$(SEDCMD)" $(srcdir)/znew.in > znew
	chmod 755 znew

zforce: zforce.in
	sed "$(SEDCMD)" $(srcdir)/zforce.in > zforce
	chmod 755 zforce

gzexe: gzexe.in
	sed "$(SEDCMD)" $(srcdir)/gzexe.in > gzexe
	chmod 755 gzexe

gzip.info: gzip.texi
	cd $(srcdir); makeinfo gzip.texi

gzip.dvi: gzip.texi
	cd $(srcdir); texi2dvi gzip.texi

gzip.doc: gzip.1
	cd $(srcdir); nroff -man gzip.1 | col -b | uniq > gzip.doc

# Prevent GNU make v3 from overflowing arg limit on SysV.
.NOEXPORT:

# end of file
