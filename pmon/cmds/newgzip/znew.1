.TH ZNEW 1
.SH NAME
znew \-   recompress .Z files to .gz files
.SH SYNOPSIS
.B znew
[ -ftv9PK] [ name.Z ...  ]
.SH DESCRIPTION
.I  Znew
recompresses files from .Z (compress) format to .gz (gzip) format.
If you want to recompress a file already in gzip format, rename the file
to force a .Z extension then apply znew.
.SH OPTIONS
.TP
.B \-f
Force recompression from .Z to .gz format even if a .gz file already exists.
.TP
.B \-t
Tests the new files before deleting originals.
.TP
.B \-v
Verbose. Display the name and percentage reduction for each file compressed.
.TP
.B \-9
Use the slowest compression method (optimal compression).
.TP
.B \-P
Use pipes for the conversion to reduce disk space usage.
.TP
.B \-K
Keep a .Z file when it is smaller than the .gz file
.SH "SEE ALSO"
gzip(1), zmore(1), zdiff(1), zgrep(1), zforce(1), gzexe(1), compress(1)
.SH BUGS
.I Znew
does not maintain the time stamp with the -P option if
.I cpmod(1)
is not available and
.I touch(1)
does not support the -r option.
