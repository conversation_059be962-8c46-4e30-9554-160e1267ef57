.TH ZDIFF 1
.SH NAME
zcmp, zdiff \- compare compressed files
.SH SYNOPSIS
.B zcmp
[ cmp_options ] file1
[ file2 ]
.br
.B zdiff
[ diff_options ] file1
[ file2 ]
.SH DESCRIPTION
.I  Zcmp
and 
.I zdiff
are used to invoke the
.I cmp
or the
.I diff
program on compressed files.  All options specified are passed directly to
.I cmp
or
.IR diff "."
If only 1 file is specified, then the files compared are
.I file1
and an uncompressed
.IR file1 ".gz."
If two files are specified, then they are uncompressed if necessary and fed to
.I cmp
or
.IR diff "."
The exit status from 
.I cmp
or
.I diff
is preserved.
.SH "SEE ALSO"
cmp(1), diff(1), zmore(1), zgrep(1), znew(1), zforce(1), gzip(1), gzexe(1)
.SH BUGS
Messages from the
.I cmp
or
.I diff
programs refer to temporary filenames instead of those specified.
