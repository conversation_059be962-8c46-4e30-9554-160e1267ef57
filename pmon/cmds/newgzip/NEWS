Current Version: 1.2.4.
See the file ChangeLog for the details of all changes.

Major changes from 1.2.3 to 1.2.4
* By default, do not restore file name and timestamp from those saved
  inside the .gz file (behave as 'compress'). Added the --name option
  to force name and timestamp restoration.
* Accept - as synonym for stdin.
* Use manlinks=so or ln to support either hard links or .so in man pages
* Accept foo.gz~ in zdiff.
* Added support for Windows NT
* Handle ENAMETOOLONG for strict Posix systems
* Use --recursive instead of --recurse to comply with Webster and
  the GNU stdandard.
* Allow installation of shell scripts with a g prefix: make G=g install
* Install by default zcat as gzcat if gzcat already exists in path.
* Let zmore behave as more when invoked without parameters (give help)
* Let gzip --list reject files not in gzip format even with --force.
* Don't complain about non gzip files for options -rt or -rl.
* Added advice in INSTALL for several systems.

Major changes from 1.2.2 to 1.2.3
* Don't display the output name when decompressing except with --verbose.
* Remove usage of alloca in getopt.c and all makefiles.
* Added the zfile shell script in subdirectory sample.
* Moved the list of compiler bugs from README to INSTALL.
* Added vms/Readme.vms.

Major changes from 1.2.1 to 1.2.2
* Fix a compilation error on Sun with cc (worked with gcc).

Major changes from 1.2 to 1.2.1
* Let zmore act as more if the data is not gzipped.
* made gzexe more secure (don't rely on PATH).
* By default, display output name only when the name was actually truncated.

Major changes from 1.1.2 to 1.2
* Added the --list option to display the file characteristics.
* Added the --no-name option: do not save or restore original filename
  Save the original name by default.
* Allow gunzip --suffix "" to attempt decompression on any file
  regardless of its extension if an original name is present.
* Add support for the SCO compress -H format.
* gzip --fast now compresses faster (speed close to that of compress)
  with degraded compression ratio (but still better than compress).
  Default level changed to -6 (acts exactly as previous level -5) to
  be a better indication of its placement in the speed/ratio range.
* Use smart name truncation: 123456789012.c -> 123456789.c.gz
   instead of 12345678901.gz
* With --force, let zcat pass non gzip'ed data unchanged (zcat == cat)
* Added the zgrep shell script.
* Made sub.c useful for 16 bit sound, 24 bit images, etc..
* Supress warnings about suffix for gunzip -r, except with --verbose.
* On MSDOS, use .gz extension when possible (files without extension)
* Moved the sample programs to a subdirectory sample.
* Added a "Special targets" section in INSTALL.

Major changes from 1.1.1 to 1.1.2.
* Fix serious bug for VMS (-gz not removed when decompressing).
* Allow suffix other than .gz in znew.
* Do not display compression ratio when decompressing stdin.
* In zmore.in, work around brain damaged stty -g (Ultrix).
* Display a correct compression ratio for .Z files.
* Added .z to .gz renaming script in INTALL.
* Allow setting CFLAGS in configure.

Major changes from 1.1 to 1.1.1.
* Fix serious bug in vms.c (affects Vax/VMS only).
* Added --ascii option.
* Add workaround in configure.in for Ultrix (quote eval argument)

Major changes from 1.0.7 to 1.1.
* Use .gz suffix by default, add --suffix option.
* Let gunzip accept a "_z" suffix (used by one 'compress' on Vax/VMS).
* Quit when reading garbage from stdin instead of reporting an error.
* Added makefile for VAX/MMS and support for wildcards on VMS.
* Added support for MSC under OS/2.
* Added support for Prime/PRIMOS.
* Display compression ratio also when decompressing (with --verbose).
* Quit after --version (GNU standard)
* Use --force to bypass isatty() check
* Continue processing other files in case of recoverable error.
* Added comparison of zip and gzip in the readme file.
* Added small sample programs (ztouch, sub, add)
* Use less memory when compiled with -DSMALL_MEM (for MSDOS).
* Remove the "off by more than one minute" time stamp kludge

Major changes from 1.0.6 to 1.0.7.
* Allow zmore to read from standard input (like more).
* Support the 68000 (Atari ST) in match.S.
* Retry partial writes (required on Linux when gzip is suspended in a pipe).
* Allow full pathnames and renamings in gzexe.
* Don't let gzexe compress setuid executables or gzip itself.
* Added vms/Makefile.gcc for gcc on the Vax.
* Allow installation of binaries and shell scripts in different dirs.
* Allows complex PAGER variable in zmore (e.g.: PAGER="col -x | more")
* Allow installation of zcat as gzcat.
* Several small changes for portability to old or weird systems.
* Suppress help message and send compressed data to the terminal when
  gzip is invoked without parameters and without redirection.
*  Add compile option GNU_STANDARD to respect the GNU coding standards:
   with -DGNU_STANDARD, behave as gzip even if invoked under the name gunzip.
(I don't like the last two changes, which were requested by the FSF.)

Major changes from 1.0.5 to 1.0.6.
* Let gzexe detect executables that are already gzexe'd.
* Keep file attributes in znew and gzexe if cpmod is available.
* Don't try restoring record format on VMS (1.0.5 did not work correctly)
* Added asm version for 68000 in amiga/match.a.
  Use asm version for Atari TT and NeXT.
* For OS/2, assume HPFS by default, add flag OS2FAT if necessary.
* Fixed some bugs in zdiff and define zcmp as a link to zdiff.


Major changes from 1.0.4 to 1.0.5.
* For VMS, restore the file type for variable record format, otherwise
    extract in fixed length format (not perfect, but better than
    forcing all files to be in stream_LF format).
* For VMS, use "-z" default suffix and accept a version number in file names.
* For Unix, allow compression of files with name ending in 'z'. Use only
  .z, .*-z, .tgz, .taz as valid gzip extensions. In the last two cases,
  extract to .tar by default.
* On some versions of MSDOS, files with a 3 character extension could not
  be compressed.
* Garbage collect files in /tmp created by gzexe.
* Fix the 'OS code' byte in the gzip header.
* For the Amiga, add the missing utime.h and add support for gcc.


Major changes from 1.0.3 to 1.0.4.
* Added optimized asm version for 68020.
* Add support for DJGPP.
	
* Add support for the Atari ST.
* Added zforce to rename gzip'ed files with truncated names.
* Do not install with name uncompress (some systems rely on the
  absence of any check in the old uncompress).
* Added missing function (fcfree) in msdos/tailor.c
* Let gunzip handle .tgz files, and let gzip skip them.
* Added -d option (decompress) for gzexe and try preserving file permissions.
* Suppress all warnings with -q.
* Use GZIP_OPT for VMS to avoid conflict with program name.
* ... and many other small changes (see ChangeLog)


Major changes from 1.0.2 to 1.0.3
* Added -K option for znew to keep old .Z files if smaller
* Added -q option (quiet) to cancel -v in GZIP env variable.
* Made gzexe safer on systems with filename limitation to 14 chars.
* Fixed bugs in handling of GZIP env variable and incorrect free with Turbo C.


Major changes from 1.0.1 to 1.0.2
* Added env variable GZIP for default options. Example:
   for sh:   GZIP="-8 -v"; export GZIP
   for csh:  setenv GZIP "-8 -v"
* Added support for the Amiga.
* znew now keeps the old .Z if it is smaller than the .z file.
  This can happen for some large and very redundant files.
* Do not complain about trailing garbage for record oriented IO (Vax/VMS).
  This implies however that multi-part gzip files are not supported
  on such systems.
* Added gzexe to compress rarely used executables.
* Reduce memory usage (required for MSDOS and useful on all systems).
* Preserve time stamp in znew -P (pipe option) if touch -r works.


Major changes from 1.0 to 1.0.1
* fix trivial errors in the Borland makefile (msdos/Makefile.bor)


Major changes from 0.8.2 to 1.0
* gzip now runs on Vax/VMS
* gzip will not not overwrite files without -f when using /bin/sh in
  background.
* Support the test option -t for compressed (.Z) files.
  Allow some data recovery for bad .Z files.
* Added makefiles for MSDOS (Only tested for MSC, not Borland).
* still more changes to configure for several systems


Major changes from 0.8.1 to 0.8.2:
* yet more changes to configure for Linux and other systems
* Allow zcat on a file with multiple links.


Major changes from 0.8 to 0.8.1:
* znew has now a pipe option -P to reduce the disk space requirements,
  but this option does not preserve timestamps.
* Fixed some #if directives for compilation with TurboC.


Major changes from 0.7 to 0.8:
* gzip can now extract .z files created by 'pack'.
* configure should no longer believe that every machine is a 386
* Fix the entry for /etc/magic in INSTALL.
* Add patch for GNU tar 1.11.1 and a pointer to crypt++.el
* Uncompress files with multiple links only with -f.
* Fix for uncompress of .Z files on 16-bit machines
* Create a correct output name for file names of exactly N-1 chars when
  the system has a limit of N chars.


Major changes from 0.6 to 0.7:
* Use "make check" instead of "make test".
* Keep time stamp and pass options to gzip in znew.
* Do not create .z.z files with gzip -r.
* Allow again gunzip .zip files (was working in 0.5)
* Allow again compilation with TurboC 2.0 (was working in 0.4)


Major changes form 0.5 to 0.6:
* gunzip reported an error when extracting certain .z files. The .z files
  produced by gzip 0.5 are correct and can be read by gunzip 0.6.
* gunzip now supports multiple compressed members within a single .z file.
* Fix the check for i386 in configure.
* Added "make test" to check for compiler bugs. (gcc -finline-functions
  is broken at least on the NeXT.)
* Use environment variable PAGER in zmore if it is defined.
* Accept gzcat in addition to zcat for people having /usr/bin before
  /usr/local/bin in their path.
