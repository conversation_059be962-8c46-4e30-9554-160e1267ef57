# Makefile for gzip (GNU zip)    -*- Indented-Text -*-
# Copyright (C) 1992-1993 <PERSON><PERSON><PERSON><PERSON> and the Free Software Foundation

# Amiga GCC version, written by <PERSON> <<EMAIL>>

# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 2, or (at your option)
# any later version.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.

# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 675 Mass Ave, Cambridge, MA 02139, USA.

CC = gcc
CPP = cpp

CFLAGS = -m68020 -m68881 -O2
#CFLAGS = -m68000 -O2
#for the 68000, set below: OBJA=
CPPFLAGS = -Dmc68020
LDFLAGS = 
O = .o

SRCS = gzip.c zip.c deflate.c trees.c bits.c unzip.c inflate.c util.c crypt.c\
       lzw.c unlzw.c unpack.c unlzh.c makecrc.c getopt.c match.S

OBJA = match$O

OBJS = gzip$O zip$O deflate$O trees$O bits$O unzip$O inflate$O util$O \
       crypt$O lzw$O unlzw$O unpack$O unlzh$O getopt$O match$O

HDRS = gzip.h lzw.h tailor.h revision.h crypt.h getopt.h

.c.o:
	$(CC) -c $(DEFS) $(CFLAGS) $<

.PHONY: default all
all:	gzip

gzip: $(OBJS)
	$(CC) $(LDFLAGS) -o $@ $(OBJS) $(LIBS)

gzip$O zip$O deflate$O trees$O bits$O unzip$O inflate$O: gzip.h tailor.h
util$O lzw$O unlzw$O unpack$O unlzh$O crypt$O: gzip.h tailor.h

gzip$O unlzw$O: revision.h lzw.h

bits$O unzip$O util$O zip$O: crypt.h

gzip$O getopt$O: getopt.h

match$O: match.S
	$(CPP) $(CPPFLAGS) match.S > _match.s
	$(CC) -c _match.s
	mv _match$O match$O
	rm -f _match.s
