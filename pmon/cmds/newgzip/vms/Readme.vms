README file for gzip on VMS
written by <PERSON> <<EMAIL>>

Here is a list of the files in [.vms]:

GZIP.HLP	! Help-file, how to use gzip
MAKEFILE.GCC	! Description for (vms-)make,  using gcc
MAKEFILE.MMS	! Description for MMS-utility, using vaxc
MAKEFILE.VMS	! Description for (vms-)make,  using vaxc
MAKEGZIP.COM	! simple and robust DCL-Script, using vaxc
README.VMS	! this file, how to install gzip
VMS.C		! VMS-specific functions in gzip

This file describe, how to compile and install the VMS-Port of gzip.
>>For restrictions or bugs due to the structure of the vms-filesystem
refer to gzip.hlp.<<

Installing gzip

Since there are many makefiles there are many possibilities
compiling and installing gzip. For most of them, it is important,
that you start them from the SOURCE-Directory (that is where
gzip.c is), not from the [.vms]-directory. Otherwise the compiler
won't find the source-files.

Using gcc saves disk-space, but needs longer to compile
	VAXC	 gzip.exe 95/96 Blocks
	gcc2.3.3 gzip.exe 86/87 Blocks

To install gzip one has to perform the following steps:
  1. compile sources (*.c, [.vms]*.c)
  2. link executable gzip.exe
  3. Make entries gunzip.exe and zcat.exe to gzip.exe.
     These are similar to hardlinks in UNIX
     >>If You wish to do this by hand, see for example makegzip.com.<<
     The Entries can't be simply deleted, use  'set file /remove' .

  4. Rename executables (*.exe) to Your prefered exe-directory. If it is on
     another Device, then you should only copy gzip.exe and do the
     entries gunzip.exe and zcat.exe by hand (3.).

  5. Setting up Symbols for the executables:
     Place the following lines in Your login.com
     Change "disk:[directory]" as appropriate.
	$ gzip   == "$disk:[directory]gzip.exe"
	$ gunzip == "$disk:[directory]gunzip.exe"
	$ zcat   == "$disk:[directory]zcat.exe"
  6. insert help-file gzip.hlp in any Help-library,
     for example in sys$help:helplib.
     this can be done by $lib sys$help:helplib/help gzip
     (I did not try this command)

Steps (4.)5. through 6 must be done by hand.
Steps 1 through 3(4.) may be done by one of the makefiles:

a) MAKEGZIP.COM
This was crated from MAKEFILE.VMS and later on edited. It uses the
VAXC-Compiler and does compiling, linking and making the entries.
Usage:
- change to the Source-Directory
- copy vms.c
	$copy [.vms]vms.c []
- start compiling, linking, entries
	$@[.vms]makegzip
if everything is OK. then you get gzip.exe, gunzip.exe, zcat.exe
continue at Step 4.

b) MAKEFILE.VMS
Makefile for a vms-make utility (NOT gnu-make).
Is uses the VAXC-Compiler and has the Advantage over MAKEGZIP.COM
that make checks dependencies. This is helpfull if You have to
recompile several times, for example after editing the sources.
- change to the Source-Directory
- copy vms.c
	$copy [.vms]vms.c []
- start compiling, linking, entries
	$make /input=[.vms]makefile.vms
if everything is OK. then you get gzip.exe, gunzip.exe, zcat.exe
Continue at Step 4.

c) MAKEFILE.GCC
Same as MAKEFILE.VMS, but uses gcc
- Use $make /input=[.vms]makefile.gcc   instead of make ....

d) MAKEFILE.MMS
Makefile for the MMS-utility. Similar to MAKEFILE.VMS
- Use $mms /description=[.vms]makefile.mms  instead of make ....
