Info file gzip.info, produced by Makeinfo, -*- Text -*- from input
file gzip.texi.

   This file documents the the GNU `gzip' command for compressing
files.

   Copyright (C) 1992-1993 Jean-loup Gailly

   Permission is granted to make and distribute verbatim copies of
this manual provided the copyright notice and this permission notice
are preserved on all copies.

   Permission is granted to copy and distribute modified versions of
this manual under the conditions for verbatim copying, provided that
the entire resulting derived work is distributed under the terms of a
permission notice identical to this one.

   Permission is granted to copy and distribute translations of this
manual into another language, under the above conditions for modified
versions, except that this permission notice may be stated in a
translation approved by the Foundation.


File: gzip.info,  Node: Top,  Up: (dir)

   This file documents the `gzip' command to compress files.

* Menu:

* Copying::		How you can copy and share `gzip'.
* Overview::		Preliminary information.
* Sample::		Sample output from `gzip'.
* Invoking gzip::	How to run `gzip'.
* Advanced usage::	Concatenated files.
* Environment::		The `GZIP' environment variable
* Tapes::               Using `gzip' on tapes.
* Problems::		Reporting bugs.
* Concept Index::	Index of concepts.


File: gzip.info,  Node: Copying,  Next: Overview,  Up: Top

GNU GENERAL PUBLIC LICENSE
**************************

                             Version 2, June 1991

     Copyright (C) 1989, 1991 Free Software Foundation, Inc.
     675 Mass Ave, Cambridge, MA 02139, USA
     
     Everyone is permitted to copy and distribute verbatim copies
     of this license document, but changing it is not allowed.

Preamble
========

   The licenses for most software are designed to take away your
freedom to share and change it.  By contrast, the GNU General Public
License is intended to guarantee your freedom to share and change free
software--to make sure the software is free for all its users.  This
General Public License applies to most of the Free Software
Foundation's software and to any other program whose authors commit to
using it.  (Some other Free Software Foundation software is covered by
the GNU Library General Public License instead.)  You can apply it to
your programs, too.

   When we speak of free software, we are referring to freedom, not
price.  Our General Public Licenses are designed to make sure that you
have the freedom to distribute copies of free software (and charge for
this service if you wish), that you receive source code or can get it
if you want it, that you can change the software or use pieces of it
in new free programs; and that you know you can do these things.

   To protect your rights, we need to make restrictions that forbid
anyone to deny you these rights or to ask you to surrender the rights. 
These restrictions translate to certain responsibilities for you if you
distribute copies of the software, or if you modify it.

   For example, if you distribute copies of such a program, whether
gratis or for a fee, you must give the recipients all the rights that
you have.  You must make sure that they, too, receive or can get the
source code.  And you must show them these terms so they know their
rights.

   We protect your rights with two steps: (1) copyright the software,
and (2) offer you this license which gives you legal permission to
copy, distribute and/or modify the software.

   Also, for each author's protection and ours, we want to make certain
that everyone understands that there is no warranty for this free
software.  If the software is modified by someone else and passed on,
we want its recipients to know that what they have is not the
original, so that any problems introduced by others will not reflect
on the original authors' reputations.

   Finally, any free program is threatened constantly by software
patents.  We wish to avoid the danger that redistributors of a free
program will individually obtain patent licenses, in effect making the
program proprietary.  To prevent this, we have made it clear that any
patent must be licensed for everyone's free use or not licensed at all.

   The precise terms and conditions for copying, distribution and
modification follow.

       TERMS AND CONDITIONS FOR COPYING, DISTRIBUTION AND MODIFICATION

  1. This License applies to any program or other work which contains
     a notice placed by the copyright holder saying it may be
     distributed under the terms of this General Public License.  The
     "Program", below, refers to any such program or work, and a "work
     based on the Program" means either the Program or any derivative
     work under copyright law: that is to say, a work containing the
     Program or a portion of it, either verbatim or with modifications
     and/or translated into another language.  (Hereinafter,
     translation is included without limitation in the term
     "modification".)  Each licensee is addressed as "you".

        Activities other than copying, distribution and modification
     are not covered by this License; they are outside its scope.  The
     act of running the Program is not restricted, and the output from
     the Program is covered only if its contents constitute a work
     based on the Program (independent of having been made by running
     the Program).  Whether that is true depends on what the Program
     does.

  2. You may copy and distribute verbatim copies of the Program's
     source code as you receive it, in any medium, provided that you
     conspicuously and appropriately publish on each copy an
     appropriate copyright notice and disclaimer of warranty; keep
     intact all the notices that refer to this License and to the
     absence of any warranty; and give any other recipients of the
     Program a copy of this License along with the Program.

        You may charge a fee for the physical act of transferring a
     copy, and you may at your option offer warranty protection in
     exchange for a fee.

  3. You may modify your copy or copies of the Program or any portion
     of it, thus forming a work based on the Program, and copy and
     distribute such modifications or work under the terms of Section 1
     above, provided that you also meet all of these conditions:

       1. You must cause the modified files to carry prominent notices
          stating that you changed the files and the date of any
          change.

       2. You must cause any work that you distribute or publish, that
          in whole or in part contains or is derived from the Program
          or any part thereof, to be licensed as a whole at no charge
          to all third parties under the terms of this License.

       3. If the modified program normally reads commands interactively
          when run, you must cause it, when started running for such
          interactive use in the most ordinary way, to print or
          display an announcement including an appropriate copyright
          notice and a notice that there is no warranty (or else,
          saying that you provide a warranty) and that users may
          redistribute the program under these conditions, and telling
          the user how to view a copy of this License.  (Exception: if
          the Program itself is interactive but does not normally
          print such an announcement, your work based on the Program
          is not required to print an announcement.)

             These requirements apply to the modified work as a whole.  If
     identifiable sections of that work are not derived from the
     Program, and can be reasonably considered independent and
     separate works in themselves, then this License, and its terms,
     do not apply to those sections when you distribute them as
     separate works.  But when you distribute the same sections as
     part of a whole which is a work based on the Program, the
     distribution of the whole must be on the terms of this License,
     whose permissions for other licensees extend to the entire whole,
     and thus to each and every part regardless of who wrote it.

        Thus, it is not the intent of this section to claim rights or
     contest your rights to work written entirely by you; rather, the
     intent is to exercise the right to control the distribution of
     derivative or collective works based on the Program.

        In addition, mere aggregation of another work not based on the
     Program with the Program (or with a work based on the Program) on
     a volume of a storage or distribution medium does not bring the
     other work under the scope of this License.

  4. You may copy and distribute the Program (or a work based on it,
     under Section 2) in object code or executable form under the
     terms of Sections 1 and 2 above provided that you also do one of
     the following:

       1. Accompany it with the complete corresponding machine-readable
          source code, which must be distributed under the terms of
          Sections 1 and 2 above on a medium customarily used for
          software interchange; or,

       2. Accompany it with a written offer, valid for at least three
          years, to give any third party, for a charge no more than
          your cost of physically performing source distribution, a
          complete machine-readable copy of the corresponding source
          code, to be distributed under the terms of Sections 1 and 2
          above on a medium customarily used for software interchange;
          or,

       3. Accompany it with the information you received as to the
          offer to distribute corresponding source code.  (This
          alternative is allowed only for noncommercial distribution
          and only if you received the program in object code or
          executable form with such an offer, in accord with
          Subsection b above.)

             The source code for a work means the preferred form of the
     work for making modifications to it.  For an executable work,
     complete source code means all the source code for all modules it
     contains, plus any associated interface definition files, plus
     the scripts used to control compilation and installation of the
     executable.  However, as a special exception, the source code
     distributed need not include anything that is normally
     distributed (in either source or binary form) with the major
     components (compiler, kernel, and so on) of the operating system
     on which the executable runs, unless that component itself
     accompanies the executable.

        If distribution of executable or object code is made by
     offering access to copy from a designated place, then offering
     equivalent access to copy the source code from the same place
     counts as distribution of the source code, even though third
     parties are not compelled to copy the source along with the
     object code.

  5. You may not copy, modify, sublicense, or distribute the Program
     except as expressly provided under this License.  Any attempt
     otherwise to copy, modify, sublicense or distribute the Program is
     void, and will automatically terminate your rights under this
     License.  However, parties who have received copies, or rights,
     from you under this License will not have their licenses
     terminated so long as such parties remain in full compliance.

  6. You are not required to accept this License, since you have not
     signed it.  However, nothing else grants you permission to modify
     or distribute the Program or its derivative works.  These actions
     are prohibited by law if you do not accept this License. 
     Therefore, by modifying or distributing the Program (or any work
     based on the Program), you indicate your acceptance of this
     License to do so, and all its terms and conditions for copying,
     distributing or modifying the Program or works based on it.

  7. Each time you redistribute the Program (or any work based on the
     Program), the recipient automatically receives a license from the
     original licensor to copy, distribute or modify the Program
     subject to these terms and conditions.  You may not impose any
     further restrictions on the recipients' exercise of the rights
     granted herein.  You are not responsible for enforcing compliance
     by third parties to this License.

  8. If, as a consequence of a court judgment or allegation of patent
     infringement or for any other reason (not limited to patent
     issues), conditions are imposed on you (whether by court order,
     agreement or otherwise) that contradict the conditions of this
     License, they do not excuse you from the conditions of this
     License.  If you cannot distribute so as to satisfy
     simultaneously your obligations under this License and any other
     pertinent obligations, then as a consequence you may not
     distribute the Program at all.  For example, if a patent license
     would not permit royalty-free redistribution of the Program by
     all those who receive copies directly or indirectly through you,
     then the only way you could satisfy both it and this License
     would be to refrain entirely from distribution of the Program.

        If any portion of this section is held invalid or
     unenforceable under any particular circumstance, the balance of
     the section is intended to apply and the section as a whole is
     intended to apply in other circumstances.

        It is not the purpose of this section to induce you to
     infringe any patents or other property right claims or to contest
     validity of any such claims; this section has the sole purpose of
     protecting the integrity of the free software distribution
     system, which is implemented by public license practices.  Many
     people have made generous contributions to the wide range of
     software distributed through that system in reliance on
     consistent application of that system; it is up to the
     author/donor to decide if he or she is willing to distribute
     software through any other system and a licensee cannot impose
     that choice.

        This section is intended to make thoroughly clear what is
     believed to be a consequence of the rest of this License.

  9. If the distribution and/or use of the Program is restricted in
     certain countries either by patents or by copyrighted interfaces,
     the original copyright holder who places the Program under this
     License may add an explicit geographical distribution limitation
     excluding those countries, so that distribution is permitted only
     in or among countries not thus excluded.  In such case, this
     License incorporates the limitation as if written in the body of
     this License.

 10. The Free Software Foundation may publish revised and/or new
     versions of the General Public License from time to time.  Such
     new versions will be similar in spirit to the present version,
     but may differ in detail to address new problems or concerns.

        Each version is given a distinguishing version number.  If the
     Program specifies a version number of this License which applies
     to it and "any later version", you have the option of following
     the terms and conditions either of that version or of any later
     version published by the Free Software Foundation.  If the
     Program does not specify a version number of this License, you
     may choose any version ever published by the Free Software
     Foundation.

 11. If you wish to incorporate parts of the Program into other free
     programs whose distribution conditions are different, write to
     the author to ask for permission.  For software which is
     copyrighted by the Free Software Foundation, write to the Free
     Software Foundation; we sometimes make exceptions for this.  Our
     decision will be guided by the two goals of preserving the free
     status of all derivatives of our free software and of promoting
     the sharing and reuse of software generally.

                                      NO WARRANTY

 12. BECAUSE THE PROGRAM IS LICENSED FREE OF CHARGE, THERE IS NO
     WARRANTY FOR THE PROGRAM, TO THE EXTENT PERMITTED BY APPLICABLE
     LAW.  EXCEPT WHEN OTHERWISE STATED IN WRITING THE COPYRIGHT
     HOLDERS AND/OR OTHER PARTIES PROVIDE THE PROGRAM "AS IS" WITHOUT
     WARRANTY OF ANY KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING, BUT
     NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
     FITNESS FOR A PARTICULAR PURPOSE.  THE ENTIRE RISK AS TO THE
     QUALITY AND PERFORMANCE OF THE PROGRAM IS WITH YOU.  SHOULD THE
     PROGRAM PROVE DEFECTIVE, YOU ASSUME THE COST OF ALL NECESSARY
     SERVICING, REPAIR OR CORRECTION.

 13. IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN
     WRITING WILL ANY COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MAY
     MODIFY AND/OR REDISTRIBUTE THE PROGRAM AS PERMITTED ABOVE, BE
     LIABLE TO YOU FOR DAMAGES, INCLUDING ANY GENERAL, SPECIAL,
     INCIDENTAL OR CONSEQUENTIAL DAMAGES ARISING OUT OF THE USE OR
     INABILITY TO USE THE PROGRAM (INCLUDING BUT NOT LIMITED TO LOSS
     OF DATA OR DATA BEING RENDERED INACCURATE OR LOSSES SUSTAINED BY
     YOU OR THIRD PARTIES OR A FAILURE OF THE PROGRAM TO OPERATE WITH
     ANY OTHER PROGRAMS), EVEN IF SUCH HOLDER OR OTHER PARTY HAS BEEN
     ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.

                         END OF TERMS AND CONDITIONS

How to Apply These Terms to Your New Programs
=============================================

   If you develop a new program, and you want it to be of the greatest
possible use to the public, the best way to achieve this is to make it
free software which everyone can redistribute and change under these
terms.

   To do so, attach the following notices to the program.  It is safest
to attach them to the start of each source file to most effectively
convey the exclusion of warranty; and each file should have at least
the "copyright" line and a pointer to where the full notice is found.

     ONE LINE TO GIVE THE PROGRAM'S NAME AND AN IDEA OF WHAT IT DOES.
     Copyright (C) 19YY  NAME OF AUTHOR
     
     This program is free software; you can redistribute it and/or
     modify it under the terms of the GNU General Public License
     as published by the Free Software Foundation; either version 2
     of the License, or (at your option) any later version.
     
     This program is distributed in the hope that it will be useful,
     but WITHOUT ANY WARRANTY; without even the implied warranty of
     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
     GNU General Public License for more details.
     
     You should have received a copy of the GNU General Public License
     along with this program; if not, write to the Free Software
     Foundation, Inc., 675 Mass Ave, Cambridge, MA 02139, USA.

   Also add information on how to contact you by electronic and paper
mail.

   If the program is interactive, make it output a short notice like
this when it starts in an interactive mode:

     Gnomovision version 69, Copyright (C) 19YY NAME OF AUTHOR
     Gnomovision comes with ABSOLUTELY NO WARRANTY; for details
     type `show w'.  This is free software, and you are welcome
     to redistribute it under certain conditions; type `show c' 
     for details.

   The hypothetical commands `show w' and `show c' should show the
appropriate parts of the General Public License.  Of course, the
commands you use may be called something other than `show w' and `show
c'; they could even be mouse-clicks or menu items--whatever suits your
program.

   You should also get your employer (if you work as a programmer) or
your school, if any, to sign a "copyright disclaimer" for the program,
if necessary.  Here is a sample; alter the names:

     Yoyodyne, Inc., hereby disclaims all copyright
     interest in the program `Gnomovision'
     (which makes passes at compilers) written 
     by James Hacker.
     
     SIGNATURE OF TY COON, 1 April 1989
     Ty Coon, President of Vice

   This General Public License does not permit incorporating your
program into proprietary programs.  If your program is a subroutine
library, you may consider it more useful to permit linking proprietary
applications with the library.  If this is what you want to do, use
the GNU Library General Public License instead of this License.


File: gzip.info,  Node: Overview,  Next: Sample,  Prev: Copying,  Up: Top

Overview
********

   `gzip' reduces the size of the named files using Lempel-Ziv coding
(LZ77).  Whenever possible, each file is replaced by one with the
extension `.gz', while keeping the same ownership modes, access and
modification times.  (The default extension is `-gz' for VMS, `z' for
MSDOS, OS/2 FAT and Atari.)  If no files are specified or if a file
name is "-", the standard input is compressed to the standard output.
`gzip' will only attempt to compress regular files.  In particular, it
will ignore symbolic links.

   If the new file name is too long for its file system, `gzip'
truncates it.  `gzip' attempts to truncate only the parts of the file
name longer than 3 characters.  (A part is delimited by dots.) If the
name consists of small parts only, the longest parts are truncated. 
For example, if file names are limited to 14 characters, gzip.msdos.exe
is compressed to gzi.msd.exe.gz.  Names are not truncated on systems
which do not have a limit on file name length.

   By default, `gzip' keeps the original file name and timestamp in
the compressed file. These are used when decompressing the file with
the `-N' option. This is useful when the compressed file name was
truncated or when the time stamp was not preserved after a file
transfer.

   Compressed files can be restored to their original form using `gzip
-d' or `gunzip' or `zcat'.  If the original name saved in the
compressed file is not suitable for its file system, a new name is
constructed from the original one to make it legal.

   `gunzip' takes a list of files on its command line and replaces
each file whose name ends with `.gz', `.z', `.Z', `-gz', `-z' or `_z'
and which begins with the correct magic number with an uncompressed
file without the original extension.  `gunzip' also recognizes the
special extensions `.tgz' and `.taz' as shorthands for `.tar.gz' and
`.tar.Z' respectively. When compressing, `gzip' uses the `.tgz'
extension if necessary instead of truncating a file with a `.tar'
extension.

   `gunzip' can currently decompress files created by `gzip', `zip',
`compress' or `pack'. The detection of the input format is automatic. 
When using the first two formats, `gunzip' checks a 32 bit CRC (cyclic
redundancy check). For `pack', `gunzip' checks the uncompressed
length. The `compress' format was not designed to allow consistency
checks. However `gunzip' is sometimes able to detect a bad `.Z' file.
If you get an error when uncompressing a `.Z' file, do not assume that
the `.Z' file is correct simply because the standard `uncompress' does
not complain.  This generally means that the standard `uncompress'
does not check its input, and happily generates garbage output.  The
SCO `compress -H' format (`lzh' compression method) does not include a
CRC but also allows some consistency checks.

   Files created by `zip' can be uncompressed by `gzip' only if they
have a single member compressed with the 'deflation' method. This
feature is only intended to help conversion of `tar.zip' files to the
`tar.gz' format. To extract `zip' files with several members, use
`unzip' instead of `gunzip'.

   `zcat' is identical to `gunzip -c'.  `zcat' uncompresses either a
list of files on the command line or its standard input and writes the
uncompressed data on standard output.  `zcat' will uncompress files
that have the correct magic number whether they have a `.gz' suffix or
not.

   `gzip' uses the Lempel-Ziv algorithm used in `zip' and PKZIP.  The
amount of compression obtained depends on the size of the input and
the distribution of common substrings.  Typically, text such as source
code or English is reduced by 60-70%.  Compression is generally much
better than that achieved by LZW (as used in `compress'), Huffman
coding (as used in `pack'), or adaptive Huffman coding (`compact').

   Compression is always performed, even if the compressed file is
slightly larger than the original. The worst case expansion is a few
bytes for the `gzip' file header, plus 5 bytes every 32K block, or an
expansion ratio of 0.015% for large files. Note that the actual number
of used disk blocks almost never increases. `gzip' preserves the mode,
ownership and timestamps of files when compressing or decompressing.


File: gzip.info,  Node: Sample,  Next: Invoking gzip,  Prev: Overview,  Up: Top

Sample Output
*************

   Here are some realistic examples of running `gzip'.

   This is the output of the command `gzip -h':

     gzip 1.2.4 (18 Aug 93)
     usage: gzip [-cdfhlLnNrtvV19] [-S suffix] [file ...]
      -c --stdout      write on standard output, keep original files unchanged
      -d --decompress  decompress
      -f --force       force overwrite of output file and compress links
      -h --help        give this help
      -l --list        list compressed file contents
      -L --license     display software license
      -n --no-name     do not save or restore the original name and time stamp
      -N --name        save or restore the original name and time stamp
      -q --quiet       suppress all warnings
      -r --recursive   operate recursively on directories
      -S .suf  --suffix .suf     use suffix .suf on compressed files
      -t --test        test compressed file integrity
      -v --verbose     verbose mode
      -V --version     display version number
      -1 --fast        compress faster
      -9 --best        compress better
      file...          files to (de)compress. If none given, use standard input.

   This is the output of the command `gzip -v texinfo.tex':

     texinfo.tex:             71.6% -- replaced with texinfo.tex.gz

   The following command will find all `gzip' files in the current
directory and subdirectories, and extract them in place without
destroying the original:

     find . -name '*.gz' -print | sed 's/^\(.*\)[.]gz$/gunzip < "&" > "\1"/' | sh


File: gzip.info,  Node: Invoking gzip,  Next: Advanced usage,  Prev: Sample,  Up: Top

Invoking `gzip'
***************

   The format for running the `gzip' program is:

     gzip OPTION ...

   `gzip' supports the following options:

`--stdout'
`--to-stdout'
`-c'
     Write output on standard output; keep original files unchanged. 
     If there are several input files, the output consists of a
     sequence of independently compressed members. To obtain better
     compression, concatenate all input files before compressing them.

`--decompress'
`--uncompress'
`-d'
     Decompress.

`--force'
`-f'
     Force compression or decompression even if the file has multiple
     links or the corresponding file already exists, or if the
     compressed data is read from or written to a terminal. If the
     input data is not in a format recognized by `gzip', and if the
     option --stdout is also given, copy the input data without change
     to the standard ouput: let `zcat' behave as `cat'. If `-f' is not
     given, and when not running in the background, `gzip' prompts to
     verify whether an existing file should be overwritten.

`--help'
`-h'
     Print an informative help message describing the options then
     quit.

`--list'
`-l'
     For each compressed file, list the following fields:

          compressed size: size of the compressed file
          uncompressed size: size of the uncompressed file
          ratio: compression ratio (0.0% if unknown)
          uncompressed_name: name of the uncompressed file

          The uncompressed size is given as `-1' for files not in `gzip'
     format, such as compressed `.Z' files. To get the uncompressed
     size for such a file, you can use:

          zcat file.Z | wc -c

          In combination with the --verbose option, the following fields
     are also displayed:

          method: compression method (deflate,compress,lzh,pack)
          crc: the 32-bit CRC of the uncompressed data
          date & time: time stamp for the uncompressed file

          The crc is given as ffffffff for a file not in gzip format.

     With --verbose, the size totals and compression ratio for all
     files is also displayed, unless some sizes are unknown. With
     --quiet, the title and totals lines are not displayed.

`--license'
`-L'
     Display the `gzip' license then quit.

`--no-name'
`-n'
     When compressing, do not save the original file name and time
     stamp by default. (The original name is always saved if the name
     had to be truncated.) When decompressing, do not restore the
     original file name if present (remove only the `gzip' suffix from
     the compressed file name) and do not restore the original time
     stamp if present (copy it from the compressed file). This option
     is the default when decompressing.

`--name'
`-N'
     When compressing, always save the original file name and time
     stamp; this is the default. When decompressing, restore the
     original file name and time stamp if present. This option is
     useful on systems which have a limit on file name length or when
     the time stamp has been lost after a file transfer.

`--quiet'
`-q'
     Suppress all warning messages.

`--recursive'
`-r'
     Travel the directory structure recursively. If any of the file
     names specified on the command line are directories, `gzip' will
     descend into the directory and compress all the files it finds
     there (or decompress them in the case of `gunzip').

`--suffix SUF'
`-S SUF'
     Use suffix `SUF' instead of `.gz'. Any suffix can be given, but
     suffixes other than `.z' and `.gz' should be avoided to avoid
     confusion when files are transferred to other systems.  A null
     suffix forces gunzip to try decompression on all given files
     regardless of suffix, as in:

          gunzip -S "" *        (*.* for MSDOS)

          Previous versions of gzip used the `.z' suffix. This was changed
     to avoid a conflict with `pack'.

`--test'
`-t'
     Test. Check the compressed file integrity.

`--verbose'
`-v'
     Verbose. Display the name and percentage reduction for each file
     compressed.

`--version'
`-V'
     Version. Display the version number and compilation options, then
     quit.

`--fast'
`--best'
`-N'
     Regulate the speed of compression using the specified digit N,
     where `-1' or `--fast' indicates the fastest compression method
     (less compression) and `--best' or `-9' indicates the slowest
     compression method (optimal compression).  The default
     compression level is `-6' (that is, biased towards high
     compression at expense of speed).


File: gzip.info,  Node: Advanced usage,  Next: Environment,  Prev: Invoking gzip,  Up: Top

Advanced usage
**************

   Multiple compressed files can be concatenated. In this case,
`gunzip' will extract all members at once. If one member is damaged,
other members might still be recovered after removal of the damaged
member. Better compression can be usually obtained if all members are
decompressed and then recompressed in a single step.

   This is an example of concatenating `gzip' files:

     gzip -c file1  > foo.gz
     gzip -c file2 >> foo.gz

   Then

     gunzip -c foo

   is equivalent to

     cat file1 file2

   In case of damage to one member of a `.gz' file, other members can
still be recovered (if the damaged member is removed). However, you
can get better compression by compressing all members at once:

     cat file1 file2 | gzip > foo.gz

   compresses better than

     gzip -c file1 file2 > foo.gz

   If you want to recompress concatenated files to get better
compression, do:

     zcat old.gz | gzip > new.gz

   If a compressed file consists of several members, the uncompressed
size and CRC reported by the `--list' option applies to the last member
only. If you need the uncompressed size for all members, you can use:

     zcat file.gz | wc -c

   If you wish to create a single archive file with multiple members so
that members can later be extracted independently, use an archiver such
as `tar' or `zip'. GNU `tar' supports the `-z' option to invoke `gzip'
transparently. `gzip' is designed as a complement to `tar', not as a
replacement.


File: gzip.info,  Node: Environment,  Next: Tapes,  Prev: Advanced usage,  Up: Top

Environment
***********

   The environment variable `GZIP' can hold a set of default options
for `gzip'.  These options are interpreted first and can be
overwritten by explicit command line parameters.  For example:

     for sh:    GZIP="-8v --name"; export GZIP
     for csh:   setenv GZIP "-8v --name"
     for MSDOS: set GZIP=-8v --name

   On Vax/VMS, the name of the environment variable is `GZIP_OPT', to
avoid a conflict with the symbol set for invocation of the program.


File: gzip.info,  Node: Tapes,  Next: Problems,  Prev: Environment,  Up: Top

Using `gzip' on tapes
*********************

   When writing compressed data to a tape, it is generally necessary
to pad the output with zeroes up to a block boundary. When the data is
read and the whole block is passed to `gunzip' for decompression,
`gunzip' detects that there is extra trailing garbage after the
compressed data and emits a warning by default. You have to use the
`--quiet' option to suppress the warning. This option can be set in the
`GZIP' environment variable, as in:

     for sh:    GZIP="-q"  tar -xfz --block-compress /dev/rst0
     for csh:   (setenv GZIP "-q"; tar -xfz --block-compress /dev/rst0)

   In the above example, `gzip' is invoked implicitly by the `-z'
option of GNU `tar'.  Make sure that the same block size (`-b' option
of `tar') is used for reading and writing compressed data on tapes. 
(This example assumes you are using the GNU version of `tar'.)


File: gzip.info,  Node: Problems,  Next: Concept Index,  Prev: Tapes,  Up: Top

Reporting Bugs
**************

   If you find a bug in `gzip', please send electronic mail to
`<EMAIL>' or, if this fails, to
`<EMAIL>'.  Include the version number, which
you can find by running `gzip -V'.  Also include in your message the
hardware and operating system, the compiler used to compile `gzip', a
description of the bug behavior, and the input to `gzip' that triggered
the bug.


File: gzip.info,  Node: Concept Index,  Prev: Problems,  Up: Top

Concept Index
*************

* Menu:

* Environment:                          Environment.
* bugs:                                 Problems.
* concatenated files:                   Advanced usage.
* invoking:                             Invoking gzip.
* options:                              Invoking gzip.
* overview:                             Overview.
* sample:                               Sample.
* tapes:                                Tapes.



Tag Table:
Node: Top864
Node: Copying1344
Node: Overview20602
Node: Sample24911
Node: Invoking gzip26528
Node: Advanced usage31183
Node: Environment32772
Node: Tapes33340
Node: Problems34317
Node: Concept Index34822

End Tag Table
