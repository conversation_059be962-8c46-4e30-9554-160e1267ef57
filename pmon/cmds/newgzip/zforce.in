:
#!/bin/sh
# zforce: force a gz extension on all gzip files so that gzip will not
# compress them twice.
#
# This can be useful for files with names truncated after a file transfer.
# 12345678901234 is renamed to 12345678901.gz

PATH="BINDIR:$PATH"; export PATH
x=`basename $0`
if test $# = 0; then
  echo "force a '.gz' extension on all gzip files"
  echo usage: $x files...
  exit 1
fi

res=0
for i do
  if test ! -f "$i" ; then
    echo ${x}: $i not a file
    res=1
    continue
  fi
  test `expr "$i" : '.*[.-]z$'` -eq 0 || continue
  test `expr "$i" : '.*[.-]gz$'` -eq 0 || continue
  test `expr "$i" : '.*[.]t[ag]z$'` -eq 0 || continue

  if gzip -l < "$i" 2>/dev/null | grep '^defl' > /dev/null; then

    if test `expr "$i" : '^............'` -eq 12; then
      new=`expr "$i" : '\(.*\)...$`.gz
    else
      new="$i.gz"
    fi
    if mv "$i" "$new" 2>/dev/null; then
      echo $i -- replaced with $new
      continue
    fi
    res=1; echo ${x}: cannot rename $i to $new
  fi
done
exit $res
