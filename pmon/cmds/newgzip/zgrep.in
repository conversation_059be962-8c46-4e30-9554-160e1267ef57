:
#!/bin/sh

# zgrep -- a wrapper around a grep program that decompresses files as needed
# Adapted from a version sent by <PERSON> <<EMAIL>>

PATH="BINDIR:$PATH"; export PATH

prog=`echo $0 | sed 's|.*/||'`
case "$prog" in
	*egrep)	grep=${EGREP-egrep}	;;
	*fgrep)	grep=${FGREP-fgrep}	;;
	*)	grep=${GREP-grep}	;;
esac
pat=""
while test $# -ne 0; do
  case "$1" in
  -e | -f) opt="$opt $1"; shift; pat="$1"
           if test "$grep" = grep; then  # grep is buggy with -e on SVR4
             grep=egrep
           fi;;
  -*)	   opt="$opt $1";;
   *)      if test -z "$pat"; then
	     pat="$1"
	   else
	     break;
           fi;;
  esac
  shift
done

if test -z "$pat"; then
  echo "grep through gzip files"
  echo "usage: $prog [grep_options] pattern [files]"
  exit 1
fi

list=0
silent=0
op=`echo "$opt" | sed -e 's/ //g' -e 's/-//g'`
case "$op" in
  *l*) list=1
esac
case "$op" in
  *h*) silent=1
esac

if test $# -eq 0; then
  gzip -cdfq | $grep $opt "$pat"
  exit $?
fi

res=0
for i do
  if test $list -eq 1; then
    gzip -cdfq "$i" | $grep $opt "$pat" > /dev/null && echo $i
    r=$?
  elif test $# -eq 1 -o $silent -eq 1; then
    gzip -cdfq "$i" | $grep $opt "$pat"
    r=$?
  else
    gzip -cdfq "$i" | $grep $opt "$pat" | sed "s|^|${i}:|"
    r=$?
  fi
  test "$r" -ne 0 && res="$r"
done
exit $res
