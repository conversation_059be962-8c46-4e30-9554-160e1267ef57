/* Not copyrighted 1990 <PERSON> */

#ifndef lint
static char rcsid[] = "$Id: makecrc.c,v ******* 2006/09/14 01:59:08 root Exp $";
#endif

#include <stdio.h>

main()
/*
  Generate a table for a byte-wise 32-bit CRC calculation on the polynomial:
  x^32+x^26+x^23+x^22+x^16+x^12+x^11+x^10+x^8+x^7+x^5+x^4+x^2+x+1.

  Polynomials over GF(2) are represented in binary, one bit per coefficient,
  with the lowest powers in the most significant bit.  Then adding polynomials
  is just exclusive-or, and multiplying a polynomial by x is a right shift by
  one.  If we call the above polynomial p, and represent a byte as the
  polynomial q, also with the lowest power in the most significant bit (so the
  byte 0xb1 is the polynomial x^7+x^3+x+1), then the CRC is (q*x^32) mod p,
  where a mod b means the remainder after dividing a by b.

  This calculation is done using the shift-register method of multiplying and
  taking the remainder.  The register is initialized to zero, and for each
  incoming bit, x^32 is added mod p to the register if the bit is a one (where
  x^32 mod p is p+x^32 = x^26+...+1), and the register is multiplied mod p by
  x (which is shifting right by one and adding x^32 mod p if the bit shifted
  out is a one).  We start with the highest power (least significant bit) of
  q and repeat for all eight bits of q.

  The table is simply the CRC of all possible eight bit values.  This is all
  the information needed to generate CRC's on data a byte at a time for all
  combinations of CRC register values and incoming bytes.  The table is
  written to stdout as 256 long hexadecimal values in C language format.
*/
{
  unsigned long c;      /* crc shift register */
  unsigned long e;      /* polynomial exclusive-or pattern */
  int i;                /* counter for all possible eight bit values */
  int k;                /* byte being shifted into crc apparatus */

  /* terms of polynomial defining this crc (except x^32): */
  static int p[] = {0,1,2,4,5,7,8,10,11,12,16,22,23,26};

  /* Make exclusive-or pattern from polynomial (0xedb88320) */
  e = 0;
  for (i = 0; i < sizeof(p)/sizeof(int); i++)
    e |= 1L << (31 - p[i]);

  /* Compute and print table of CRC's, five per line */
  printf("  0x00000000L");
  for (i = 1; i < 256; i++)
  {
    c = i;
    /* The idea to initialize the register with the byte instead of
     * zero was stolen from Haruhiko Okumura's ar002
     */
    for (k = 8; k; k--)
      c = c & 1 ? (c >> 1) ^ e : c >> 1;
    printf(i % 5 ? ", 0x%08lxL" : ",\n  0x%08lxL", c);
  }
  putchar('\n');
  return 0;
}
