# Simple makefile for Windows NT
# Written by <PERSON> <<EMAIL>>

# This is free software; you can redistribute it and/or modify it under the
# terms of the GNU General Public License, see the file COPYING.

# This makefile is suitable for NTFS only. To build a gzip executable suitable
# for a FAT file system, add -DNTFAT to DEFS.

# To build debug version, define environment variable DEBUG, or include a
# -DDEBUG on the commandline (i.e.: nmake -DDEBUG)

.silent:

!include <ntwin32.mak>

#
# Object files
#
OBJS = gzip.obj zip.obj deflate.obj trees.obj bits.obj unzip.obj inflate.obj \
	util.obj crypt.obj lzw.obj unlzw.obj unpack.obj getopt.obj unlzh.obj

DEFS =
# for FAT support, set: DEFS = -DNTFAT

#
# How to build .obj's from .c's
#
.c.obj:
	$(cc) $(DEFS) -Ox $(cflags) $(cvarsdll) $<

#
# Main target
#
all: gzip.exe

#
# Link target. setargv.obj is provided in the compiler library directory.
#
gzip.exe: $(OBJS)
	$(link) $(linkdebug) $(conflags) -out:gzip.exe $(OBJS) setargv.obj \
	  $(conlibsdll)

#
# Dependencies
#
gzip.obj:      gzip.c gzip.h tailor.h
zip.obj:       zip.c gzip.h tailor.h crypt.h
deflate.obj:   deflate.c gzip.h tailor.h
trees.obj:     trees.c gzip.h tailor.h
bits.obj:      bits.c gzip.h tailor.h crypt.h
unzip.obj:     unzip.c gzip.h tailor.h crypt.h
inflate.obj:   inflate.c gzip.h tailor.h crypt.h
util.obj:      util.c gzip.h tailor.h
lzw.obj:       lzw.c gzip.h tailor.h
unlzw.obj:     unlzw.c gzip.h tailor.h revision.h lzw.h
unpack.obj:    unpack.c gzip.h tailor.h
crypt.obj:     crypt.c gzip.h tailor.h
unlzh.obj:     unlzh.c gzip.h tailor.h lzw.h


clean:
	-rm $(OBJS)

clobber: clean
	-rm gzip.exe
