ifndef S
S:=$(shell cd ../../../../..; pwd)
endif
TARGETIN := $(value TARGETIN)
TARGETEL := $(value TARGETELIN)
TARGET=mem_config.a

all: hunt
	cp binary/$(TARGET) ../../../../../Targets/${TARGETIN}/compile/${TARGETEL}/
	cp binary/$(TARGET) $S/zloader/

hunt:
ifneq ($(wildcard source), )
	make all -C source -e TARGET=${TARGETIN}
endif

clean:
ifneq ($(wildcard source), )
	make clean -C source  -e TARGET=${TARGETIN}
endif
