#!/bin/bash

#export PATH=/opt/loongarch_toolchain/bin:$PATH
#export PATH=/opt/loongson-gnu-toolchain-8.3.2k2100-x86_64-loongarch64-linux-gnu-rc1.1a/bin:$PATH
export PATH=/opt/loongson-gnu-toolchain-8.3-x86_64-loongarch64-linux-gnu-rc1.2/bin:$PATH
#export PATH=/opt/loongarch64-linux-gnu-2021-06-19-vector/bin:$PATH

make clean
cd zloader.ls2k/
make ls2p300 -j8
make cfg all
make  tgt=rom ARCH=loongarch CROSS_COMPILE=loongarch64-linux-gnu- DEBUG=-g
#make cfg 
#make all tgt=rom  ARCH=loongarch CROSS_COMPILE=loongarch64-linux-gnu-  DEBUG=-g
#make all tgt=rom ARCH=loongarch CROSS_COMPILE=loongarch64-linux-gnu- DEBUG=-g

#make dtb ARCH=loongarch CROSS_COMPILE=loongarch64-linux-gnu-
