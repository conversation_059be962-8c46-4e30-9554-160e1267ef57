#include <pmon.h>
#include <stdio.h>

#define SCL0	1
#define	SDA0	0

#define	LOW	0
#define HIGH	1

#define	OUT	0
#define	IN	1

//static unsigned long gmac_gpio_val_base = PHYS_TO_UNCACHED(0x1fe00508);
//static unsigned long gmac_gpio_dir_base = PHYS_TO_UNCACHED(0x1fe00500);

static unsigned long gmac_gpio_val_base = PHYS_TO_UNCACHED(0x100e0010);
static unsigned long gmac_gpio_dir_base = PHYS_TO_UNCACHED(0x100e0000);
int mdio_sda = SDA0;
int mdio_scl = SCL0;

void gmac_set_gpio_val(int gpio, int val)
{
	if (val == 1)
		*(volatile unsigned int *)gmac_gpio_val_base |= (1U << gpio);
	else
		*(volatile unsigned int *)gmac_gpio_val_base &= ~(1U << gpio);
}

unsigned int gmac_get_gpio_val(int gpio)
{
	return (*(volatile unsigned int *)(gmac_gpio_val_base + 0x10) & (1U << gpio)) ? 1 : 0;
}

void set_gpio_direction(unsigned int gpio, int val)
{
	if (val == 1)
		*(volatile unsigned int *)gmac_gpio_dir_base |= (1 << gpio);
	else
		*(volatile unsigned int *)gmac_gpio_dir_base &= ~(1 << gpio);
}

void gmac_gpio_write_byte(unsigned char c,unsigned char z)
{
	int i;

	set_gpio_direction(mdio_sda, OUT);

	for(i = 7; i >= 0; i--) {
		gmac_set_gpio_val(mdio_scl, LOW);
		delay(5);
//		if (z && i == 1)
//			set_gpio_direction(mdio_sda, IN);
//		else
			gmac_set_gpio_val(mdio_sda, (c & (1 << i)) ? 1 : 0);
		delay(5);
		gmac_set_gpio_val(mdio_scl, HIGH);
		delay(10);
//		if (z && i == 1)
//			set_gpio_direction(mdio_sda, OUT);
	}

	delay(10);
}

void gmac_gpio_read_byte(unsigned char *c)
{
	int i;

	set_gpio_direction(mdio_sda, IN);
	for (i = 7; i >= 0; i--) {
		gmac_set_gpio_val(mdio_scl, HIGH);
		delay(10);
		*c = (*c << 1) | gmac_get_gpio_val(mdio_sda);
		gmac_set_gpio_val(mdio_scl, LOW);
		delay(10);
	}
}

unsigned short gmac_mdio_read(unsigned int phy_addr,unsigned int reg_offset)
{
	unsigned short data, data_h = 0, data_l = 0;

	set_gpio_direction(mdio_scl, OUT);
	// preamble 32bits 1
	gmac_gpio_write_byte(0xff, 0);
	gmac_gpio_write_byte(0xff, 0);
	gmac_gpio_write_byte(0xff, 0);
	gmac_gpio_write_byte(0xff, 0);
	// start 2bits 01 op code 10 2bits PHYAD A4-A1
	gmac_gpio_write_byte(0x60 | (phy_addr >> 1), 0);
	// PHYAD A0 REGAD 5bits R4-R0 turn around 2bits z0
	gmac_gpio_write_byte((((phy_addr & 1) << 7) | reg_offset << 2), 1);

	gmac_gpio_read_byte(&data_h);
	gmac_gpio_read_byte(&data_l);
	data = (data_h << 8) | data_l;

	//printf(" - R- - %x %x -> %x\n", phy_addr, reg_offset, data);
	//printf(" - R- - %x %x\n", data_h, data_l);

	return data;
}
void gmac_mdio_write(unsigned int phy_addr,unsigned int reg_offset, unsigned short data)
{
	set_gpio_direction(mdio_scl, OUT);
	// preamble 32bits 1
	gmac_gpio_write_byte(0xff, 0);
	gmac_gpio_write_byte(0xff, 0);
	gmac_gpio_write_byte(0xff, 0);
	gmac_gpio_write_byte(0xff, 0);
	// start 2bits 01 op code 01 2bits PHYAD A4-A1
	gmac_gpio_write_byte(0x50 | (phy_addr >> 1),0);
	// PHYAD A0 REGAD 5bits R4-R0 turn around 2bits 10
	gmac_gpio_write_byte(((phy_addr & 1) << 7) | (reg_offset << 2) | 2,0);

	gmac_gpio_write_byte(data >> 8,0);
	gmac_gpio_write_byte(data & 0xff,0);
}

static void gmac_gpio_write(ac, av)
    int ac;
    char *av[];
{
	unsigned char offset = (unsigned char)strtoul(av[1], 0, 0);
	unsigned short data = (unsigned short)strtoul(av[2], 0, 0);

	gmac_mdio_write(0, offset, data);

	printf("- - > done!\n");
}

static void gmac_gpio_read(ac, av)
    int ac;
    char *av[];
{
	unsigned char offset = (unsigned char)strtoul(av[1], 0, 0);
	unsigned short data;

	data = gmac_mdio_read(0, offset);

	printf("- - > %x\n", data);
}

static const Cmd Cmds[] = {
	{"Misc"},
	{"mdio_r", "mdio", 0, "mdio", gmac_gpio_read, 1, 5, 0},
	{"mdio_w", "mdio", 0, "mdio", gmac_gpio_write, 1, 5, 0},
	{0, 0}
};

static void init_cmd __P((void)) __attribute__ ((constructor));
static void init_cmd()
{
	cmdlist_expand(Cmds, 1);
}
