#
machine		ls3a5000_7a	loongarch	# CPU Architecture, Platform
config		pmon 
option		VGAROM_IN_BIOS
#
#  Define target endian
#
makeoptions	ENDIAN=EL		# Little endian version.

option		SMBIOS_SUPPORT
option		ACPI_SUPPORT
select		acpi_support
option		LS_STR

#include "conf/GENERIC_ALL"

#
# System Name and Target Name
#
option		SYSTYPE="\"loongson\""
option		TARGETNAME="\"loongson\""

#
# Cpu Type options
#

## 3A5000 support options ##
option		CORE_FREQ=2500
option		DDR_FREQ=800
option		RESERVED_COREMASK=0xfff0 #for 3A5000_7a
option		TOT_NODE_NUM=1
option		CORES_PER_NODE=4
option		TOT_7A_NUM=1 # (TOT_7A_NUM == 2) should change pcietree.asl
option		LOONGSON3A5000
option		PCIE_CONF_BASE="0xefe00000000"	#select this option when LOONGSON3A5000 is selected

## 3B5000 support options ##
#option		CORE_FREQ=2500
#option		DDR_FREQ=800
#option		RESERVED_COREMASK=0xff00 #for 3A5000_2w_7a
#option		TOT_NODE_NUM=2
#option		CORES_PER_NODE=4
#option		TOT_7A_NUM=1 # (TOT_7A_NUM == 2) should change pcietree.asl
#option		PCIE_CONF_BASE="0xefe00000000"	#select this option when LOONGSON3A5000 is selected

## 3C5000l support options ##
#option		CORE_FREQ=2200
#option		DDR_FREQ=800
#option		MCC			#memory cache coherency mode, recommend with TOT_NODE_NUM >= 2
#option		RESERVED_COREMASK=0x0000000000000000 #for 3C5000L_4w_7a
#option		RESERVED_COREMASK=0x00000000 #for 3C5000L_2w_7a
#option		RESERVED_COREMASK=0x0000 #for 3A5000_4w_7a
#option		TOT_NODE_NUM=8
#option		CORES_PER_NODE=4
#option		TOT_7A_NUM=1 # (TOT_7A_NUM == 2) should change pcietree.asl
#option		LOONGSON3A5000
#option		LOONGSON_3C5000     #LOONGSON3A5000 must be selected if this option is selected
#option		PCIE_CONF_BASE="0xefe00000000"	#select this option when LOONGSON3A5000 is selected

## 3C5000 support options ##
#option		CORE_FREQ=2200
#option		DDR_FREQ=800
#option		RESERVED_COREMASK=0x0000 #for 3A5000_4w_7a
#option		MCC			#memory cache coherency mode, recommend with TOT_NODE_NUM >= 2
#option		TOT_NODE_NUM=1
#option		CORES_PER_NODE=16
#option		TOT_7A_NUM=1 # (TOT_7A_NUM == 2) should change pcietree.asl
#option		LOONGSON3A5000
#option		LOONGSON_3C5000     #LOONGSON3A5000 must be selected if this option is selected
#option		FLAT_MODE
#option		PCIE_CONF_BASE="0xefe00000000"	#select this option when LOONGSON3A5000 is selected

## 2K2000 support options ##
#option		CORE_FREQ=1200
#option		DDR_FREQ=800
#option		RESERVED_COREMASK=0xfffc #for 2K2000
#option		TOT_NODE_NUM=1
#option		CORES_PER_NODE=2
#option		TOT_7A_NUM=1
#option		LOONGSON_2K2000
#option		LOONGSON_LS2K
#option		LS2K2000_GMEM_SIZE=256
#option		PCIE_CONF_BASE="0xfe00000000"
#option		LS_RIO		#RapidIo support

## 2K1500 support options ##   #2K1500 must cancel option: LS_STR, ACPI_SUPPORT, acpi_support
#option		CORE_FREQ=800
#option		DDR_FREQ=600
#option		RESERVED_COREMASK=0xfffc #for 2K1500
#option		TOT_NODE_NUM=1
#option		CORES_PER_NODE=2
#option		TOT_7A_NUM=1
#option		LOONGSON_2K1500
#select		ls2k1500
#option		LOONGSON_LS2K
#option		DDR3_MODE
#option		PCIE_CONF_BASE="0xfe00000000"


#
# Platform options
#
option		LOONGSON_GMAC

option		BONITOEL
option		INET
option		LS3_HT			# Enable the IO cache coherent of HT
#option		MCP68_IDE		# Enable the MCP68 IDE 0 channel
#option		USE_LPC_UART
#option		USE_SB700_LPC_UART

option		SOFT_CLKSEL
option		HT0_FREQ=1600
option		DDR_FREQ_2SLOT=600   # Frequency of dual slot memory with one mc
option		NODE_OFFSET=44

option		HIGH_MEM_WIN_BASE_ADDR=0x80000000
option		BOOTCORE_ID=0
#option		LS7A_2WAY_CONNECT
option		SHUTDOWN_MASK=0x0000
option		LOONGSON_3ASINGLE	#for 3A5000_7a
#option		LOONGSON_3ASERVER	#for 3A5000_2w_7a/3A5000_4w_7a/3C5000L_2w_7a/3C5000L_4w_7a
option		BONITO_100M
#option		BONITO_25M
option		BOOT_PARAM
option		LS7A			#for 7A
option		LS7A2000		#for 7A2000
#option		MULTI_7A_ON_PCIE=1
#option		ABOVE_4G

#option		ENABLE_SATA		#Enable SATA  ,when enable IDE can also use, when commended only IDE can use
#option		X800x600
option		LS_FB_XSIZE=640
option		LS_FB_YSIZE=480
option		CONFIG_VIDEO_16BPP
option		BEEP_NEW=1		#board v1.4 or later used new beep need this option

## VGA option ##
#option		USE_BMC
option		VGA_NO_ROM
option		VGA_BASE=0x1e000000
#option		VGA_BASE=0xc0000000
option		VRAM_SIZE=128
#option		DEBUG_EMU_VGA
option		AUTOLOAD

select		ls_rtc
#
# GPU driver selection. Selects for video
# Disable all options below to disable gpu driver 
# Enable all options below to enble gpu driver 
#
select		mod_x86emu_int10
select		mod_framebuffer
select		mod_vesa
select		mod_vgacon
#option		VESAFB
option		INTERFACE_3A780E

#
# flash type selection. Selects flash support
#
select		mod_flash_amd		# AMD flash device programming
select		mod_flash_intel		# intel flash device programming
select		mod_flash_sst		# intel flash device programming
select		mod_debugger		# Debugging module
select		mod_symbols		# Symbol table handling
select		mod_s3load		# Srecord loading
#select		mod_fastload		# LSI Fastload
select		mod_elfload		# ELF loading

#
# Command selection. Selects pmon commands
#
select		cmd_test_spi
select		cmd_newmt
select		cmd_setup
select		mod_display
select		cmd_about		# Display info about PMON
select		cmd_boot		# Boot wrapper
select		cmd_mycmd		
select		cmd_newmt
select		cmd_cache		# Cache enabling
#select		cmd_call		# Call a function command
select		cmd_date		# Time of day command
select		cmd_env			# Full blown environment command set
select		cmd_flash		# Flash programming cmds
select		cmd_hist		# Command history
select		cmd_ifaddr		# Interface address command
select		cmd_l			# Disassemble
select		cmd_mem			# Memory manipulation commands
select		cmd_more		# More paginator
select		cmd_misc		# Reboot & Flush etc.
#select		cmd_stty		# TTY setings command
select		cmd_tr			# Host port-through command
select		cmd_devls		# Device list
select		cmd_set			# As cmd_env but not req. cmd_hist
select		cmd_testdisk
#

select		cmd_shell		# Shell commands, vers, help, eval
#select		cmd_dtb
#option		DTB				# Only 2K use DTB


#
#
# Platform options
#
select		mod_uart_ns16550	# Standard UART driver
#option		CONS_BAUD=B9600
option		CONS_BAUD=B115200
select		ext4
select		fatfs
select		ramfiles
#select		mod_x86emu		# X86 emulation for VGA
option		MY40IO
option		NOPCINAMES		# Save some space for x86emu
#option		FASTBOOT
#select		vt82c686		#via686a/b code
#option		VGA_BASE=0xb0000000

#
# Functional options.
#
option		NOSNOOP			# Caches are no-snooping

#
# HAVE options. What tgt level provide
#
option		HAVE_TOD		# Time-Of-Day clock
option		HAVE_NVENV		#  Platform has non-volatile env mem
option		HAVE_LOGO		# Output splash logo
option		CONFIG_VIDEO_LOGO
option		PROGRESS_BAR
option		USE_SUPERIO_UART
#option		USE_LEGACY_RTC
#option		GODSONEV2A
#option		LINUX_PC
#option		LONGMENG
#option		RADEON7000
#option		DEBUG_EMU_VGA
#option		AUTOLOAD
#option		CONFIG_PCI0_LARGE_MEM	
#option		CONFIG_PCI0_HUGE_MEM	
#option		CONFIG_PCI0_GAINT_MEM	
option		CONFIG_CACHE_64K_4WAY
option		NVRAM_IN_FLASH	

#
#  Now the Machine specification
#
mainbus0	at root
localbus0	at mainbus0
#loopdev0	at mainbus0
#fd0		at mainbus0
pcibr0		at mainbus0
pcibr1		at mainbus0
pci*		at pcibr?
ppb*		at pci? dev ? function ?	# PCI-PCI bridges
pci*		at ppb? bus ?

#lahci0		at localbus0 base 0xbfe60000	# AHCI
#ahci_sd*	at lahci0
#ahci_cdrom*	at lahci0

ahci*		at pci? dev ? function ?
ahci_sd*	at ahci?
ahci_cdrom*	at ahci?
#### USB
#uhci*		at pci? dev ? function ?
ohci*		at pci? dev ? function ?	# OHCI
usb*		at ohci?

pcixhci*       at pci? dev ? function ?
xhci*          at pcixhci?
usb*           at xhci?
#usbnet*       at xhci?
select                 mod_usb_xhci

pcinvme*	at pci? dev ? function ?
nvme*		at pcinvme?

#### SCSI support
#siop*		at pci? dev ? function ?	# Symbios/NCR 53c...
#scsibus*	at siop?
#sd*		at scsibus? target ? lun ?
#cd*		at scsibus? target ? lun ?
#option		CONFIG_LSI_9260			# for LSI_9260-8i(2108) RAID card support

#### Networking Devices
#gt0		at localbus? base 4
#gt1		at localbus? base 5
#gt2		at localbus? base 6
# fxp normally only used for debugging (enable/disable both)
#fxp0		at pci? dev ? function ?	# Intel 82559 Device
#inphy*		at mii? phy ?			# Intel 82555 PHYs
#brgphy*		at mii? phy ?		# Broadcom PHYs
#rtl*		at pci? dev ? function ?
#rtk*		at pci? dev ? function ?
em*			at pci? dev ? function ?
igb*		at pci? dev ? function ?	#Intel 82576
select		igb1
bnx*		at pci? dev ? function ?	# BCM5709S

pcisyn0		at pci? dev ? function ?
pcisyn1		at pci? dev ? function ?
#pcisyn2		at pci? dev ? function ?
select		gmac


select		loongarch

select		mod_usb
select		mod_usb_storage
#select		mod_usb_uhci
select		mod_usb_ohci
select		mod_usb_kbd


#### IDE controllers
option		IDE_DMA
pciide*		at pci ? dev ? function ? flags 0x0000

#### IDE hard drives
wd*		at pciide? channel ? drive ? flags 0x0000
#wd0		at pciide? channel ? drive ? flags 0x0000
#cmdide*	at pci ? dev ? function ? flags 0x0000
#wd*		at cmdide? channel ? drive ? flags 0x0000

### wan+
### LSI MegaRAID SAS RAID controllers
mfi*		at pci?
scsibus*	at mfi?
sd*		at scsibus? target ? lun ?

#### Pseudo devices
pseudo-device	loop	1	# network loopback

ide_cd*		at pciide? channel ? drive ? flags 0x0001
select		iso9660
select		ramfiles
select		cmd_xyzmodem
option		IDECD
option		HAVE_NB_SERIAL
#option		USE_ENVMAC
#option		LOOKLIKE_PC
#select		cmd_lwdhcp
#select		cmd_bootp
option		FOR_GXEMUL
select		fatfs
#option		FLOATINGPT
#option		PCI_IDSEL_VIA686B=17
#option		PCI_IDSEL_SB700=14
option		WDC_NORESET
select		gzip
option		INPUT_FROM_BOTH
option		OUTPUT_TO_BOTH
#option		VIA686B_POWERFIXUP
option		DEVBD2F_VIA
#option		USE_GPIO_SERIAL
option		CONFIG_VIDEO_SW_CURSOR
select		http
#select		nfs
select		tcp
select		inet
select		tftpd
#select		e100
#SCSI		RAID disk drive support
select		scsi_sd
select		raw_ether
