/*
 * node clksetting.S
 */
// 100 / node_refc * node_loopc / node_div
#define NODE_REFC 4
//#define NODE_LOOPC 80 //1000M
//#define NODE_LOOPC 120 //1500M
//#define NODE_LOOPC 96 //1200M
#define NODE_LOOPC (CORE_FREQ*NODE_DIV*NODE_REFC/100)
#define NODE_DIV 2
#define HDA_DIV  NODE_LOOPC //25M
#define EMMC_DIV (NODE_LOOPC/4) //100M
	// config CONFBUS address
	li.d	t0, (PHYS_TO_UNCACHED(0xfdfe000000) | (21 << 11) | (0 << 8))
	li.w	t1, 0x10010000
	st.w	t1, t0, 0x10
	ld.w	t2, t0, 0x4
	li.w	t1, 0x2
	or	t1, t2, t1
	st.w	t1, t0, 0x4

	/* set lio sel */
	li.d	t0, PHYS_TO_UNCACHED(0x10010440)
	ld.w	t2, t0, 0
	li.w	t1, ~(3 << 20)
	and	t2, t1, t2
	li.w	t1, (1 << 20)
	or	t1, t1, t2
	st.d	t1, t0, 0

	TTYDBG ("Soft CLK SEL adjust begin\r\n")

#ifdef LOONGSON_2K2000
	li.d    t0, PHYS_TO_UNCACHED(0x100104d0)
	ld.d    t1, t0, 0
	li.d    t2, 0xfffffff8ffffffff
	and     t1, t2, t1
	li.d    t2, 0x0000000200000000
	or      t1, t2, t1
	st.d    t1, t0, 0
#endif

	li.d	t0, PHYS_TO_UNCACHED(0x100104a0)

	ld.d	t2, t0, 0
	li.d	t1, ~(1 << 41) 	//set sel_pll_out1 to 0
	and	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, (1 << 45) 	//set pll_pd to 1
	or	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, ~(1 << 43) 	//set pll_param to 0
	and	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, ~((0x7f << 32) | (0x1ff << 21) | (0x7f << 7))
	and	t2, t1, t2
	li.d	t1, (NODE_REFC << 32) | (NODE_LOOPC << 21) | (NODE_DIV << 7)
	or	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, (1 << 43) 	//set pll_param to 1
	or	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, ~(1 << 44) 	//set bypass to 0
	and	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, ~(1 << 45) 	//set pll_pd to 0
	and	t1, t1, t2
	st.d	t1, t0, 0

	li.d	t1, (1 << 39)
1:
	ld.d	t2, t0, 0
	and	t2, t1, t2
	beqz	t2, 1b			  //wait_locked_sys

	ld.d	t2, t0, 0
	li.d	t1, (1 << 41) 	//set sel_pll_out1 to 1
	or	t1, t1, t2
	st.d	t1, t0, 0

#ifdef LOONGSON_2K2000
	/* HDA pll config */
	ld.d	t2, t0, 0
	li.d	t1, ~((0x7f << 0))
	and	t2, t1, t2
	li.d	t1, (HDA_DIV << 0)
	or	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, (1 << 40) 	//set sel_pll_out2 to 1
	or	t1, t1, t2
	st.d	t1, t0, 0
#endif

	/* emmc pll config */
	ld.d	t2, t0, 0
	li.d	t1, ~((0x7f << 14))
	and	t2, t1, t2
	li.d	t1, (EMMC_DIV << 14)
	or	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, (1 << 42) 	//set sel_pll_out0 to 1
	or	t1, t1, t2
	st.d	t1, t0, 0

	//DDR clock
//1.2 < (refclk / div_ref * loopc) < 3.2
#define DDR_REFC 5
#define DDR_DIV 4
#define DDR_LOOPC (DDR_FREQ*DDR_REFC*DDR_DIV/100)    //DDR_FREQ = 100*DDR_LOOPC/(DDR_REFC*DDR_DIV)
#define DC_FREQ 400
#define DC_DIV DDR_LOOPC*100/(DDR_REFC*DC_FREQ)      //DC_FREQ = 100*DDR_LOOPC/(DDR_REFC*DC_DIV)
#define GPU_FREQ 500
#define GPU_DIV DDR_LOOPC*100/(DDR_REFC*GPU_FREQ)      //GPU_FREQ = 100*DDR_LOOPC/(DDR_REFC*GPU_DIV)

	li.d	t0, PHYS_TO_UNCACHED(0x10010490)

	ld.d	t2, t0, 0
	li.d	t1, ~(3 << 50)
	and	t1, t1, t2
	li.d	t2, (1 << 50) 	//set soft_memdiv_mode to 1
	or	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, ~(1 << 41) 	//set sel_pll_out1 to 0
	and	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, (1 << 45) 	//set pll_pd to 1
	or	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, ~(1 << 43) 	//set pll_param to 0
	and	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
#ifdef LOONGSON_2K2000
	li.d	t1, ~((0x7f << 32) | (0x1ff << 21) | (0x7f << 14) | (0x7f << 7))
#else
	li.d	t1, ~((0x7f << 32) | (0x1ff << 21) | (0x7f << 7))
#endif
	and	t2, t1, t2
	li.d	t1, (DDR_REFC << 32) | (DDR_LOOPC << 21) | (DDR_DIV << 7)
	or	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, (1 << 43) 	//set pll_param to 1
	or	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, ~(1 << 44) 	//set bypass to 0
	and	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, ~(1 << 45) 	//set pll_pd to 0
	and	t1, t1, t2
	st.d	t1, t0, 0

	li.d	t1, (1 << 39)
1:
	ld.d	t2, t0, 0
	and	t2, t1, t2
	beqz	t2, 1b			  //wait_locked_sys

	ld.d	t2, t0, 0
	li.d	t1, (1 << 41) 	//set sel_pll_out1 to 1
	or	t1, t1, t2
	st.d	t1, t0, 0

#ifdef LOONGSON_2K2000
	ld.d	t2, t0, 0
	li.d	t1, (1 << 42) 	//set sel_pll_out2 to 1
	or	t1, t1, t2
	st.d	t1, t0, 0

	/* DC pll config */
	ld.d	t2, t0, 0
	li.d	t1, ~((0x7f << 0))
	and	t2, t1, t2
	li.d	t1, (DC_DIV << 0)
	or	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, (1 << 40) 	//set sel_pll_out0 to 1
	or	t1, t1, t2
	st.d	t1, t0, 0
#endif

#if 0
	li.d	t0, PHYS_TO_UNCACHED(0x10010480)

	ld.d	t2, t0, 0
	li.d	t1, ~(3 << 50)
	and	t1, t1, t2
	li.d	t2, (1 << 50) 	//set soft_memdiv_mode to 1
	or	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, ~(1 << 41) 	//set sel_pll_out1 to 0
	and	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, (1 << 45) 	//set pll_pd to 1
	or	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, ~(1 << 43) 	//set pll_param to 0
	and	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, ~((0x7f << 32) | (0x1ff << 21) | (0x7f << 7))
	and	t2, t1, t2
	li.d	t1, (4 << 32) | (80 << 21) | (16 << 7)
	or	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, (1 << 43) 	//set pll_param to 1
	or	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, ~(1 << 44) 	//set bypass to 0
	and	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, ~(1 << 45) 	//set pll_pd to 0
	and	t1, t1, t2
	st.d	t1, t0, 0

	li.d	t1, (1 << 39)
1:
	ld.d	t2, t0, 0
	and	t2, t1, t2
	beqz	t2, 1b			  //wait_locked_sys

	ld.d	t2, t0, 0
	li.d	t1, (1 << 41) 	//set sel_pll_out0 to 1
	or	t1, t1, t2
	st.d	t1, t0, 0

	/* rio sata  pll config */
	ld.d	t2, t0, 0
	li.d	t1, ~((0x7f << 0))
	and	t2, t1, t2
	li.d	t1, (13 << 0)
	or	t1, t1, t2
	st.d	t1, t0, 0

	ld.d	t2, t0, 0
	li.d	t1, (1 << 38)
	or	t1, t1, t2
	st.d	t1, t0, 0
#endif
