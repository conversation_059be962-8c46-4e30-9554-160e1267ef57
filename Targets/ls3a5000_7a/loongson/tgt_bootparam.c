#include <libfdt.h>
#include <libfdt_env.h>
#include <sys/malloc.h>
#include "../../../pmon/common/smbios/smbios.h"
#include "../../../pmon/cmds/bootparam.h"
#include "target/ls7a.h"

#define PAGE_SHIFT 12
extern unsigned long long memorysize_high_n[];

void efimap_init_entry(struct efi_boot_memmap *m, int type, u64 start, u64 size)
{
	static int entry = 0;
	efi_memory_desc_t *map;

	map = (void *)m + sizeof(*m);
	map[entry].type = type;
	map[entry].phys_addr = start;
	map[entry].num_pages = size >> PAGE_SHIFT;
	entry++;
	m->map_size += sizeof(efi_memory_desc_t);
}

struct efi_boot_memmap *init_efi_mmap(void)
{
	int ret = 0;
	int i;
	struct efi_boot_memmap *map;
	efi_guid_t tbl_guid = LINUX_EFI_BOOT_MEMMAP_GUID;

	map = malloc(sizeof(struct efi_boot_memmap) + sizeof(efi_memory_desc_t) * 32);
	if (!map) {
		return NULL;
		printf("Alloc mammp memory failed!\n");
	}
	map->desc_size = sizeof(efi_memory_desc_t);
	map->desc_ver = 1;
	map->map_size = 0;

	efimap_init_entry(map, SYSTEM_RAM, 0x200000, 0xf000000 - 0x200000);  // 0x200000~0xf000000

	/* 3. Reserved low memory highest 16M. */
	efimap_init_entry(map, MEM_RESERVED, 0xf000000, 0x1000000);  // 0xf000000~0x10000000

	/* 4. Available SYSTEM_RAM area */
	efimap_init_entry(map, SYSTEM_RAM, HIGH_MEM_WIN_BASE_ADDR + 0x10000000, memorysize_high_n[0] - 0x10000000); // (HIGH_MEM_WIN_BASE_ADDR + 0x10000000) ~ MAX

	for (i = 1; i < TOT_NODE_NUM; i++) {
		if (memorysize_high_n[i])
			efimap_init_entry(map, SYSTEM_RAM, HIGH_MEM_WIN_BASE_ADDR | (i << 44), memorysize_high_n[i]);
	}
	/* And expose them to our EFI payload */
	return efi_install_configuration_table(&tbl_guid,
			(void *)VA_TO_PHYS(map));

}
