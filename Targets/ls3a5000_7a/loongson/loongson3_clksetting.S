/*
 * loongson3_clksetting.S
 * change the PLL settings of each core
 * NOTE:
 * Using S1 for passing the NODE ID
 */
#ifdef SOFT_CLKSEL

#define CORE_STEP       25

#define USE_LS_PLL      1

#ifdef BONITO_100M
#define SYS_CLOCK       100     //MUST BE 100 or 25, depend on the osillator
#elif BONITO_25M
#define SYS_CLOCK       25      //MUST BE 100 or 25, depend on the osillator
#SYS_CLOCK IS NOT GOOD FOR HIGH FREQUENCY
#endif

// L1_* define core frequency
#define LS_PLL		USE_LS_PLL
#define ST_PLL		(1 - USE_LS_PLL)

//PLL Constraint
//Refc = 25/50/100MHz, 50MHz is more prefered
//VCO = 4.8GHz - 6.4GHz
//LOOPC < 255
//DIV = 1/2/4/6/>6, 3/5 is NOT prefered

#define L1_REFC		2 //SYS_CLOCK/25
//#define L1_REFC		SYS_CLOCK/25

#define VDDA_CTRL_3     0x3
#define VDDD_CTRL_3     0x3
#define VDDA_CTRL_7     0x7
#define VDDD_CTRL_7     0x7
#define VDDA_EN         0x1
#define VDDD_EN         0x1

#define SYS_PD			(((1 - ST_PLL) << 1) | (1 - LS_PLL))
#define PLL_L1_LOCKED 		((ST_PLL << 17) | (LS_PLL << 16))
#define PLL_CHANG_COMMIT 	0x1

#define CORE_CLKSEL		0xc0
#define CORE_HSEL		0x80
#define PLL_L1_ENA		(0x1 << 2)

#if	defined(LOONGSON_3C5000)
#if	defined(FLAT_MODE)
    li.d    s4, 0x00010000
    li.d    s5, 0x00040000
#else
    li.d    s4, 0x0000100000000000
    li.d    s5, 0x0000400000000000
#endif
    add.d   s5, s5, s1
    move    s6, s1
1:
    TTYDBG ("Change the scale of HT0 clock\r\n")
    li.d    t0, PHYS_TO_UNCACHED(0x1fe00180)
    or      t0, t0, s1
    ld.w    a0, t0, 0x0
    li.w    a1, 0xf0ffffff
    and     a0, a0, a1
    li.w    a1, 0x0b000000 // 4/8 of node clock
    or      a0, a0, a1
    st.w    a0, t0, 0x0

#if (TOT_NODE_NUM >= 2)
    TTYDBG ("Change the scale of HT1 clock\r\n")
    li.d    t0, PHYS_TO_UNCACHED(0x1fe00180)
    or      t0, t0, s1
    ld.w    a0, t0, 0x0
    li.w    a1, 0x0fffffff
    and     a0, a0, a1
    li.w    a1, 0xb0000000 // 4/8 of node clock
    or      a0, a0, a1
    st.w    a0, t0, 0x0

    TTYDBG ("Change the scale of HT2/HT3 clock\r\n")
    li.d    t0, PHYS_TO_UNCACHED(0x1fe00180)
    or      t0, t0, s1
    ld.w    a0, t0, 0x4
    li.w    a1, 0xffffff00
    and     a0, a0, a1
    li.w    a1, 0x000000bb // 4/8 of node clock
    or      a0, a0, a1
    st.w    a0, t0, 0x4
#else
    TTYDBG ("Disable HT1/HT2/HT3 clock\r\n")
    li.d    t0, PHYS_TO_UNCACHED(0x1fe00180)
    or      t0, t0, s1
    ld.d    a0, t0, 0
    li.d    a1, 0xffffff000fffffff
    and     a0, a0, a1
    st.d    a0, t0, 0x4
#endif

    add.d   s1, s1, s4
    bne     s1, s5, 1b
    move    s1, s6
#else
#if (TOT_NODE_NUM >= 2)
    TTYDBG ("Change the scale of HT0 clock\r\n")
    li.d	t0, PHYS_TO_UNCACHED(0x1fe00180)
    or      t0, t0, s1
    ld.w    a0, t0, 0x0
    li.w	a1, 0xf0ffffff
    and     a0, a0, a1
    li.w	a1, 0x0b000000 // 4/8 of node clock
    or      a0, a0, a1
    st.w    a0, t0, 0x0
#else
    TTYDBG ("Disable HT0 clock\r\n")
    li.d	t0, PHYS_TO_UNCACHED(0x1fe00180)
    or      t0, t0, s1
    ld.w    a0, t0, 0x0
    li.w	a1, 0xf0ffffff
    and     a0, a0, a1
    li.w	a1, 0x00000000 // Disable
    or      a0, a0, a1
    st.w    a0, t0, 0x0
#endif

    TTYDBG ("Change the scale of HT1 clock\r\n")
    li.d	t0, PHYS_TO_UNCACHED(0x1fe00180)
    or      t0, t0, s1
    ld.w    a0, t0, 0x0
    li.w	a1, 0x0fffffff
    and     a0, a0, a1
    li.w	a1, 0xb0000000 // 4/8 of node clock
    or      a0, a0, a1
    st.w    a0, t0, 0x0
#endif /* LOONGSON_3C5000 */

    TTYDBG ("Change the scale of LS132 clock\r\n")
    li.d    t0, PHYS_TO_UNCACHED(0x1fe00420)
    or      t0, t0, s1
    ld.w    a0, t0, 0x0
    li.w    a1, 0xfff0ffff
    and     a0, a0, a1
    li.w    a1, 0x00080000 // 1/8 of node clock
    or      a0, a0, a1
    st.w    a0, t0, 0x0

    TTYDBG ("Soft CLK SEL adjust begin\r\n")

    li.d    t0, PHYS_TO_UNCACHED(0x1fe00194)
    or      t0, t0, s1
    ld.w    a0, t0, 0x0
    li.w    a1, CORE_CLKSEL
    and     a0, a0, a1
    li.w    a1, CORE_HSEL
    bne     a0, a1, 30f //soft_ht


//soft_sys:
    TTYDBG ("CORE & NODE:")
    li.d    t0, PHYS_TO_UNCACHED(0x1fe001b0)
    or      t0, t0, s1
    li.d    t1, (0x7 << 19) //power down pll L1 first
    st.d    t1, t0, 0x0

    li.d    t1, (VDDD_CTRL_3 << 60) | (VDDA_CTRL_3 << 56)
    csrrd   a0, 0x32 /* Get CPU Version */
    li.d    a1, 0x30
    bne     a0, a1, 1f
    li.d    t1, (VDDD_CTRL_7 << 60) | (VDDA_CTRL_7 << 56)
1:
#if (CORE_FREQ >= 2300)
    li.w    t5, 2
    bne     a0, a1, 1f
    li.w    t5, 3
1:
#elif (CORE_FREQ >= 1600)
    li.w    t5, 3
#else
    li.w    t5, 4
#endif
    /*L1_LOOPC = L1_REFC*CPU_FREQ*L1_DIV/SYS_CLOCK */
    li.w    t2, CORE_FREQ
    li.w    t3, L1_REFC
    li.w    t4, SYS_CLOCK

    mul.d   t3, t2, t3
    mul.d   t3, t5, t3
    div.d   t3, t3, t4

    slli.d  t2, t3, 32
    slli.d  t3, t3, 54
    or      t2, t2, t3 /*L1_LOOPC*/

    or      t1, t1, t5 /* | L1_DIV */
    st.d    t1, t0, 0x8
    li.d    t1, (0x7 << 19) | (VDDD_EN << 9) | (VDDA_EN << 8) 	//power down pll L1 first
    st.d    t1, t0, 0x0
    li.d    t1, (L1_REFC << 48) | (L1_REFC << 26) | \
                (SYS_PD << 19) | (ST_PLL << 22) | (0x3 << 10) | (VDDD_EN << 9) | (VDDA_EN << 8) | (0x1 << 7)
    or      t1, t1, t2 /* | L1_LOOPC */
    slli.d  t5, t5, 42 /* L1_DIV << 42*/
    or      t1, t1, t5 /* | L1_DIV */

    st.d    t1, t0, 0x0
    ori     t1, t1, PLL_L1_ENA
    st.d    t1, t0, 0x0

11: //wait_locked_sys:
    ld.d    a0, t0, 0x0
    li.d    a1, PLL_L1_LOCKED
    and     a0, a1, a0
    beqz    a0, 11b //wait_locked_sys

    ld.d    a0, t0, 0x0
    ori     a0, a0, PLL_CHANG_COMMIT
    st.d    a0, t0, 0x0

    bl     hexserial


30: //soft_ht:
    TTYDBG ("\r\n clock setting done!\r\n")
#endif /* SOFT_CLKSEL */
