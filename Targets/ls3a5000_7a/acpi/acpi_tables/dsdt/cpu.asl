/** @file
  Sample ACPI Platform Driver

  Copyright (c) 2008 - 2011, Intel Corporation. All rights reserved.<BR>
  Copyright (c) 2018 Loongson Technology Corporation Limited (www.loongson.cn).
  All intellectual property rights(Copyright, Patent and Trademark) reserved.

  Any violations of copyright or other intellectual property rights of the Loongson
  Technology Corporation Limited will be held accountable in accordance with the law,
  if you (or any of your subsidiaries, corporate affiliates or agents) initiate directly
  or indirectly any Intellectual Property Assertion or Intellectual Property Litigation:
  (i) against Loongson Technology Corporation Limited or any of its subsidiaries or corporate affiliates,
  (ii) against any party if such Intellectual Property Assertion or Intellectual Property Litigation arises
  in whole or in part from any software, technology, product or service of Loongson Technology Corporation Limited
  or any of its subsidiaries or corporate affiliates, or (iii) against any party relating to the Software.

  THIS SOFTWARE IS PROVIDED BY THE AUTHOR "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
  THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION).

**/

#define TOTCORE  (TOT_NODE_NUM * CORES_PER_NODE)
Scope (\_SB)
{
  Device (C000)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x01)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }

  Device (C001)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x02)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }

#if (TOTCORE > 2)
  Device (C002)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x03)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C003)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x04)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }

#if (TOTCORE > 4)
  Device (C004)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x05)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C005)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x06)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C006)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x07)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C007)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x08)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
#if (TOTCORE > 8)
  Device (C008)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x09)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C009)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x0A)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C00A)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x0B)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C00B)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x0C)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C00C)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x0D)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C00D)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x0E)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C00E)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x0F)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C00F)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x10)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
#if (TOTCORE > 16)
  Device (C010)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x11)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C011)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x12)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C012)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x13)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C013)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x14)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C014)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x15)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C015)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x16)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C016)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x17)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C017)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x18)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C018)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x19)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C019)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x1A)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C01A)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x1B)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C01B)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x1C)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C01C)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x1D)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C01D)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x1E)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C01E)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x1F)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C01F)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x20)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
#if (TOTCORE > 32)
  Device (C020)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x21)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C021)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x22)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C022)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x23)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C023)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x24)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C024)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x25)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C025)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x26)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C026)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x27)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C027)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x28)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C028)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x29)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C029)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x2A)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C02A)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x2B)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C02B)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x2C)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C02C)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x2D)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C02D)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x2E)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C02E)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x2F)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C02F)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x30)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C030)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x31)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C031)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x32)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C032)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x33)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C033)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x34)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C034)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x35)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C035)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x36)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C036)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x37)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C037)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x38)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C038)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x39)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C039)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x3A)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C03A)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x3B)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C03B)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x3C)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C03C)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x3D)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C03D)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x3E)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C03E)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x3F)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
  Device (C03F)
  {
    Name (_HID, "ACPI0007" /* Processor Device */)  // _HID: Hardware ID
    Name (_UID, 0x40)  // _UID: Unique ID
    Name (_PXM, Zero)  // _PXM: Device Proximity
    Name (_STA, 0x0F)  // _STA: Status
    Method (_PPC, 0, NotSerialized)  // _PPC: Performance Present Capabilites
    {
      return (0);
    }

    Method (_PCT, 0, NotSerialized)  // _PCT: Performance Control
    {
      Return (Package (0x02)
      {
        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
          ,)},

        ResourceTemplate ()
        {
          Register (FFixedHW,
            0x00,               // Bit Width
            0x00,               // Bit Offset
            0x0000000000000000, // Address
        ,)}
      })
    }

    Method (_PSS, 0, NotSerialized)  // _PSS: Performance Supported States
    {
      Return (LPSS)
    }

    Name (LPSS, Package (0x10)
    {
      Package (0x06)
      {
        0x000007D0,
        0x00003A98,
        20000,
        20000,
        0x00000103,
        0x00000003
      },

      Package (0x06)
      {
        0x00000708,
        0x00003A98,
        20000,
        20000,
        0x00000003,
        0x00000003
      },

      Package (0x06)
      {
        0x000006D6,
        0x00003A98,
        20000,
        20000,
        0x00000104,
        0x00000004
      },
      Package (0x06)
      {
        0x00000627,
        0x00003A98,
        20000,
        20000,
        0x00000004,
        0x00000004
      },
      Package (0x06)
      {
        0x000005DC,
        0x00003A98,
        20000,
        20000,
        0x00000105,
        0x00000005
      },
      Package (0x06)
      {
        0x00000546,
        0x00003A98,
        20000,
        20000,
        0x00000005,
        0x00000005
      },
      Package (0x06)
      {
        0x000004E2,
        0x00003A98,
        20000,
        20000,
        0x00000106,
        0x00000006
      },
      Package (0x06)
      {
        0x00000465,
        0x00003A98,
        20000,
        20000,
        0x00000006,
        0x00000006
      },
      Package (0x06)
      {
        0x000003E8,
        0x00003A98,
        20000,
        20000,
        0x00000107,
        0x00000007
      },
      Package (0x06)
      {
        0x00000384,
        0x00003A98,
        20000,
        20000,
        0x00000007,
        0x00000007
      },
      Package (0x06)
      {
        0x000002EE,
        0x00003A98,
        20000,
        20000,
        0x00000108,
        0x00000008
      },
      Package (0x06)
      {
        0x000002A3,
        0x00003A98,
        20000,
        20000,
        0x00000008,
        0x00000008
      },
      Package (0x06)
      {
        0x000001F4,
        0x00003A98,
        20000,
        20000,
        0x00000109,
        0x00000009
      },
      Package (0x06)
      {
        0x000001C2,
        0x00003A98,
        20000,
        20000,
        0x00000009,
        0x00000009
      },
      Package (0x06)
      {
        0x000000FA,
        0x00003A98,
        20000,
        20000,
        0x0000010A,
        0x0000000A
      },
      Package (0x06)
      {
        0x000000E1,
        0x00003A98,
        20000,
        20000,
        0x0000000A,
        0x0000000A
      }
    })
  }
#endif
#endif
#endif
#endif
#endif
}
