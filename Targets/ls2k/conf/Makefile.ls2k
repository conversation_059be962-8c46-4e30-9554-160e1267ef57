ifndef S
S:=$(shell 	\
	echo "char *node_name[] = {" > ../../include/dtsnode.h;	\
	cat ../../conf/LS2K1000.dts | grep @ | cut -d '{' -f 1 | cut -d ':' -f 2 | sed s/[[:space:]]//g | sort -k2n | uniq | awk '{print "\"/soc/"$$0}' | awk '{print $$0"\","}' >> ../../include/dtsnode.h;\
	echo "};" >> ../../include/dtsnode.h; \
	cd ../../../..; pwd)
endif
TARGET= ${S}/Targets/ls2k
SUBTARGET?=$(DESTTAGET)


%OBJS

%CFILES

%SFILES

%LOAD

export CFILES OBJS SFILES
include ${S}/Makefile.inc

%RULES
