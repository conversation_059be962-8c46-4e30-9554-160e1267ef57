#include <stdio.h>
//#include "include
#include <stdlib.h>
#include <ctype.h>
#undef _KERNEL
#include <errno.h>
#include <pmon.h>

#if  defined(LOONGARCH_2P500)
#include <target/ls2p500.h>
#elif   defined(LOONGARCH_2P300)
#include <target/ls2p300.h>
#endif
#include <linux/spi.h>
#include <target/spi-io.h>

#define DIV_ROUND_UP(n,d)       (((n) + (d) - 1) / (d))


#ifdef TPM2
struct ls_spi tpm={
	.base  =SPI2,
	.cur_speed = 1000000,  // 降低速度测试
	.cur_bpw =8,
	.cur_fthlv =0,
	.pol = 0,
	.pha = 0,

	.tx_buf =NULL,
	.rx_buf=NULL,
	.tx_len = 0,
	.rx_len = 0,
	.cur_xferlen = 0,
	.cs_change  =0,
};
struct ls_spi *spi = &tpm;
#else
struct ls_spi st7735={
	.base  =SPI1,
	.cur_speed = 50000000,
	.cur_bpw =8,
	.cur_fthlv =0,
	.pol = 0,
	.pha = 0,

	.tx_buf =NULL,
	.rx_buf=NULL,
	.tx_len = 0,
	.rx_len = 0,
	.cur_xferlen = 0,
};
struct ls_spi *spi = &st7735;
#endif

typedef enum{
        RX,
        TX
}DATATYPE;

static inline void dump_reg()
{
        printf("CR1   :%08x\n",spi_readl(spi,SPI_CR1));
        printf("CR2   :%08x\n",spi_readl(spi,SPI_CR2));
        printf("CR3   :%08x\n",spi_readl(spi,SPI_CR3));
        printf("CR4   :%08x\n",spi_readl(spi,SPI_CR4));
        printf("IER   :%08x\n",spi_readl(spi,SPI_IER));
        printf("SR1   :%08x\n",spi_readl(spi,SPI_SR1));
        printf("SR2   :%08x\n",spi_readl(spi,SPI_SR2));
        printf("CFG1  :%08x\n",spi_readl(spi,SPI_CFG1));
        printf("CFG2  :%08x\n",spi_readl(spi,SPI_CFG2));
        printf("CFG3  :%08x\n",spi_readl(spi,SPI_CFG3));
        printf("\n\n========dump_reg end============\n\n");
}

static int ls_spi_prepare_br(u32 speed_hz)
{
	u32 div;

	div = DIV_ROUND_UP(125000000 & ~0x1, speed_hz);

	if(div < 2 || div > 255){
		return -EINVAL;
	}

	return div;
}

static u32 ls_spi_prepare_fthlv(DATATYPE data_type)
{
        u32 fthlv;

        if(data_type == RX){
                if(spi->rx_len > 3)
                        fthlv =4;
                else if(spi->rx_len > 1)
                        fthlv =2;
                else
                        fthlv =1;
        }else{
                if(spi->tx_len > 3)
                        fthlv =4;
                else if(spi->tx_len > 1)
                        fthlv =2;
                else
                        fthlv =1;

        }
        return fthlv;

}

/* static void ls_spi_write_fifo()
{
        while ((spi->tx_len > 0) && spi_get_txaflag(spi)){
                u32 offs = spi->cur_xferlen -spi->tx_len;

                if(spi->tx_len >= sizeof(u32)) {
                        const u32 * tx_buf32=(const u32 *)(spi->tx_buf + offs);

                        spi_writel(spi,SPI_DR, *tx_buf32);
                        spi->tx_len -= sizeof(u32);
                }else if (spi->tx_len >= sizeof(u16)){
                        const u16 * tx_buf16 =(const u16 *)(spi->tx_buf + offs);

                        spi_writel(spi,SPI_DR, *tx_buf16);
                        spi->tx_len -=sizeof(u16);
                }else{
                        const u8 * tx_buf8=(const u8 *)(spi->tx_buf + offs);

                        spi_writel(spi,SPI_DR, *tx_buf8);
                        spi->tx_len -=sizeof(u8);
                }
        }
//	printf("%d bytes left\n",spi->tx_len);
} */

static void ls_spi_write_fifo() {
    while((spi->tx_len > 0) && spi_get_txaflag(spi)) {
        u32 offs = spi->cur_xferlen - spi->tx_len;

        if(spi->tx_len >= sizeof(u32)) {
            const u32 *tx_buf32 = (const u32 *)(spi->tx_buf + offs);
            spi_writel(spi, SPI_DR, *tx_buf32);
            spi->tx_len -= sizeof(u32);
        } else {
            u8 remaining_bytes[4] = {0};
            memcpy(remaining_bytes, spi->tx_buf + offs, spi->tx_len);
            
            u32 *data_to_send = (u32 *)remaining_bytes;
            spi_writel(spi, SPI_DR, *data_to_send);
            
            spi->tx_len = 0;
        }
    }
}

static void ls_spi_read_fifo()
{
        u32 rxflv = spi_get_rxflv(spi);

        while((spi->rx_len >0)&&
	       spi_get_rxaflag(spi)){
                u32 offs = spi->cur_xferlen -spi->rx_len;

                if((spi->rx_len >=sizeof(u32))){
                         u32 *rx_buf32 = (u32 *)(spi->rx_buf + offs);

                         *rx_buf32 =spi_readl(spi,SPI_DR);
                         spi->rx_len -= sizeof(u32);
                }else if((spi->rx_len >=sizeof(u16))){
                                u16 *rx_buf16 = (u16 *)(spi->rx_buf + offs);

                                *rx_buf16 =spi_readl(spi,SPI_DR);
                                spi->rx_len -= sizeof(u16);
                }else{
                        u8 *rx_buf8 = (u8 *)(spi->rx_buf + offs);

                        *rx_buf8 =spi_readl(spi,SPI_DR);
                        spi->rx_len -= sizeof(u8);
                }

		if(spi->rx_len >3){
			spi_set_bits(spi,SPI_CR2,SPI_CR2_RXFTHLV,3);
		}
		else if(spi->rx_len >1){
			spi_set_bits(spi,SPI_CR2,SPI_CR2_RXFTHLV,1);

		}
		else{
			spi_set_bits(spi,SPI_CR2,SPI_CR2_RXFTHLV,0);
		}
		rxflv =spi_get_rxflv(spi);

        }
        //printf(" %d bytes left\n",spi->rx_len);
}

static void ls_spi_enable()
{
        //printf("enable spi controller\n");

	spi_spe_enable(spi);
}

static void ls_spi_disable()
{
	while(!spi_get_eotflag(spi));

	spi_set_bits(spi,SPI_SR1,SPI_SR1_EOT,1);

	if(!spi->cs_change){
		spi_set_bits(spi,SPI_CR1,SPI_CR1_SPE,0);
	}

}

static void ls_spi_transfer_one_irq()
{
	ls_spi_enable(spi);

	if(spi->tx_buf){
		ls_spi_write_fifo();
	}
	spi_cstart_enable(spi);

	spi_writel(spi,SPI_IER,0x8903);
}

static int ls_spi_transfer_one_setup(struct message *msg)
{
    u32 nb_words;
    u32 cfg1_setb =0,cfg1_clrb =0,cr2_setb=0, cr2_clrb=0, cfg2_setb =0,cfg2_clrb =0;
	u32 bpw,fthlv;

	int ret =0;

	spi_autosus_enable(spi);
	spi_set_cpha(spi,spi->pha);
	spi_set_cpol(spi,spi->pol);

	bpw = spi->cur_bpw -1;

	spi->cur_fthlv = ls_spi_prepare_fthlv(0);
	fthlv =spi->cur_fthlv -1;
	spi_set_bits(spi,SPI_CR2,SPI_CR2_RXFTHLV,fthlv);

	spi->cur_fthlv = ls_spi_prepare_fthlv(1);
	fthlv =spi->cur_fthlv -1;
	spi_set_bits(spi,SPI_CR2,SPI_CR2_TXFTHLV,fthlv);

	spi_set_bits(spi,SPI_CFG1,SPI_CFG1_DSIZE,bpw);

	int br;

	br = ls_spi_prepare_br(spi->cur_speed);
	if(br <0){
		ret =-EMSGSIZE;
		goto out;
	}

	spi_set_bits(spi,SPI_CFG2,SPI_CFG2_BRINT,br);

	spi_set_bits(spi,SPI_CR2,SPI_CR2_TXFTHLV,fthlv);

	spi_set_bits(spi,SPI_CFG3,SPI_CFG3_MSTR,1);

	if(spi->tx_buf && spi->rx_buf){
		spi_set_bits(spi,SPI_CFG3,SPI_CFG3_DIOSWP,0);
		spi_set_bits(spi,SPI_CFG3,SPI_CFG3_DIE,1);
		spi_set_bits(spi,SPI_CFG3,SPI_CFG3_DOE,1);
	}else if(spi->tx_buf){
		spi_set_bits(spi,SPI_CFG3,SPI_CFG3_DIOSWP,0);
		spi_set_bits(spi,SPI_CFG3,SPI_CFG3_DOE,1);
		spi_set_bits(spi,SPI_CFG3,SPI_CFG3_DIE,0);
	}else if(spi->rx_buf){
		spi_set_bits(spi,SPI_CFG3,SPI_CFG3_DIOSWP,0);
		spi_set_bits(spi,SPI_CFG3,SPI_CFG3_DIE,1);
		spi_set_bits(spi,SPI_CFG3,SPI_CFG3_DOE,0);
	}

	if(spi->cur_bpw <= 8)
		nb_words = msg->len;
	else if(spi->cur_bpw <= 16)
		nb_words = DIV_ROUND_UP(msg->len * 8, 16);
	else
		nb_words = DIV_ROUND_UP(msg->len * 8, 32);

	if(nb_words <= SPI_CR3_TSIZE){
		nb_words =nb_words-1;
		spi_writel(spi,SPI_CR3,nb_words);
		spi_writel(spi,SPI_CR4,0);
	}else{
		ret= -1;
		goto out;
	}

	spi->cur_xferlen =msg->len;
out:
	return ret;

}

int ls_spi_transfer_poll(struct message *msg)
{
        int ret;
	int timeout =1000;
        if(msg->len == 0)
                return 0;

	spi->tx_buf  =msg->tx_buf;
	spi->rx_buf  =msg->rx_buf;
        spi->tx_len =spi->tx_buf ? msg->len : 0;
        spi->rx_len =spi->rx_buf ? msg->len : 0;
	spi->cs_change =msg->cs_change;

	ret = ls_spi_transfer_one_setup(msg);
        if(ret){
                printf("SPI transfer setup failed\n");
                return ret;
        }

        ls_spi_transfer_one_irq(spi);

	while((spi->rx_len || spi->tx_len)&&timeout --){
		if(spi->tx_len >0){
			ls_spi_write_fifo();
		}
		if(spi->rx_len >0){
			ls_spi_read_fifo();
		}
		delay(1000);
	}

	ls_spi_disable();

	return 0;
}

static int spi_read_write(const u8 *in, u8 *out, u32 len)
{
        int ret;
	struct message m;

	memset(&m, 0, sizeof(m));
	m.tx_buf = in;
	m.rx_buf = out;
	m.len = len;

	ret = ls_spi_transfer_poll(&m);
	if (ret < 0) {
		printf("%s: spi request transfer failed (err: %d)\n", __func__, ret);
		return -1;
	}

	return 0;
}

static int test_spi_transfer(int argc, char *argv[])
{
#define LEN	8
	u8 tx_buf[LEN];
	u8 rx_buf[LEN];
	int i, ret;

	spi->pol = atoi(argv[1]);
	spi->pha = atoi(argv[2]);

	printf("input: pol:%d  pha%d \n", spi->pol, spi->pha);

	for(i = 0; i < LEN; i++) {
		tx_buf[i] = i;
	}
	memset(rx_buf, 0, LEN);

	ret = spi_read_write(tx_buf, rx_buf, LEN);
	if(!ret) {
		printf("read spi data:\n");
		for(i = 0; i < LEN; i++) {
			printf("rx[%d] %d\n", i, rx_buf[i]);
		}
	} else {
		printf("spi read write fail !\n");
	}

	return ret;

#undef LEN
}

static const Cmd spi_cmds[] =
{
        {"SPI"},
        {"spirw", NULL, NULL,"test spi read write", test_spi_transfer, 0, 99, CMD_REPEAT},
        {0, 0}
};
static void init_cmd __P((void)) __attribute__ ((constructor));

static void init_cmd()
{
        cmdlist_expand(spi_cmds, 1);
}

