# $Id: files.Bonito
#
# Bonito Target specific files
#

file	Targets/ls2k/pci/pci_machdep.c
file	Targets/ls2k/cache_stage/cache_stage.c
file	Targets/ls2k/ls2k1000/tgt_machdep.c
file	Targets/ls2k/dev/dc.c
file	Targets/ls2k/pci/ls2k_pci.c
file	Targets/ls2k/dev/fl_env.c

define  localbus { [base = -1 ] }
device  localbus
attach  localbus at mainbus
file    Targets/ls2k/dev/localbus.c		localbus
file    Targets/ls2k/dev/spi_w.c
file	Targets/ls2k/dev/eeprom.c
file	Targets/ls2k/dev/load_dtb.c		cmd_dtb
#file	Targets/ls2k/dev/slt.c

#XHCI
device lxhci  : xhcibus
attach lxhci at localbus

# OHCI
#device	lohci {[channel = -1]} :usbbus
#attach	lohci at localbus

#XHCI
#device lxhci  : xhcibus
#attach lxhci at localbus

# GMAC
#file	sys/dev/gmac/synopGMAC_Host.c
file	sys/dev/gmac/synopGMAC_Dev.c
file	sys/dev/gmac/synopGMAC_network_interface.c
file	sys/dev/gmac/synopGMAC_pci_bus_interface.c
file	sys/dev/gmac/synopGMAC_plat.c
file	sys/dev/nand/ls2k-nand.c nand


device	syn: ether, ifnet
attach	syn at localbus
file	sys/dev/gmac/if_gmac.c gmac


# Ethernet driver for Discovery ethernet
device  gt: ether, ifnet, ifmedia
attach  gt at localbus
file    sys/dev/ic/if_gt.c			gt

device	sdcard
attach	sdcard at localbus


# AHCI
device	lahci {[channel = -1]} :ahcibus
attach	lahci at localbus

#
# Media Indepedent Interface (mii)
#
include "sys/dev/mii/files.mii"

# Various PCI bridges

include "sys/dev/pci/files.pci"

device	pcisyn: ether, ifnet
attach	pcisyn at pci

device	lohci {[channel = -1]} :usbbus
attach	lohci at localbus

device	lehci {[channel = -1]} :usbbus
attach	lehci at localbus
#
# Machine-independent ATAPI drivers
#

include "sys/dev/ata/files.ata"

#
#  SCSI framework
#
include "sys/scsi/files.scsi"

#
# Custom application files
#
include "pmon/custom/files.custom"
