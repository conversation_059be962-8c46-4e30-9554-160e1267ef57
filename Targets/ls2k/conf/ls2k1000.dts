/dts-v1/;
/ {
	model = "loongson,generic";
	compatible = "loongson,ls2k";
	#address-cells = <2>;
	#size-cells = <2>;

	aliases {
		ethernet0 = &gmac0;
		ethernet1 = &gmac1;
		serial0 = &cpu_uart0;
		i2c0 = &i2c0;
		i2c1 = &i2c1;
		i2c2 = &i2c2;
		i2c3 = &i2c3;
	};

	chosen {
		stdout-path = "serial0:115200n8";
		bootargs = "earlycon";
	};

	memory {
		name = "memory";
		device_type = "memory";
		reg =  <0 0x00200000 0 0x06e00000
			0 0x08000000 0 0x07000000
			0 0x90000000 1 0xe0000000>;
	};

	reserved-memory {
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;

		linux,cma {
			compatible = "shared-dma-pool";
			reusable;
			size = <0x2000000>;
			linux,cma-default;
		};
	};

	memalloc@0x90000000 {
		compatible = "loongson,ls-memalloc";
		reg = <0 0x90000000 0 0x20000000>;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			device_type = "cpu";
			compatible = "loongarch";
			reg=<0>;
			numa-node-id = <0>;
		};

		cpu1: cpu@1 {
			device_type = "cpu";
			compatible = "loongarch";
			reg=<1>;
			numa-node-id = <0>;
		};
	};

	cpuic: interrupt-controller {
		compatible = "loongson,cpu-interrupt-controller";
		interrupt-controller;
		#interrupt-cells = <1>;
	};

	icu: interrupt-controller@1fe01400 {
		compatible = "loongson,2k1000-icu";
		interrupt-controller;
		#interrupt-cells = <1>;
		reg = <0 0x1fe01400 0 0x40
			0 0x1fe01040 0 16>;
		interrupt-parent = <&cpuic>;
		interrupt-names = "cascade";
		interrupts = <3>; /* HW IP1 */
	};

	soc {
		compatible = "ls,nbus", "simple-bus";
		#address-cells = <2>;
		#size-cells = <2>;
		ranges = <0 0x10000000 0 0x10000000 0 0x10000000
			0 0x2000000  0 0x2000000  0 0x2000000
			0 0x20000000 0 0x20000000 0 0x10000000
			0 0x40000000 0 0x40000000 0 0x40000000
			0xfe 0x00000000 0xfe 0x00000000 0 0x40000000>;

		dma-coherent;

		scfg: scfg@1fe00000 {
			compatible = "loongson,2k1000la-scfg";
			reg = <0 0x1fe00000 0 0x3ffc>;
			little-endian;
		};

		cpu_uart0: serial@0x1fe20000 {
			compatible = "ns16550a";
			reg = <0 0x1fe20000 0 0x10>;
			clock-frequency = <125000000>;
			interrupt-parent = <&icu>;
			interrupts = <0>;
			no-loopback-test;
		};

		pioA:gpio@0x1fe00500 {
			compatible = "ls,ls2k-gpio", "ls,ls-gpio";
			reg = <0 0x1fe00500 0 0x38>;
			ngpios = <64>;
			gpio-controller;
			#gpio-cells = <2>;

			interrupt-parent = <&icu>;
			interrupts =
				<60>, <61>, <62>, <63>, <58>, <58>,
				<58>, <58>, <58>, <58>, <58>, <58>,
				<58>, <58>, <58>, <>,   <58>, <58>,
				<58>, <58>, <58>, <58>, <58>, <58>,
				<58>, <58>, <58>, <58>, <58>, <58>,
				<58>, <58>, <59>, <59>, <59>, <59>,
				<59>, <>,   <59>, <59>, <59>, <59>,
				<>,   <>,   <59>, <59>, <59>, <59>,
				<59>, <59>, <59>, <59>, <59>, <59>,
				<59>, <59>, <59>, <59>, <59>, <59>,
				<59>, <59>, <59>, <59>;
		};

		pmc: acpi@0x1fe27000 {
			compatible = "loongson,acpi-pmc", "syscon";
			reg = <0x0 0x1fe27000 0x0 0x58>;
			interrupt-parent = <&icu>;
			interrupts = <43>;
			suspend-address = <0x1c000500>;
			pcie-wake;
		};

		reboot {
			compatible ="syscon-reboot";
			regmap = <&pmc>;
			offset = <0x30>;
			mask = <0x1>;
		};

		poweroff {
			compatible ="syscon-poweroff";
			regmap = <&pmc>;
			offset = <0x14>;
			mask = <0x3c00>;
			value = <0x3c00>;
		};

		otg@0x40000000 {
			compatible = "loongson,ls2k-otg";
			reg = <0 0x40000000 0 0x40000>;
			interrupt-parent = <&icu>;
			interrupts = <49>;
			dma-mask = <0x0 0xffffffff>;
		};

		ohci@0x40070000 {
			compatible = "loongson,ls2k-ohci", "generic-ohci";
			reg = <0 0x40070000 0 0x8000>;
			interrupt-parent = <&icu>;
			interrupts = <51>;
			dma-mask = <0x0 0xffffffff>;
		};

		ehci@0x40060000 {
			compatible = "loongson,ls2k-ehci", "generic-ehci";
			reg = <0 0x40060000 0 0x8000>;
			interrupt-parent = <&icu>;
			interrupts = <50>;
			/* enable 64 bits dma-mask nedd setting 0x1fe00420 |= 1 << 36*/
			dma-mask = <0 0xffffffff>;
		};

		i2c2: i2c@1fe21000 {
			compatible = "loongson,ls-i2c";
			reg = <0 0x1fe21000 0 0x8>;
			interrupt-parent = <&icu>;
			interrupts = <22>;
			#address-cells = <1>;
			#size-cells = <0>;

			eeprom@57{
				compatible = "atmel,24c16";
				reg = <0x57>;
				pagesize = <16>;
			};
		};

		i2c3: i2c@1fe21800 {
			#address-cells = <1>;
			#size-cells = <0>;

			compatible = "loongson,ls-i2c";
			reg = <0 0x1fe21800 0 0x8>;
			interrupt-parent = <&icu>;
			interrupts = <23>;

			codec@1a{
				compatible = "codec_uda1342";
				reg = <0x1a>;
			};
		};

		i2c0: i2c-gpio@0 {
			compatible = "i2c-gpio";
			gpios = <&pioA 1 0 /* sda */
				 &pioA 0 0 /* scl */
				>;
			/*i2c-gpio,sda-open-drain;*/
			/*i2c-gpio,scl-open-drain;*/
			i2c-gpio,delay-us = <5>;	/* ~100 kHz */
			#address-cells = <1>;
			#size-cells = <0>;
			eeprom@50 {
				compatible = "dvi-eeprom-edid";
				reg = <0x50>;
			};
		};

		i2c1: i2c-gpio@1 {
			compatible = "i2c-gpio";
			gpios = <&pioA 32 0 /* sda */
				 &pioA 33 0 /* scl */
				>;
			/*i2c-gpio,sda-open-drain;*/
			/*i2c-gpio,scl-open-drain;*/
			i2c-gpio,delay-us = <5>;	/* ~100 kHz */
			#address-cells = <1>;
			#size-cells = <0>;
			eeprom@50 {
				compatible = "eeprom-edid";
				reg = <0x50>;
			};
		};

		dc@0x400c0000 {
			compatible = "loongson,display-subsystem";
			reg = <0 0x400c0000 0 0x00010000>;
			interrupt-parent = <&icu>;
			interrupts = <28>;
			dma-mask = <0x00000000 0xffffffff>;
		};

		gpu@0x40080000 {
			compatible = "vivante,gc";
			reg = <0 0x40080000 0 0x00040000>;
			interrupt-parent = <&icu>;
			interrupts = <29>;
			dma-mask = <0x00000000 0xffffffff>;
		};

		ahci@0x400e0000 {
			compatible = "snps,spear-ahci";
			reg = <0 0x400e0000 0 0x10000>;
			interrupt-parent = <&icu>;
			interrupts = <19>;
			dma-mask = <0x0 0xffffffff>;
		};

		rtc0: rtc@1fe27800{
			#address-cells = <1>;
			#size-cells = <1>;
			compatible = "loongson,ls-rtc";
			reg = <0 0x1fe27800 0 0x100>;
			regmap = <&pmc>;
			interrupt-parent = <&icu>;
			interrupts = <52>;
		};

		pwm0: pwm@1fe22000{
			compatible = "loongson,ls2k-pwm";
			reg = <0 0x1fe22000 0 0x10>;
			clock-frequency = <125000000>;
			interrupt-parent = <&icu>;
			interrupts = <24>;
			#pwm-cells = <2>;
		};

		pwm1: pwm@1fe22010{
			compatible = "loongson,ls2k-pwm";
			reg = <0 0x1fe22010 0 0x10>;
			clock-frequency = <125000000>;
			interrupt-parent = <&icu>;
			interrupts = <25>;
			#pwm-cells = <2>;
		};

		gmac0: ethernet@0x40040000 {
			compatible = "snps,dwmac-3.70a", "ls,ls-gmac";
			reg = <0 0x40040000 0 0x8000>;
			interrupt-parent = <&icu>;
			interrupts = <12 13>;
			interrupt-names = "macirq", "eth_wake_irq";
		/*	mac-address = [ 64 48 48 48 48 60 ];*//* [>mac 64:48:48:48:48:60 <]*/
			phy-mode = "rgmii";
			bus_id = <0x0>;
			phy_addr = <0xffffffff>;
			dma-mask = <0xffffffff 0xffffffff>;
		};

		gmac1: ethernet@0x40050000 {
			compatible = "snps,dwmac-3.70a", "ls,ls-gmac";
			reg = <0 0x40050000 0 0x8000>;
			interrupt-parent = <&icu>;
			interrupts = <14 15>;
			interrupt-names = "macirq", "eth_wake_irq";
		/*	mac-address = [ 64 48 48 48 48 61 ];*//* [>mac 64:48:48:48:48:61 <]*/
			phy-mode = "rgmii";
			bus_id = <0x1>;
			phy_addr = <0xffffffff>;
			dma-mask = <0xffffffff 0xffffffff>;
		};

		/* APB DMA controller nodes:
		 * apbdma node specify the commom property for dma node.
		 * the #config-nr must be 2,Used to provide APB sel region
		 * and APB DMA controler information.
		 */
		apbdma: apbdma@1fe00438{
			compatible = "loongson,ls-apbdma";
			reg = <0 0x1fe00438 0 0x8>;
			#config-nr = <2>;
		};
		/* DMA node should specify the apbdma-sel property using a
		 * phandle to the controller followed by number of APB sel
		 * region(max 9) and number of APB DMA controller(max 4).
		*/

		dma0: dma@1fe00c00 {
			compatible = "loongson,ls-apbdma-0";
			reg = <0 0x1fe00c00 0 0x8>;
			apbdma-sel = <&apbdma 0x0 0x0>;
			#dma-cells = <1>;
			dma-channels = <1>;
			dma-requests = <1>;
		};

		dma1: dma@1fe00c10 {
			compatible = "loongson,ls-apbdma-1";
			reg = <0 0x1fe00c10 0 0x8>;
			apbdma-sel = <&apbdma 0x5 0x1>;
			#dma-cells = <1>;
			dma-channels = <1>;
			dma-requests = <1>;
		};

		dma2: dma@1fe00c20 {
			compatible = "loongson,ls-apbdma-2";
			reg = <0 0x1fe00c20 0 0x8>;
			apbdma-sel = <&apbdma 0x6 0x2>;
			#dma-cells = <1>;
			dma-channels = <1>;
			dma-requests = <1>;
		};

		dma3: dma@1fe00c30 {
			compatible = "loongson,ls-apbdma-3";
			reg = <0 0x1fe00c30 0 0x8>;
			apbdma-sel = <&apbdma 0x7 0x3>;
			#dma-cells = <1>;
			dma-channels = <1>;
			dma-requests = <1>;
		};

		dma4: dma@1fe00c40 {
			compatible = "loongson,ls-apbdma-4";
			apbdma-sel = <&apbdma 0x0 0x0>;
			reg = <0 0x1fe00c40 0 0x8>;
			#dma-cells = <1>;
			dma-channels = <1>;
			dma-requests = <1>;
		};

		sdio@0x1fe2c000 {
			#address-cells = <2>;
			compatible = "loongson,ls2k_sdio";
			reg = <0 0x1fe2c000 0 0x1000>;
			interrupt-parent = <&icu>;
			interrupts = <31>;
			interrupt-names = "ls2k_mci_irq";
			clock-frequency = <0 125000000>;

			cd-gpio = <&pioA 22 0>;
			dmas = <&dma1 1>;
			dma-names = "sdio_rw";
			dma-mask = <0xffffffff 0xffffffff>;
		};

		spi0: spi@1fff0220{
			compatible = "loongson,ls-spi";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0 0x1fff0220 0 0x10>;
			spidev@0{
				compatible = "rohm,dh2228fv";
				spi-max-frequency = <100000000>;
				reg = <0>;
			};
		};

		nand@0x1fe26040{
			#address-cells = <2>;
			compatible = "loongson,ls-nand";
			reg = <0 0x1fe26040 0 0x0
			       0 0x1fe26000 0 0x20>;
			interrupt-parent = <&icu>;
			interrupts = <44>;
			interrupt-names = "nand_irq";

			dmas = <&dma0 1>;
			dma-names = "nand_rw";
			dma-mask = <0xffffffff 0xffffffff>;

			number-of-parts = <0x2>;
			partitions {
				compatible = "fixed-partitions";
				#address-cells = <2>;
				#size-cells = <2>;

				partition@0 {
					label = "kernel_partition";
					reg = <0 0x0000000 0 0x01400000>;
				};

				partition@0x01400000 {
					label = "os_partition";
					reg = <0 0x01400000 0 0x0>;
				};
			};
		};

		/* CAN controller nodes:
		 * If you want to use the "can" function,enable the "can"
		 * controller by configure general configuration register 0.
		 */
/*		can0: can@1fe20c00{
			compatible = "nxp,sja1000";
			reg = <0 0x1fe20c00 0 0xff>;
			nxp,external-clock-frequency = <125000000>;
			interrupt-parent = <&icu>;
			interrupts = <16>;
		};
		can1: can@1fe20d00{
			compatible = "nxp,sja1000";
			reg = <0 0x1fe20d00 0 0xff>;
			nxp,external-clock-frequency = <125000000>;
			interrupt-parent = <&icu>;
			interrupts = <17>;
		};*/

		hda@0x400d0000 {
			compatible = "loongson,ls2k-audio";
			reg = <0 0x400d0000 0 0xffff>;
			interrupt-parent = <&icu>;
			interrupts = <4>;
		};

		vpu@0x79000000 {
			compatible = "loongson,ls-vpu";
			reg = <0 0x79000000 0 0xffff>;
			interrupt-parent = <&icu>;
			interrupts = <30>;
		};

		pcie@0 {
			compatible = "loongson,ls2k1000-pci";
			#interrupt-cells = <1>;
			bus-range = <0x1 0x16>;
			#size-cells = <2>;
			#address-cells = <3>;

			reg = < 0xfe 0x00000000 0 0x20000000>;
			ranges = <0x2000000 0x0 0x60000000 0 0x60000000 0x0 0x20000000 /* mem */
				0x01000000 0 0x00008000 0 0x18008000 0x0 0x8000>;

			pcie0_port0: pci_bridge@9,0 {
				compatible = "pciclass060400",
						   "pciclass0604";
				reg = <0x4800 0x0 0x0 0x0 0x0>;
				interrupts = <32>;
				interrupt-parent = <&icu>;

				#interrupt-cells = <1>;
				interrupt-map-mask = <0 0 0 0>;
				interrupt-map = <0 0 0 0 &icu 32>;
			};

			pcie0_port1: pci_bridge@10,0 {
				compatible = "pciclass060400",
						   "pciclass0604";
				reg = <0x5000 0x0 0x0 0x0 0x0>;
				interrupts = <33>;
				interrupt-parent = <&icu>;

				#interrupt-cells = <1>;
				interrupt-map-mask = <0 0 0 0>;
				interrupt-map = <0 0 0 0 &icu 33>;
			};

			pcie0_port2: pci_bridge@11,0 {
				compatible = "pciclass060400",
						   "pciclass0604";
				reg = <0x5800 0x0 0x0 0x0 0x0>;
				interrupts = <34>;
				interrupt-parent = <&icu>;

				#interrupt-cells = <1>;
				interrupt-map-mask = <0 0 0 0>;
				interrupt-map = <0 0 0 0 &icu 34>;
			};

			pcie_port3: pci_bridge@12,0 {
				compatible = "pciclass060400",
						   "pciclass0604";
				reg = <0x6000 0x0 0x0 0x0 0x0>;
				interrupts = <35>;
				interrupt-parent = <&icu>;

				#interrupt-cells = <1>;
				interrupt-map-mask = <0 0 0 0>;
				interrupt-map = <0 0 0 0 &icu 35>;
			};

			pcie1_port0: pci_bridge@13,0 {
				compatible = "pciclass060400",
						   "pciclass0604";
				reg = <0x6800 0x0 0x0 0x0 0x0>;
				interrupts = <36>;
				interrupt-parent = <&icu>;

				#interrupt-cells = <1>;
				interrupt-map-mask = <0 0 0 0>;
				interrupt-map = <0 0 0 0 &icu 36>;
			};

			pcie1_port1: pci_bridge@14,0 {
				compatible = "pciclass060400",
						   "pciclass0604";
				reg = <0x7000 0x0 0x0 0x0 0x0>;
				interrupts = <37>;
				interrupt-parent = <&icu>;

				#interrupt-cells = <1>;
				interrupt-map-mask = <0 0 0 0>;
				interrupt-map = <0 0 0 0 &icu 37>;
			};

		};
	};
};
