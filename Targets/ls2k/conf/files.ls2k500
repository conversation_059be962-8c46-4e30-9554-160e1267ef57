# $Id: files.Bonito
#
# Bonito Target specific files
#

file	Targets/ls2k/pci/pci_machdep.c
file	Targets/ls2k/cache_stage/cache_stage.c
file	Targets/ls2k/ls2k500/tgt_machdep.c
file	Targets/ls2k/ls2k500/resume.c
file	Targets/ls2k/ls2k500/pincfgs.c
file	Targets/ls2k/dev/dc.c
file	Targets/ls2k/pci/ls2k_pci.c
file	Targets/ls2k/dev/fl_env.c

define	localbus { [base = -1 ] }
device	localbus
attach	localbus at mainbus
file	Targets/ls2k/dev/localbus.c		localbus
file	Targets/ls2k/dev/spi_w.c
#file	Targets/ls2k/dev/nand_opt.c
#file	Targets/ls2k/dev/set_cpu_ddr_freq.c
#file	Targets/ls2k/dev/set_vol.c
#file	Targets/ls2k/dev/rtc.c
file	Targets/ls2k/dev/eeprom.c
file	Targets/ls2k/dev/load_dtb.c		cmd_dtb
#file	Targets/ls2k/dev/signal_test.c
#file	Targets/ls2k/dev/slt.c
#file	Targets/ls2k/dev/hda_test.c
#file	Targets/ls2k/dev/sgl_instruction_ts.c
#file	Targets/ls2k/dev/gmac_test.c
#file	Targets/ls2k/dev/st7701s.c st7701s needs-flag

#XHCI
device	lxhci  : xhcibus
attach	lxhci at localbus

# OHCI
#device	lohci {[channel = -1]} :usbbus
#attach	lohci at localbus

#XHCI
#device	lxhci  : xhcibus
#attach	lxhci at localbus

# GMAC
#file	sys/dev/gmac/synopGMAC_Host.c
file	sys/dev/gmac/synopGMAC_Dev.c
file	sys/dev/gmac/synopGMAC_network_interface.c
file	sys/dev/gmac/synopGMAC_pci_bus_interface.c
file	sys/dev/gmac/synopGMAC_plat.c

# NAND
#file	sys/dev/nand/ls2k500-nand.c nand
file	sys/dev/nand/m25p80.c   m25p80 & nand needs-flag

# SDIO
#device	sdcard
#attach	sdcard at localbus
#file	Targets/ls2k/dev/mmc_sdio.c	sdcard

device	syn: ether, ifnet
attach	syn at localbus
file	sys/dev/gmac/if_gmac.c		gmac


# Ethernet driver for Discovery ethernet
device	gt: ether, ifnet, ifmedia
attach	gt at localbus
file	sys/dev/ic/if_gt.c		gt

# AHCI
device	lahci {[channel = -1]} :ahcibus
attach	lahci at localbus

#
# Media Indepedent Interface (mii)
#
include "sys/dev/mii/files.mii"

# Various PCI bridges

include "sys/dev/pci/files.pci"

#device	pcisyn: ether, ifnet
#attach	pcisyn at pci

device	lohci {[channel = -1]} :usbbus
attach	lohci at localbus

device	lehci {[channel = -1]} :usbbus
attach	lehci at localbus
#
# Machine-independent ATAPI drivers
#

include "sys/dev/ata/files.ata"

#
#  SCSI framework
#
include "sys/scsi/files.scsi"

#
# Custom application files
#
include "pmon/custom/files.custom"
