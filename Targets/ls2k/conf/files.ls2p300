# $Id: files.Bonito
#
# Bonito Target specific files
#

file	Targets/ls2k/pci/pci_machdep.c
file	Targets/ls2k/cache_stage/cache_stage.c
file	Targets/ls2k/ls2p300/tgt_machdep.c
file	Targets/ls2k/ls2p300/pincfgs.c
#file    Targets/ls2k/ls2p300/serial.S
#file    Targets/ls2k/ls2p300/cpulib.S
file	Targets/ls2k/dev/dc.c
file	Targets/ls2k/pci/ls2k_pci.c
file	Targets/ls2k/dev/fl_env.c
file    sys/dev/printer/load_prt.c
#file    sys/dev/printer/st7735gpio.c
file    sys/dev/printer/st7735.c
file    sys/dev/tcm/tcm2_tis_spi.c          cmd_tcm2
file    sys/dev/tcm/tcm2_device.c           cmd_tcm2
file    sys/dev/tcm/tcm2_sm3.c              cmd_tcm2
file    sys/dev/tcm/tcm2_mailbox.c          cmd_tcm2
file    sys/dev/tcm/tcm2_command.c          cmd_tcm2
file    sys/dev/tcm/tcm2_key.c              cmd_tcm2
file    sys/dev/tcm/tcm2_log.c              cmd_tcm2
file    sys/dev/tcm/tcm2_sm4.c              cmd_tcm2
file    sys/dev/tcm/tcm2_sm2.c              cmd_tcm2
file    sys/dev/tcm/tcm2_sigver.c           cmd_tcm2
file    sys/dev/tcm/tcm2_test.c             cmd_tcm2
file    sys/dev/tcm/tcm2_context.c          cmd_tcm2
file    sys/dev/tcm/tcm2_nv.c               cmd_tcm2
file    sys/dev/tcm/tcm2_pcr.c              cmd_tcm2
file    sys/dev/tcm/tcm2_metric.c           cmd_tcm2
file    Targets/ls2k/dev/mailbox_cmd.c

define	localbus { [base = -1 ] }
device	localbus
attach	localbus at mainbus
file	Targets/ls2k/dev/localbus.c		localbus
file	Targets/ls2k/dev/spi_w.c
file    Targets/ls2k/dev/spi.c
file    Targets/ls2k/dev/gmac_mac_init.c
#file	Targets/ls2k/dev/nand_opt.c
#file	Targets/ls2k/dev/set_cpu_ddr_freq.c
#file	Targets/ls2k/dev/set_vol.c
#file	Targets/ls2k/dev/rtc.c
file	Targets/ls2k/dev/eeprom.c
file	Targets/ls2k/dev/load_dtb.c		cmd_dtb
#file	Targets/ls2k/dev/signal_test.c
#file	Targets/ls2k/dev/slt.c
#file	Targets/ls2k/dev/hda_test.c
#file	Targets/ls2k/dev/sgl_instruction_ts.c
#file	Targets/ls2k/dev/gmac_test.c
#file	Targets/ls2k/dev/st7701s.c st7701s needs-flag
file	Targets/ls2k/dev/qspi_w.c   nand

#ADC

file	Targets/ls2k/dev/ls2p300_dma.c
file    Targets/ls2k/dev/adc/ls2p300_adc.c
file    Targets/ls2k/dev/adc/adc_test.c

#XHCI
device	lxhci  : xhcibus
attach	lxhci at localbus

# OHCI
#device	lohci {[channel = -1]} :usbbus
#attach	lohci at localbus

#XHCI
#device	lxhci  : xhcibus
#attach	lxhci at localbus

# OTG Device
file sys/dev/usb/otg-dev/dwc2_udc.c		otg-device
file sys/dev/usb/otg-dev/dwc2_udc_otg.c		otg-device
file sys/dev/usb/otg-dev/printer.c		otg-device
file sys/dev/usb/otg-dev/gadget_config.c	otg-device
file sys/dev/usb/otg-dev/usbstring.c		otg-device
file sys/dev/usb/otg-dev/epautoconf.c		otg-device
file sys/dev/usb/otg-dev/otg_cmd.c		otg-device


# GMAC
#file	sys/dev/gmac/synopGMAC_Host.c
file	sys/dev/gmac/synopGMAC_Dev.c
file	sys/dev/gmac/synopGMAC_network_interface.c
file	sys/dev/gmac/synopGMAC_pci_bus_interface.c
file	sys/dev/gmac/synopGMAC_plat.c

# NAND
#file	sys/dev/nand/ls2p300-nand.c nand
file	sys/dev/nand/m25p80.c   m25p80 & nand needs-flag
file	sys/dev/nand/qspi_nand.c nand   

device emmc
attach emmc at localbus

#SDCARD
device	tfcard
attach	tfcard at localbus

file sys/dev/mmc/mmc.c       mci
file sys/dev/mmc/sd_card.c   mci
file sys/dev/mmc/core.c      mci needs-flag 
file sys/dev/mmc/ls_mci.c    mci 
file sys/dev/mmc/mci_pmon.c  mci


device	syn: ether, ifnet
attach	syn at localbus
file	sys/dev/gmac/if_gmac.c		gmac


# Ethernet driver for Discovery ethernet
device	gt: ether, ifnet, ifmedia
attach	gt at localbus
file	sys/dev/ic/if_gt.c		gt

# AHCI
device	lahci {[channel = -1]} :ahcibus
attach	lahci at localbus

#
# Media Indepedent Interface (mii)
#
include "sys/dev/mii/files.mii"

# Various PCI bridges

include "sys/dev/pci/files.pci"

#device	pcisyn: ether, ifnet
#attach	pcisyn at pci

device  lotg {[channel = -1]} :usbbus
attach  lotg at localbus

device	lohci {[channel = -1]} :usbbus
attach	lohci at localbus

device	lehci {[channel = -1]} :usbbus
attach	lehci at localbus

#
# Machine-independent ATAPI drivers
#

include "sys/dev/ata/files.ata"

#
#  SCSI framework
#
include "sys/scsi/files.scsi"

#
# Custom application files
#
include "pmon/custom/files.custom"
