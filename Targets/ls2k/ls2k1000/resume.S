#ifdef LS_STR
/*
 * 2K1000 STR config start
 */
	.org 0x500
	/*************************************************************************
	/* This Code Must Be Execute Before Memory SelfRefresh Begain,
	/* Because Once We Enter SelfRefresh Mode,Memory Can't Be Accessed Any More
	/* We Leave Poweroff Op Later(After Enter SelfRefresh Mode)
	**************************************************************************/
	/* store ra and sp to memory */

	li.d	t0, STR_STORE_BASE
	st.d	a0, t0, 0x40	//store ra
	st.d    a1, t0, 0x48	//store sp
	li.w	t1, 0x5a5a5a5a
	st.w    t1, t0, 0x50	//store	flag

	li.d	t2, 0x800000001fe27050
	st.w	t1, t2, 0x0 //store mem_ctrl_flag

	li.w      t1, 0x100
1:
	addi.w  t1, t1, -1
	bnez    t1, 1b

	/*set memory controller selfrefresh*/
	/* Enable DDR control register */
	li.d	t0, LS2K1000_GENERAL_CFG0
	ld.d    t1, t0, 0
	li.d	t2, (1 << 41)
	or		t1, t1, t2
	st.d    t1, t0, 0

	li.d	t2, ~(1 << 40)
	and		t1, t1, t2
	st.d    t1, t0, 0
	dbar    0 //sync

	/* Is necessary? */
/*
	li.w  	a0, 0x0
	li.d 	t0, 0x800000001fe10000

	STR_XBAR_CONFIG_NODE_a0(0x10, \
			0x0000000000000000, \
			0xfffffffff0000000, \
			0x00000000000000f0)
*/
	li.d 	a0, 0x800000000ff00000
	ld.d	t1, a0, 0x198
	li.d	t3, 0x0000000f00000000
	or  	t1, t1, t3  /* bit32 for self refresh*/
	st.d  	t1, a0, 0x198
	dbar    0

	/* Don't forget to recovery the XbarII config Window is necessary? */
/*
	li.w  a0,0x0
	li.d t0, 0x800000001fe10000
	STR_XBAR_CONFIG_NODE_a0(0x10, \
			0x0000000000000000, \
			0xfffffffff0000000, \
			0x00000000000000f0)
*/
	dbar    0 //sync
	dbar    0 //sync
	dbar    0 //sync
	dbar    0 //sync

	/* delay */
	li.w	t0, 0x4000
1:
	addi.w	t0, t0, -1
	bnez	t0, 1b

	li.d     t0, LS2K1000_ACPI_REG_BASE
	/* set key,usb wakeup of reg GPE0_EN */
	ld.w    t1, t0,0x2c
	li.w      t3, (0x1 << 8)|(0x3f<<10)
	or      t1, t1, t3
	st.w    t1, t0,0x2c

	/* clear 0-15 of reg GPE0_STS */
	ld.w    t1, t0,0x28
	li.w      t3, 0x0000ffff
	st.w    t3, t0,0x28

	/* clear 0-15 of reg PM1_STS */
	ld.w    t1, t0,0x0c
	li.w      t3, 0x0000ffff
	st.w    t3, t0,0x0c

	/* set wake on line */
	ld.w    t1, t0,0x4
	li.w      t3, 0x80
	or      t1, t1, t3
	st.w    t1, t0,0x4

	/* set vsb_gat_delay */
	ld.w    t1, t0,0x4
	li.w      t3, 0x5 << 11
	or      t1, t1, t3
	li.d     t3, 0xefff
	and     t1, t1, t3
	st.w    t1, t0,0x4

	li.w 	a0, 'S'
	bl 	tgt_putchar
	li.w 	a0, '3'
	bl 	tgt_putchar

	/* set reg PM1_CNT to get into S3*/
	li.w      t3, 0x00003400
	st.w    t3, t0,0x14

	/* delay */
	li.w  t0, 0x40000
2:
	addi.w  t0, t0, -1
	bnez    t0, 2b

1:
	b  1b
#endif
