/*	$Id: pci_machdep.c,v 1.1.1.1 2006/09/14 01:59:09 root Exp $ */

/*
 * Copyright (c) 2001 Opsycon AB  (www.opsycon.se)
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *	This product includes software developed by Opsycon AB, Sweden.
 * 4. The name of the author may not be used to endorse or promote products
 *    derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 */

#include <sys/param.h>
#include <sys/device.h>
#include <sys/systm.h>

#include <sys/malloc.h>

#include <dev/pci/pcivar.h>
#include <dev/pci/pcireg.h>
#include <dev/pci/nppbreg.h>

#include <machine/bus.h>

#include "include/bonito.h"

#include <pmon.h>

extern void *pmalloc __P((size_t ));
extern int _pciverbose;

extern char hwethadr[6];
struct pci_device *_pci_bus[16];
int _max_pci_bus = 0;

/* PCI mem regions in PCI space */

/* soft versions of above */
static pcireg_t pci_local_mem_pci_base;


/****************************/
/*initial PCI               */
/****************************/

int
_ls2k_pci_hwinit(initialise, iot, memt)
	int initialise;
	bus_space_tag_t iot;
	bus_space_tag_t memt;
{
	/*pcireg_t stat;*/
	struct pci_device *pd;
	struct pci_bus *pb;
	int newcfg=0;
	char *env;
	SBD_DISPLAY ("HW-0", 0);
	if(getenv("newcfg"))newcfg=1;

	if (!initialise) {
		return(0);
	}
	SBD_DISPLAY ("HW-1", 0);

	/*
	 *  Allocate and initialize PCI bus heads.
	 */

	/*
	 * PCI Bus 0
	 */
	pd = pmalloc(sizeof(struct pci_device));
	pb = pmalloc(sizeof(struct pci_bus));
	if(pd == NULL || pb == NULL) {
		printf("pci: can't alloc memory. pci not initialized\n");
		return(-1);
	}
	SBD_DISPLAY ("HW-2", 0);

	pd->pa.pa_flags = PCI_FLAGS_IO_ENABLED | PCI_FLAGS_MEM_ENABLED;
	pd->pa.pa_iot = pmalloc(sizeof(bus_space_tag_t));
	pd->pa.pa_iot->bus_reverse = 1;
	pd->pa.pa_iot->bus_base = BONITO_PCIIO_BASE_VA;
	pd->pa.pa_memt = pmalloc(sizeof(bus_space_tag_t));
	pd->pa.pa_memt->bus_reverse = 1;
	pd->pa.pa_memt->bus_base = PHYS_TO_UNCACHED(0x40000000ULL);//0xc0000000;
	pd->pa.pa_dmat = &bus_dmamap_tag;
	pd->bridge.secbus = pb;
	_pci_head = pd;
	SBD_DISPLAY ("HW-3", 0);

	pb->minpcimemaddr  = 0x40100000;
	pb->nextpcimemaddr = 0x80000000;
	pb->minpciioaddr   = PCI_IO_SPACE_BASE + 0x0004000;
	pb->nextpciioaddr  = PCI_IO_SPACE_BASE + BONITO_PCIIO_SIZE; // 0 + 0x0200_0000
	pb->pci_mem_base   = BONITO_PCILO_BASE_VA;
	pb->pci_io_base    = BONITO_PCIIO_BASE_VA;

	pb->max_lat = 255;
	pb->fast_b2b = 1;
	pb->prefetch = 1;
	pb->bandwidth = 4000000;
	pb->ndev = 1;
	_pci_bushead = pb;
	_pci_bus[_max_pci_bus++] = pd;

	SBD_DISPLAY ("HW-5", 0);

	bus_dmamap_tag._dmamap_offs = 0;

	/*set pci base0 address and window size*/
	pci_local_mem_pci_base = 0x0;

	return(1);
}

#ifdef LOONGARCH_2K500
static void setup_pcimap()
{
	int idx;
	/*
	 * local to PCI mapping for CPU accessing PCI space
	 * CPU address space [256M,448M] is window for accessing pci space
	 * we set pcimap_lo[0,1,2] to map it to pci space[0M,64M], [320M,448M]
	 *
	 * pcimap: PCI_MAP2  PCI_Mem_Lo2 PCI_Mem_Lo1 PCI_Mem_Lo0
	 * 	     [<2G]   [384M,448M] [320M,384M] [0M,64M]
	 */
	BONITO_PCICMD = PCI_COMMAND_IO|PCI_COMMAND_MEMORY|PCI_COMMAND_MASTER;
	BONITO_PCIMAP = LOONGSON_PCIMAP_PCIMAP_2 |
		LOONGSON_PCIMAP_WIN(2, LOONGSON_PCILO2_BASE) |
		LOONGSON_PCIMAP_WIN(1, LOONGSON_PCILO1_BASE) |
		LOONGSON_PCIMAP_WIN(0, 0);

	/*
	 * PCI-DMA to local mapping: [2G,2G+256M] -> [0M,256M]
	 */
	/* size: 256M, burst transmission, pre-fetch enable, 64bit */
	BONITO_PCI_HIT2_SEL_L = 0x8000000c; /* set this BAR as invalid */
	BONITO_PCI_HIT2_SEL_H = 0xffffffff;
	BONITO_PCI_HIT1_SEL_L = 0xf000000c;
	BONITO_PCI_HIT1_SEL_H = 0xffffffff;
	BONITO_PCI_HIT0_SEL_L = 0x00000006; /* set this BAR as invalid */
	BONITO_PCI_HIT0_SEL_H = 0x00000000;

	BONITO_PCIBASE4 = 0x80000000; /*kernel: base: 2G -> mmap: 0M */
	BONITO_PCIBASE2 = 0x00000000;

	/* avoid deadlock of PCI reading/writing lock operation */
	BONITO_PCI_ISR4C = 0xd2000001;
	BONITO_PXARB_CFG = 0x00fe0105;
#ifdef CONFIG_PCI_NONCOHERENT
	*(volatile int *)PHYS_TO_UNCACHED(0x1fe10110) &= ~4;
#else
	*(volatile int *)PHYS_TO_UNCACHED(0x1fe10110) |= 4;
#endif
}
int
_pcils3a_hwinit(initialise, iot, memt)
	int initialise;
	bus_space_tag_t iot;
	bus_space_tag_t memt;
{
	/*pcireg_t stat;*/
	struct pci_device *pd;
	struct pci_bus *pb;
	int newcfg=0;

	if (!initialise) {
		return(0);
	}

	/*
	 *  Allocate and initialize PCI bus heads.
	 */

	/*
	 * PCI Bus 0
	 */
	pd = pmalloc(sizeof(struct pci_device));
	pb = pmalloc(sizeof(struct pci_bus));
	if (pd == NULL || pb == NULL) {
		printf("pci: can't alloc memory. pci not initialized\n");
		return(-1);
	}

	pd->pa.pa_flags = PCI_FLAGS_IO_ENABLED | PCI_FLAGS_MEM_ENABLED;
	pd->pa.pa_iot = pmalloc(sizeof(bus_space_tag_t));
	pd->pa.pa_iot->bus_reverse = 1;
	pd->pa.pa_iot->bus_base = PHYS_TO_UNCACHED(0x17000000);
	pd->pa.pa_memt = pmalloc(sizeof(bus_space_tag_t));
	pd->pa.pa_memt->bus_reverse = 1;
	pd->pa.pa_dmat = &bus_dmamap_tag;
	pd->bridge.secbus = pb;
	_pci_head->next = pd;

	pb->minpcimemaddr  = 0x20000000;
	pb->nextpcimemaddr = 0x30000000;
	pd->pa.pa_memt->bus_base = PHYS_TO_UNCACHED(0x0);
	pb->minpciioaddr  = 0x0004000;
	pb->nextpciioaddr = 0x10000;
	pb->pci_mem_base   = PHYS_TO_UNCACHED(0x0);
	pb->pci_io_base    = PHYS_TO_UNCACHED(0x17000000);

	pb->max_lat = 255;
	pb->fast_b2b = 1;
	pb->prefetch = 1;
	pb->bandwidth = 4000000;
	pb->ndev = 1;

	_pci_bushead->next = pb;
	_pci_bus[_max_pci_bus++] = pd;

	bus_dmamap_tag._dmamap_offs = 0;

/*set pci base0 address and window size*/
	pci_local_mem_pci_base = 0x0;
	setup_pcimap();

	loop_set_pin(100, 148, 5, 0);
#ifdef PCI_INT_GPIO
	loop_set_pin(PCI_INT_GPIO, PCI_INT_GPIO, 0, 0);
#endif
	return(1);
}
#endif

int _pci_hwinit(initialise, iot, memt)
	int initialise;
	bus_space_tag_t iot;
	bus_space_tag_t memt;
{
	struct pci_device *pd; 
	_ls2k_pci_hwinit(initialise, iot, memt);
#ifdef LS2K500_HAVE_PCI
	_pcils3a_hwinit(initialise, iot, memt);
	return 2;
#else
	return 1;
#endif
}
/*
 * Called to reinitialise the bridge after we've scanned each PCI device
 * and know what is possible. We also set up the interrupt controller
 * routing and level control registers.
 */
void _pci_hwreinit (void)
{
}

void _pci_flush (void)
{
}

/*
 *  Map the CPU virtual address of an area of local memory to a PCI
 *  address that can be used by a PCI bus master to access it.
 */
vm_offset_t
_pci_dmamap(va, len)
	vm_offset_t va;
	unsigned int len;
{
	return(pci_local_mem_pci_base + VA_TO_PA (va));
}


/*
 *  Map the PCI address of an area of local memory to a CPU physical
 *  address.
 */
vm_offset_t
_pci_cpumap(pcia, len)
	vm_offset_t pcia;
	unsigned int len;
{
	return PA_TO_VA(pcia - pci_local_mem_pci_base);
}


/*
 *  Make pci tag from bus, device and function data.
 */
pcitag_t
_pci_make_tag(bus, device, function)
	uint8_t bus;
	uint8_t device;
	uint8_t function;
{
	pcitag_t tag;

	tag = (bus << 16) | (device << 11) | (function << 8);
	return(tag);
}

/*
 *  Break up a pci tag to bus, device function components.
 */
void
_pci_break_tag(tag, busp, devicep, functionp)
	pcitag_t tag;
	uint8_t *busp;
	uint8_t *devicep;
	uint8_t *functionp;
{
	if (busp) {
		*busp = (tag >> 16) & 255;
	}
	if (devicep) {
		*devicep = (tag >> 11) & 31;
	}
	if (functionp) {
		*functionp = (tag >> 8) & 7;
	}
}

int _pci_canscan (pcitag_t tag)
{
	uint8_t bus, device, function;

	_pci_break_tag (tag, &bus, &device, &function);
	return (1);
}

void
pci_sync_cache(p, adr, size, rw)
	void *p;
	vm_offset_t adr;
	size_t size;
	int rw;
{
//	CPU_IOFlushDCache(adr, size, rw);
}

int pci_get_busno(struct pci_device *pd, int bus)
{
	int ret = bus + 1;
	if(!pd->pa.pa_bus)
	{
		switch(pd->pa.pa_device)
		{
			case 9:
				ret = 1;
				break;
			case 0xa:
				ret = 4;
				break;
			case 0xb:
				ret = 8;
				break;
			case 0xc:
				ret = 0xc;
				break;
			case 0xd:
				ret = 0x10;
				break;
			case 0xe:
				ret = 0x14;
				break;
		}
	}

	return ret;
}

#ifdef LOONGARCH_2K1000
extern struct pci_config_data pci_config_array[];
extern int pci_config_array_size;
static char pci_dev_index[0x12*4];

int __attribute__ ((constructor)) build_pci() {
	int i;
	for(i = 0;i < pci_config_array_size ;i++){
		pci_dev_index [((pci_config_array[i].dev<<2) |pci_config_array[i].func) ] = i;
	}
	return 0;
}
#endif

pcireg_t pci_alloc_fixmemio(struct pci_win *pm)
{
	int idx;
	struct pci_device *pd = pm->device;
	int reg = pm->reg;
	size_t size;
#if defined(LOONGARCH_2K1000)
	if(!pd->pa.pa_bus)
	{
		idx = pci_dev_index [((pd->pa.pa_device<<2) |pd->pa.pa_function)];
		if(idx)
		{
			if(pci_config_array[idx].type == PCI_DEV)
			{
				if(reg == 0x10)
				{
					size = pci_config_array[idx].mem_end - pci_config_array[idx].mem_start + 1;
					if(size < pm->size || pci_config_array[idx].mem_start & (pm->size - 1))
						_pci_tagprintf (pd->pa.pa_tag, "alloced resource size or alignment failed,want 0x%x/0x%x, got 0x%x/0x%x\n", pci_config_array[idx].mem_start, size, pci_config_array[idx].mem_start & ~(pm->size -1), pm->size);
					return pci_config_array[idx].mem_start;
				}
			}
			else
			{

				if(reg == PCI_MEMBASE_1)
				{
					size = pci_config_array[idx].mem_end - pci_config_array[idx].mem_start + 1;
					if(size < pm->size || pci_config_array[idx].mem_start & (pm->size - 1))
						_pci_tagprintf (pd->pa.pa_tag, "alloced resource size or alignment failed,want 0x%x/0x%x, got 0x%x/0x%x\n", pci_config_array[idx].mem_start, size, pci_config_array[idx].mem_start & ~(pm->size -1), pm->size);
					pm->size = size;
					return pci_config_array[idx].mem_start;
				}

				if(reg == PCI_IOBASEL_1)
				{
					size = pci_config_array[idx].io_end - pci_config_array[idx].io_start + 1;
					if(size < pm->size || pci_config_array[idx].mem_start & (pm->size - 1))
						_pci_tagprintf (pd->pa.pa_tag, "alloced resource size or alignment failed,want 0x%x/0x%x, got 0x%x/0x%x\n", pci_config_array[idx].io_start & LS2K_PCI_IO_MASK, size, pci_config_array[idx].io_start & ~(pm->size -1) & LS2K_PCI_IO_MASK, pm->size);
					pm->size = size;
					return  pci_config_array[idx].io_start & LS2K_PCI_IO_MASK ;
				}
			}
		}
	}
#elif defined(LOONGARCH_2K500)
	/*only bridge address is fixed so judge pm->reg*/
	if (((pd->pa.pa_class & (0xff << 24)) == (0x6 << 24)) && (pm->reg == 0x10) && (pd->pa.pa_bus == 0)) {
		if (pd->pa.pa_device == 0)
			return 0x16000000;
		else
			return 0x16001000;
	}
#endif
	return -1;
}
