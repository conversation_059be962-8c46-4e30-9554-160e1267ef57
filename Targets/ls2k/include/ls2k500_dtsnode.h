char *node_name[] = {
"/soc/ahci@0x1f040000",
"/soc/apbdma@0x1fe10100",
"/soc/can@1ff44000",
"/soc/can@1ff45000",
"/soc/cpu@10000",
"/soc/dc@0x1f010000",
"/soc/dc_io@0x1fe10000",
"/soc/dma@0x1fe10c00",
"/soc/dma@0x1fe10c10",
"/soc/dma@0x1fe10c20",
"/soc/dma@0x1fe10c30",
"/soc/eeprom@57",
"/soc/ehci@0x1f050000",
"/soc/ethernet@0x1f020000",
"/soc/ethernet@0x1f030000",
"/soc/gpio@0x1fe10430",
"/soc/gpio@0x1fe10450",
"/soc/i2c@0x1ff48000",
"/soc/i2c@0x1ff48800",
"/soc/i2c@0x1ff49000",
"/soc/i2c@0x1ff49800",
"/soc/interrupt-controller@0x1fe11600",
"/soc/interrupt-controller@1fe11400",
"/soc/isa@16400000",
"/soc/nand@0x1ff58040",
"/soc/ohci@0x1f058000",
"/soc/partition@0",
"/soc/partition@0x01e00000",
"/soc/pci@0x16000000",
"/soc/pci_bridge@0,0",
"/soc/pci_bridge@1,0",
"/soc/pix0_pll@0x1fe10418",
"/soc/pix1_pll@0x1fe10420",
"/soc/pixi2c@0x1ff4a000",
"/soc/pixi2c@0x1ff4a800",
"/soc/sdio0@0x1ff64000",
"/soc/sdio1@0x1ff66000",
"/soc/serial@0x1ff40800",
"/soc/spi@0x1fd00000",
"/soc/spi@0x1fd40000",
"/soc/spi@0x1ff50000",
"/soc/spi@0x1ff51000",
"/soc/spi@0x1ff52000",
"/soc/spi@0x1ff53000",
"/soc/spidev@0",
"/soc/syscon@0x1ff6c000",
};
