#ifndef __PINCFGS_H__
#define __PINCFGS_H__
typedef struct pin_cfgs {
	int pin_num;
	int model;
} pin_cfgs_t;


#if defined(LOONGARCH_2K500)
#include "target/ls2k500.h"
pin_cfgs_t default_pin_cfgs[155];

#define set_pin_mode(pin_num, mode)				\
do {								\
	volatile unsigned int *addr = (unsigned int *)		\
		(LS2K500_GPIO_MULTI_CFG + (pin_num) / 8 * 4);	\
	int bit = (pin_num) % 8 * 4;				\
	*addr &= ~(7 << bit);					\
	*addr |= ((mode) << bit);				\
} while(0)

#elif defined(LOONGARCH_2P500)
#include "target/ls2p500.h"
pin_cfgs_t default_pin_cfgs[44];

#define set_pin_mode(pin_num, mode)				\
do {								\
	volatile unsigned int *addr = (unsigned int *)		\
		(LS2P500_GPIO_MULTI_CFG + (pin_num) / 16 * 4);	\
	int bit = (pin_num) % 16 * 2;				\
	*addr &= ~(0x3 << bit);					\
	*addr |= ((mode) << bit);				\
} while(0)

#elif defined(LOONGARCH_2P300)
#include "target/ls2p300.h"
pin_cfgs_t default_pin_cfgs[48];
#define set_pin_mode(pin_num, mode)				\
do {								\
	volatile unsigned int *addr = (unsigned int *)		\
		(LS2P300_GPIO_MULTI_CFG + (pin_num) / 16 * 4);	\
	int bit = (pin_num) % 16 * 2;				\
	*addr &= ~(0x3 << bit);					\
	*addr |= ((mode) << bit);				\
} while(0)




#endif
#define ARRAY_SIZE(x) (sizeof(x) / sizeof((x)[0]))
#define cfg_all_pin_multi(cfgs)					\
do {								\
	int i;							\
	for (i = 0; i < ARRAY_SIZE(cfgs); i++) {		\
		set_pin_mode(cfgs[i].pin_num, cfgs[i].model);	\
	}							\
} while(0)

#endif
