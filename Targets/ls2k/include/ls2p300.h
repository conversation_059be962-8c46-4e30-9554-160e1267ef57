#ifndef _LS2P300_H
#define _LS2P300_H
#include "cpu.h"

/* CHIP CONFIG regs */
#define LS2P300_GENERAL_CFG0				PHYS_TO_UNCACHED(0x14000100)
#define LS2P300_GENERAL_CFG1				PHYS_TO_UNCACHED(0x14000104)
#define LS2P300_GENERAL_CFG2				PHYS_TO_UNCACHED(0x14000108)
#define LS2P300_GENERAL_CFG3				PHYS_TO_UNCACHED(0x1400010c)
#define LS2P300_GENERAL_CFG4				PHYS_TO_UNCACHED(0x14000110)
#define LS2P300_GENERAL_CFG5				PHYS_TO_UNCACHED(0x14000114)
#define LS2P300_SAMPLE_CFG0				PHYS_TO_UNCACHED(0x14000120)
#define LS2P300_SAMPLE_CFG1				PHYS_TO_UNCACHED(0x14000124)
#define LS2P300_CHIP_HPT_LO				PHYS_TO_UNCACHED(0x14000130)
#define LS2P300_CHIP_HPT_HI				PHYS_TO_UNCACHED(0x14000134)

//GPIO regs
#define LS2P300_GPIO_MULTI_CFG				PHYS_TO_UNCACHED(0x14000490)
#define LS2P300_GPIO_BASE				    PHYS_TO_UNCACHED(0x14201000)

#define LS2P300_GPIO_BASE_BIT				PHYS_TO_UNCACHED(LS2P300_GPIO_BASE)

#define LS2P300_GPIO_BIT_OEN				PHYS_TO_UNCACHED(LS2P300_GPIO_BASE_BIT + 0x00)
#define LS2P300_GPIO_BIT_O				    PHYS_TO_UNCACHED(LS2P300_GPIO_BASE_BIT + 0x10)
#define LS2P300_GPIO_BIT_I				    PHYS_TO_UNCACHED(LS2P300_GPIO_BASE_BIT + 0x20)

#define LS2P300_GPIO_BIT_INT_EN			PHYS_TO_UNCACHED(LS2P300_GPIO_BASE_BIT + 0x30)
#define LS2P300_GPIO_BIT_INT_POL			PHYS_TO_UNCACHED(LS2P300_GPIO_BASE_BIT + 0x40)
#define LS2P300_GPIO_BIT_INT_EDGE			PHYS_TO_UNCACHED(LS2P300_GPIO_BASE_BIT + 0x50)
#define LS2P300_GPIO_BIT_INT_CLEAR			PHYS_TO_UNCACHED(LS2P300_GPIO_BASE_BIT + 0x60)
#define LS2P300_GPIO_BIT_INT_STS			PHYS_TO_UNCACHED(LS2P300_GPIO_BASE_BIT + 0x70)

#define LS2P300_GPIO_BASE_BYTE				PHYS_TO_UNCACHED(LS2P300_GPIO_BASE + 0x800)

#define LS2P300_GPIO_BYTE_OEN				PHYS_TO_UNCACHED(LS2P300_GPIO_BASE_BYTE + 0x000)
#define LS2P300_GPIO_BYTE_O				PHYS_TO_UNCACHED(LS2P300_GPIO_BASE_BYTE + 0x100)
#define LS2P300_GPIO_BYTE_I				PHYS_TO_UNCACHED(LS2P300_GPIO_BASE_BYTE + 0x200)

#define LS2P300_GPIO_BYTE_INT_EN			PHYS_TO_UNCACHED(LS2P300_GPIO_BASE_BYTE + 0x300)
#define LS2P300_GPIO_BYTE_INT_POL			PHYS_TO_UNCACHED(LS2P300_GPIO_BASE_BYTE + 0x400)
#define LS2P300_GPIO_BYTE_INT_EDGE			PHYS_TO_UNCACHED(LS2P300_GPIO_BASE_BYTE + 0x500)
#define LS2P300_GPIO_BYTE_INT_CLEAR		PHYS_TO_UNCACHED(LS2P300_GPIO_BASE_BYTE + 0x600)
#define LS2P300_GPIO_BYTE_INT_STS			PHYS_TO_UNCACHED(LS2P300_GPIO_BASE_BYTE + 0x700)

#define LS2P300_PRT_GPIO_MULTI_CFG			PHYS_TO_UNCACHED(0x15103040)
#define LS2P300_SCA_GPIO_MULTI_CFG			PHYS_TO_UNCACHED(0x15202040)

//USB PHY CFG
#define LS2P300_USB_PHY_CFG0				PHYS_TO_UNCACHED(0x14000500)
#define LS2P300_USB_PHY_CFG1				PHYS_TO_UNCACHED(0x14000504)

/* OTG regs */

/* USB regs */
#define LS2P300_EHCI_BASE				    PHYS_TO_UNCACHED(0x1f050000)

/* GMAC regs */

/* SPI regs */
#define LS2P300_SPI0_BASE				    PHYS_TO_UNCACHED(0x14010000)
#define LS2P300_SPI1_BASE				    PHYS_TO_UNCACHED(0x14203000)
#define LS2P300_SPI2_BASE				    PHYS_TO_UNCACHED(0x15109000)
#define LS2P300_SPI3_BASE				    PHYS_TO_UNCACHED(0x15203000)

/* UART regs */
#define LS2P300_UART0_REG_BASE				PHYS_TO_UNCACHED(0x14200000)
#define LS2P300_UART1_REG_BASE				PHYS_TO_UNCACHED(0x14200400)
#define LS2P300_UART2_REG_BASE				PHYS_TO_UNCACHED(0x14200800)
#define LS2P300_UART3_REG_BASE				PHYS_TO_UNCACHED(0x14200c00)
#define LS2P300_UART4_REG_BASE				PHYS_TO_UNCACHED(0x15100000)
#define LS2P300_UART5_REG_BASE				PHYS_TO_UNCACHED(0x15100800)
#define LS2P300_UART6_REG_BASE				PHYS_TO_UNCACHED(0x15200000)
#define LS2P300_UART7_REG_BASE				PHYS_TO_UNCACHED(0x15100800)

/* I2C regs */
#define LS2P300_I2C0_REG_BASE				PHYS_TO_UNCACHED(0x14201000)
#define LS2P300_I2C0_PRER_LO_REG			(LS2P300_I2C0_REG_BASE + 0x0)
#define LS2P300_I2C0_PRER_HI_REG			(LS2P300_I2C0_REG_BASE + 0x1)
#define LS2P300_I2C0_CTR_REG				(LS2P300_I2C0_REG_BASE + 0x2)
#define LS2P300_I2C0_TXR_REG				(LS2P300_I2C0_REG_BASE + 0x3)
#define LS2P300_I2C0_RXR_REG				(LS2P300_I2C0_REG_BASE + 0x3)
#define LS2P300_I2C0_CR_REG				(LS2P300_I2C0_REG_BASE + 0x4)
#define LS2P300_I2C0_SR_REG				(LS2P300_I2C0_REG_BASE + 0x4)

#define LS2P300_I2C1_REG_BASE				PHYS_TO_UNCACHED(0x14201800)
#define LS2P300_I2C1_PRER_LO_REG			(LS2P300_I2C1_REG_BASE + 0x0)
#define LS2P300_I2C1_PRER_HI_REG			(LS2P300_I2C1_REG_BASE + 0x1)
#define LS2P300_I2C1_CTR_REG				(LS2P300_I2C1_REG_BASE + 0x2)
#define LS2P300_I2C1_TXR_REG				(LS2P300_I2C1_REG_BASE + 0x3)
#define LS2P300_I2C1_RXR_REG				(LS2P300_I2C1_REG_BASE + 0x3)
#define LS2P300_I2C1_CR_REG				(LS2P300_I2C1_REG_BASE + 0x4)
#define LS2P300_I2C1_SR_REG				(LS2P300_I2C1_REG_BASE + 0x4)

#define LS2P300_I2C2_REG_BASE				PHYS_TO_UNCACHED(0x15101000)
#define LS2P300_I2C3_REG_BASE				PHYS_TO_UNCACHED(0x15101800)

#define CR_START                            0x80
#define CR_STOP                             0x40
#define CR_READ                             0x20
#define CR_WRITE                            0x10
#define CR_ACK                              0x8
#define CR_IACK                             0x1

#define SR_NOACK                            0x80
#define SR_BUSY                             0x40
#define SR_AL                               0x20
#define SR_TIP                              0x2
#define	SR_IF                               0x1

/* PWM regs */
#define LS2P300_PWM0_REG_BASE				PHYS_TO_UNCACHED(0x14207000)
#define LS2P300_PWM1_REG_BASE				PHYS_TO_UNCACHED(0x14207010)

/* SDIO regs */
#define LS2P300_SDIO0_BASE 				PHYS_TO_UNCACHED(0x14210000)
#define LS2P300_SDIO1_BASE 				PHYS_TO_UNCACHED(0x14218000)

/* Emmc regs */
#define LS2P300_EMMC_BASE                  PHYS_TO_UNCACHED(0x14210000)
#define	DMA_DESC_ADDR                       PHYS_TO_CACHED(0x90000000)
#define DMA_CONFREG_BASE                    (LS2P300_EMMC_BASE + 0x800)
#define EMMC_DES_ADDR                       (LS2P300_EMMC_BASE + 0x40)

#define	DMA_DESC_OADDRLOW                   0x0
#define	DMA_DESC_SADDRLOW                   0x4
#define	DMA_DESC_DADDR                      0x8
#define	DMA_DESC_LENGTH                     0xc
#define	DMA_DESC_STEPLENGTH                 0x10
#define	DMA_DESC_STEPTIMES                  0x14
#define	DMA_DESC_CMD                        0x18
#define	DMA_DESC_OADDRHIGH                  0x20
#define	DMA_DESC_SADDRHIGH                  0x24

#define SDIINTMSK                           0x3C
#define SDIO_BOOT
#define	DATA_LENGTH                         0x40000		//256*1024Byte
#define	DMA_READ_WORDS                      (DATA_LENGTH >> 2)
#define	SDIO_BLK_CNT                        (DATA_LENGTH / 512)
#define SDICON                              0x00
#define SDIPRE                              0x04
#define SDICMDARG                           0x08
#define SDICMDCON                           0x0c
#define SDICMDSTA                           0x10
#define SDIRSP0                             0x14
#define SDIRSP1                             0x18
#define SDIRSP2                             0x1C
#define SDIRSP3                             0x20
#define SDIDTIMER                           0x24
#define SDIBSIZE                            0x28
#define SDIDATCON                           0x2C
#define SDIINTMSK                           0x3C
#define SDIINTEN                            0x64
#define	CMD_INDEX                           0x12	//multi block read
#define	CMST                                0x140
#define	WAIT_RSP                            0x1
#define	LONG_RSP                            0x0
#define	CHECK_CRC                           0x1
#define	START_ADDRS                         0x2//0x0

/* HPET regs */
#define LS2P300_HPET0_BASE 				PHYS_TO_UNCACHED(0x14204000)
#define LS2P300_HPET0_PERIOD				LS2P300_HPET0_BASE + 0x4
#define LS2P300_HPET0_CONF				    LS2P300_HPET0_BASE + 0x10 
#define LS2P300_HPET0_MAIN				    LS2P300_HPET0_BASE + 0xF0 

#define LS2P300_HPET1_BASE 				PHYS_TO_UNCACHED(0x14204800)
#define LS2P300_HPET2_BASE 				PHYS_TO_UNCACHED(0x1510b000)
#define LS2P300_HPET3_BASE 				PHYS_TO_UNCACHED(0x15205000)

/* WDT regs */
#define LS2P300_MAINWDT_BASE 				PHYS_TO_UNCACHED(0x14205000)
/* RTC regs */
#define LS2P300_RTC_REG_BASE				PHYS_TO_UNCACHED(0x1510a000)
#define	LS2P300_TOY_TRIM_REG				(LS2P300_RTC_REG_BASE + 0x0020)
#define	LS2P300_TOY_WRITE0_REG				(LS2P300_RTC_REG_BASE + 0x0024)
#define	LS2P300_TOY_WRITE1_REG				(LS2P300_RTC_REG_BASE + 0x0028)
#define	LS2P300_TOY_READ0_REG				(LS2P300_RTC_REG_BASE + 0x002c)
#define	LS2P300_TOY_READ1_REG				(LS2P300_RTC_REG_BASE + 0x0030)
#define	LS2P300_TOY_MATCH0_REG				(LS2P300_RTC_REG_BASE + 0x0034)
#define	LS2P300_TOY_MATCH1_REG				(LS2P300_RTC_REG_BASE + 0x0038)
#define	LS2P300_TOY_MATCH2_REG				(LS2P300_RTC_REG_BASE + 0x003c)
#define	LS2P300_RTC_CTRL_REG				(LS2P300_RTC_REG_BASE + 0x0040)
#define	LS2P300_RTC_TRIM_REG				(LS2P300_RTC_REG_BASE + 0x0060)
#define	LS2P300_RTC_WRITE0_REG				(LS2P300_RTC_REG_BASE + 0x0064)
#define	LS2P300_RTC_READ0_REG				(LS2P300_RTC_REG_BASE + 0x0068)
#define	LS2P300_RTC_MATCH0_REG				(LS2P300_RTC_REG_BASE + 0x006c)
#define	LS2P300_RTC_MATCH1_REG				(LS2P300_RTC_REG_BASE + 0x0070)
#define	LS2P300_RTC_MATCH2_REG				(LS2P300_RTC_REG_BASE + 0x0074)

#define ADC_BASE        PHYS_TO_UNCACHED(0x15105000)
#define ADC1_BASE  (ADC_BASE)
#define ADC1            ((ADC_TypeDef *)ADC1_BASE)
#define ADC2_BASE   0x1
#define ADC3_BASE   0x2

/* DMA regs */
#define LS2P300_DMA_ORDER0                                      PHYS_TO_UNCACHED(0x14208000)

//APB
//DMA
#define DMA_BASE                          PHYS_TO_UNCACHED(0x1510a000)

#define DMA1_BASE             (DMA_BASE)                 //(DMA_BASE + 0x0000)
#define DMA1_Channel1_BASE    (DMA1_BASE + 0x0008)       //(DMA_BASE + 0x0008)
#define DMA1_Channel2_BASE    (DMA1_BASE + 0x001C)       //(DMA_BASE + 0x001C)
#define DMA1_Channel3_BASE    (DMA1_BASE + 0x0030)       //(DMA_BASE + 0x0030)
#define DMA1_Channel4_BASE    (DMA1_BASE + 0x0044)       //(DMA_BASE + 0x0044)
#define DMA1_Channel5_BASE    (DMA1_BASE + 0x0058)       //(DMA_BASE + 0x0058)
#define DMA1_Channel6_BASE    (DMA1_BASE + 0x006c)       //(DMA_BASE + 0x006C)
#define DMA1_Channel7_BASE    (DMA1_BASE + 0x0080)       //(DMA_BASE + 0x0080)
#define DMA1_Channel8_BASE    (DMA1_BASE + 0x0094)       //(DMA_BASE + 0x0094)

#define DMA1                ((DMA_TypeDef *) DMA1_BASE)
#define DMA1_Channel1       ((DMA_Channel_TypeDef *) DMA1_Channel1_BASE)
#define DMA1_Channel2       ((DMA_Channel_TypeDef *) DMA1_Channel2_BASE)
#define DMA1_Channel3       ((DMA_Channel_TypeDef *) DMA1_Channel3_BASE)
#define DMA1_Channel4       ((DMA_Channel_TypeDef *) DMA1_Channel4_BASE)
#define DMA1_Channel5       ((DMA_Channel_TypeDef *) DMA1_Channel5_BASE)
#define DMA1_Channel6       ((DMA_Channel_TypeDef *) DMA1_Channel6_BASE)
#define DMA1_Channel7       ((DMA_Channel_TypeDef *) DMA1_Channel7_BASE)
#define DMA1_Channel8       ((DMA_Channel_TypeDef *) DMA1_Channel8_BASE)

#define DMA2_BASE             (DMA1_BASE)
#define DMA2_Channel1_BASE    (0x4)
#define DMA2_Channel2_BASE    (0x5)
#define DMA2_Channel3_BASE    (0x6)
#define DMA2_Channel4_BASE    (0x7)
#define DMA2_Channel5_BASE    (0x8)
#define DMA2                  ((DMA_TypeDef *) DMA2_BASE)

#define CONFBUS_BASE    0x8000000015103070
#define CBUS_FEATURE    0x0008

#define CBUS_CHIP_CTRL0 0x0100
#define CBUS_CHIP_CTRL1 0x0108
#define CBUS_CHIP_CTRL2 0x0110
#define CBUS_CHIP_CTRL3 0x0118
#define CBUS_CHIP_CTRL4 0x0120
#define CBUS_CHIP_CTRL5 0x0128
#define CBUS_CHIP_CTRL6 0x0130
#define CBUS_CHIP_CTRL7 0x0138
#define CBUS_CHIP_SAMP0 0x0140
#define CBUS_CHIP_SAMP1 0x0148

#define CBUS_PLL_NODE    (CONFBUS_BASE + 0x0400)
#define CBUS_PLL_DDR     (CONFBUS_BASE + 0x0408)
#define CBUS_PLL_PIX     (CONFBUS_BASE + 0x0410)

#define CBUS_SB_GPIOCFG0 (CONFBUS_BASE + 0x0490)
#define CBUS_SB_GPIOCFG1 (CONFBUS_BASE + 0x0498)
#define CBUS_SB_GPIOCFG2 (CONFBUS_BASE + 0x04a0)
#define CBUS_SB_GPIOCFG3 (CONFBUS_BASE + 0x04a8)

#define CBUS_USB_PHY     (CONFBUS_BASE + 0x0500)

#define CBUS_THSENS_INT  (CONFBUS_BASE + 0x1500)

#define CBUS_CHIP_ID0    (CONFBUS_BASE + 0x3ff0)
#define CBUS_CHIP_ID1    (CONFBUS_BASE + 0x3ff8)






#endif /*_LS2P300_H*/
