#ifndef _LS2H_H
#define _LS2H_H
#include "cpu.h"
#define NODE0_CORE0_BUF0	PHYS_TO_UNCACHED(0x1fe01000)
#define NODE0_CORE1_BUF0	PHYS_TO_UNCACHED(0x1fe01100)
#define RESERVED_COREMASK	0xfff0
#define BOOTCORE_ID		0
#define SHUTDOWN_MASK	0
#define FN_OFF			0x020
#define SP_OFF			0x028
#define GP_OFF			0x030
#define A1_OFF			0x038

/* CHIP CONFIG regs */
#define LS2K1000_GENERAL_CFG0				PHYS_TO_UNCACHED(0x1fe00420)
#define LS2K1000_GENERAL_CFG1				PHYS_TO_UNCACHED(0x1fe00428)
#define LS2K1000_GENERAL_CFG2				PHYS_TO_UNCACHED(0x1fe00430)
#define LS2K1000_APB_DMA_CFG				PHYS_TO_UNCACHED(0x1fe00438)

#define LS2K1000_PIXCLK0_CTRL0_REG			PHYS_TO_UNCACHED(0x1fe004b0)
#define LS2K1000_PIXCLK0_CTRL1_REG			PHYS_TO_UNCACHED(0x1fe004b8)
#define LS2K1000_PIXCLK1_CTRL0_REG			PHYS_TO_UNCACHED(0x1fe004c0)
#define LS2K1000_PIXCLK1_CTRL1_REG			PHYS_TO_UNCACHED(0x1fe004c8)

#define PIXCLK_CTRL0_PSTDIV_SET				(1 << 31)
#define PIXCLK_CTRL0_PSTDIV_PD				(1 << 30)
#define PIXCLK_CTRL0_PSTDIV_DF				(0X3f << 24)
#define PIXCLK_CTRL0_PLL_PD				(1 << 7)
#define PIXCLK_CTRL0_PLL_LDF				(0xff << 16)
#define PIXCLK_CTRL0_PLL_ODF				(0x3 << 5)
#define PIXCLK_CTRL0_PLL_IDF				(0X7 << 2)
#define PIXCLK_CTRL0_REF_SEL				0X3

#define LS2K1000_XBAR_WIN0_BASE				PHYS_TO_UNCACHED(0x1fe02000)
#define LS2K1000_XBAR_WIN0_MASK				PHYS_TO_UNCACHED(0x1fe02040)
#define LS2K1000_XBAR_WIN0_MMAP				PHYS_TO_UNCACHED(0x1fe02080)
#define LS2K1000_XBAR_WIN1_BASE				PHYS_TO_UNCACHED(0x1fe02008)
#define LS2K1000_XBAR_WIN1_MASK				PHYS_TO_UNCACHED(0x1fe02048)
#define LS2K1000_XBAR_WIN1_MMAP				PHYS_TO_UNCACHED(0x1fe02088)
#define LS2K1000_XBAR_WIN2_BASE				PHYS_TO_UNCACHED(0x1fe02010)
#define LS2K1000_XBAR_WIN2_MASK				PHYS_TO_UNCACHED(0x1fe02050)
#define LS2K1000_XBAR_WIN2_MMAP				PHYS_TO_UNCACHED(0x1fe02090)
#define LS2K1000_XBAR_WIN3_BASE				PHYS_TO_UNCACHED(0x1fe02018)
#define LS2K1000_XBAR_WIN3_MASK				PHYS_TO_UNCACHED(0x1fe02058)
#define LS2K1000_XBAR_WIN3_MMAP				PHYS_TO_UNCACHED(0x1fe02098)
#define LS2K1000_XBAR_WIN4_BASE				PHYS_TO_UNCACHED(0x1fe02020)
#define LS2K1000_XBAR_WIN4_MASK				PHYS_TO_UNCACHED(0x1fe02060)
#define LS2K1000_XBAR_WIN4_MMAP				PHYS_TO_UNCACHED(0x1fe020a0)
#define LS2K1000_XBAR_WIN5_BASE				PHYS_TO_UNCACHED(0x1fe02028)
#define LS2K1000_XBAR_WIN5_MASK				PHYS_TO_UNCACHED(0x1fe02068)
#define LS2K1000_XBAR_WIN5_MMAP				PHYS_TO_UNCACHED(0x1fe020a8)
#define LS2K1000_XBAR_WIN6_BASE				PHYS_TO_UNCACHED(0x1fe02030)
#define LS2K1000_XBAR_WIN6_MASK				PHYS_TO_UNCACHED(0x1fe02070)
#define LS2K1000_XBAR_WIN6_MMAP				PHYS_TO_UNCACHED(0x1fe020b0)
#define LS2K1000_XBAR_WIN7_BASE				PHYS_TO_UNCACHED(0x1fe02038)
#define LS2K1000_XBAR_WIN7_MASK				PHYS_TO_UNCACHED(0x1fe02078)
#define LS2K1000_XBAR_WIN7_MMAP				PHYS_TO_UNCACHED(0x1fe020b8)

#define writel_reg_bit( addr, clear_bit, bit_val)	(*(volatile unsigned int*)(addr)) = (*(volatile unsigned int*)(addr)) & (~clear_bit) | bit_val

/* OTG regs */

/* USB regs */
#define	LS2K1000_EHCI_REG_BASE			(*(volatile unsigned int *)(BONITO_PCICFG0_BASE_VA | (0 << 16) | (2 << 11) | (1 << 8) | 0x10))
#define LS2K1000_EHCI_BASE				PHYS_TO_UNCACHED(LS2K1000_EHCI_REG_BASE & (~0xf))

/* GMAC regs */

/* HDA regs */

/* SATAregs */
#define	LS2K1000_SATA_REG_BASE			(*(volatile unsigned int *)(BONITO_PCICFG0_BASE_VA | (0 << 16) | (8 << 11) | (0 << 8) | 0x10))
#define LS2K1000_SATA_BASE				PHYS_TO_UNCACHED(LS2K1000_SATA_REG_BASE & (~0xf))

/* GPU regs */

/* DC regs */
//#define	LS2K1000_DC_BASE					PHYS_TO_UNCACHED(0x1f010000)
#define	LS2K1000_DC_REG_BASE					(*(volatile unsigned int *)(BONITO_PCICFG0_BASE_VA | (0 << 16) | (6 << 11) | (0 << 8) | 0x10))
#define	LS2K1000_DC_BASE						PHYS_TO_UNCACHED(LS2K1000_DC_REG_BASE & (~0xf))
#define LS2K1000_FB_CFG_DVO_REG				(0x1240)
#define LS2K1000_FB_CFG_VGA_REG				(0x1250)
#define LS2K1000_FB_ADDR0_DVO_REG			(0x1260)
#define LS2K1000_FB_ADDR0_VGA_REG			(0x1270)
#define LS2K1000_FB_STRI_DVO_REG				(0x1280)
#define LS2K1000_FB_STRI_VGA_REG				(0x1290)
#define LS2K1000_FB_ADDR1_DVO_REG			(0x1580)
#define LS2K1000_FB_ADDR1_VGA_REG			(0x1590)

#define LS2K1000_FB_CUR_CFG_REG				(0x1520)
#define LS2K1000_FB_CUR_ADDR_REG				(0x1530)
#define LS2K1000_FB_CUR_LOC_ADDR_REG			(0x1540)
#define LS2K1000_FB_CUR_BACK_REG				(0x1550)
#define LS2K1000_FB_CUR_FORE_REG				(0x1560)

#define LS2K1000_FB_DAC_CTRL_REG				(0x1600)

/* SPI regs */
#define LS2K1000_SPI0_BASE				PHYS_TO_UNCACHED(0x1fff0220)
#define LS2K1000_SPI1_BASE				PHYS_TO_UNCACHED(0x1fd40000)
#define LS2K1000_SPI2_BASE				PHYS_TO_UNCACHED(0x1ff50000)
#define LS2K1000_SPI3_BASE				PHYS_TO_UNCACHED(0x1ff51000)
#define LS2K1000_SPI4_BASE				PHYS_TO_UNCACHED(0x1ff52000)
#define LS2K1000_SPI5_BASE				PHYS_TO_UNCACHED(0x1ff53000)

/* UART regs */
#define LS2K1000_UART_REG_BASE				PHYS_TO_UNCACHED(0x1fe20000)

/* I2C regs */
//APB configured addr 0x1fe2,i2c0 addr is 0x1fe21000
#define LS2K1000_I2C0_REG_BASE				PHYS_TO_UNCACHED(0x1fe21000)
#define LS2K1000_I2C0_PRER_LO_REG			(LS2K1000_I2C0_REG_BASE + 0x0)
#define LS2K1000_I2C0_PRER_HI_REG			(LS2K1000_I2C0_REG_BASE + 0x1)
#define LS2K1000_I2C0_CTR_REG				(LS2K1000_I2C0_REG_BASE + 0x2)
#define LS2K1000_I2C0_TXR_REG				(LS2K1000_I2C0_REG_BASE + 0x3)
#define LS2K1000_I2C0_RXR_REG				(LS2K1000_I2C0_REG_BASE + 0x3)
#define LS2K1000_I2C0_CR_REG				(LS2K1000_I2C0_REG_BASE + 0x4)
#define LS2K1000_I2C0_SR_REG				(LS2K1000_I2C0_REG_BASE + 0x4)

#define LS2K1000_I2C1_REG_BASE				PHYS_TO_UNCACHED(0x1fe21800)
#define LS2K1000_I2C1_PRER_LO_REG			(LS2K1000_I2C1_REG_BASE + 0x0)
#define LS2K1000_I2C1_PRER_HI_REG			(LS2K1000_I2C1_REG_BASE + 0x1)
#define LS2K1000_I2C1_CTR_REG				(LS2K1000_I2C1_REG_BASE + 0x2)
#define LS2K1000_I2C1_TXR_REG				(LS2K1000_I2C1_REG_BASE + 0x3)
#define LS2K1000_I2C1_RXR_REG				(LS2K1000_I2C1_REG_BASE + 0x3)
#define LS2K1000_I2C1_CR_REG				(LS2K1000_I2C1_REG_BASE + 0x4)
#define LS2K1000_I2C1_SR_REG				(LS2K1000_I2C1_REG_BASE + 0x4)

#define LS2K1000_I2C4_REG_BASE				PHYS_TO_UNCACHED(0x1ff4a000)	//PIX0

#define CR_START					0x80
#define CR_STOP						0x40
#define CR_READ						0x20
#define CR_WRITE					0x10
#define CR_ACK						0x8
#define CR_IACK						0x1

#define SR_NOACK					0x80
#define SR_BUSY						0x40
#define SR_AL						0x20
#define SR_TIP						0x2
#define	SR_IF						0x1

/* PWM regs */
#define LS2K1000_PWM0_REG_BASE				PHYS_TO_UNCACHED(0x1fe22000)
#define LS2K1000_PWM1_REG_BASE				PHYS_TO_UNCACHED(0x1fe22010)

/* SDIO regs */
#define LS2K1000_SDIO0_BASE 				PHYS_TO_UNCACHED(0x1fe27000)
#define LS2K1000_SDIO1_BASE 				PHYS_TO_UNCACHED(0x1ff66000)

/* HPET regs */
#define LS2K1000_HPET0_BASE 				PHYS_TO_UNCACHED(0x1fe24000)
#define LS2K1000_HPET0_PERIOD				LS2K1000_HPET0_BASE + 0x4
#define LS2K1000_HPET0_CONF				LS2K1000_HPET0_BASE + 0x10
#define LS2K1000_HPET0_MAIN				LS2K1000_HPET0_BASE + 0xF0

#define LS2K1000_HPET1_BASE 				PHYS_TO_UNCACHED(0x1ff69000)
#define LS2K1000_HPET2_BASE 				PHYS_TO_UNCACHED(0x1ff6a000)
#define LS2K1000_HPET3_BASE 				PHYS_TO_UNCACHED(0x1ff6b000)

/* AC97 regs */
#define LS2K1000_AC97_REG_BASE				PHYS_TO_UNCACHED(0x1ff54000)

/* NAND regs */
#define LS2K1000_NAND_REG_BASE				PHYS_TO_UNCACHED(0x1fe26000)
#define LS2K1000_NAND_CMD_REG				(LS2K1000_NAND_REG_BASE + 0x0000)
#define LS2K1000_NAND_ADDR_C_REG				(LS2K1000_NAND_REG_BASE + 0x0004)
#define LS2K1000_NAND_ADDR_R_REG				(LS2K1000_NAND_REG_BASE + 0x0008)
#define LS2K1000_NAND_TIMING_REG				(LS2K1000_NAND_REG_BASE + 0x000c)
#define LS2K1000_NAND_IDL_REG				(LS2K1000_NAND_REG_BASE + 0x0010)
#define LS2K1000_NAND_STA_IDH_REG			(LS2K1000_NAND_REG_BASE + 0x0014)
#define LS2K1000_NAND_PARAM_REG				(LS2K1000_NAND_REG_BASE + 0x0018)
#define LS2K1000_NAND_OP_NUM_REG				(LS2K1000_NAND_REG_BASE + 0x001c)
#define LS2K1000_NAND_CSRDY_MAP_REG			(LS2K1000_NAND_REG_BASE + 0x0020)
#define LS2K1000_NAND_DMA_ACC_REG			(LS2K1000_NAND_REG_BASE + 0x0040)

/* ACPI regs */
#define LS2K1000_ACPI_REG_BASE			PHYS_TO_UNCACHED(0x1fe27000)
#define LS2K1000_PM_SOC_REG				(LS2K1000_ACPI_REG_BASE + 0x0000)
#define LS2K1000_PM_RESUME_REG			(LS2K1000_ACPI_REG_BASE + 0x0004)
#define LS2K1000_PM_RTC_REG				(LS2K1000_ACPI_REG_BASE + 0x0008)
#define LS2K1000_PM1_STS_REG				(LS2K1000_ACPI_REG_BASE + 0x000c)
#define LS2K1000_PM1_EN_REG				(LS2K1000_ACPI_REG_BASE + 0x0010)
#define LS2K1000_PM1_CNT_REG				(LS2K1000_ACPI_REG_BASE + 0x0014)
#define LS2K1000_PM1_TMR_REG				(LS2K1000_ACPI_REG_BASE + 0x0018)
#define LS2K1000_P_CNT_REG				(LS2K1000_ACPI_REG_BASE + 0x001c)
#define LS2K1000_P_LVL2_REG				(LS2K1000_ACPI_REG_BASE + 0x0020)
#define LS2K1000_P_LVL3_REG				(LS2K1000_ACPI_REG_BASE + 0x0024)
#define LS2K1000_GPE0_STS_REG			(LS2K1000_ACPI_REG_BASE + 0x0028)
#define LS2K1000_GPE0_EN_REG				(LS2K1000_ACPI_REG_BASE + 0x002c)
#define LS2K1000_RST_CNT_REG				(LS2K1000_ACPI_REG_BASE + 0x0030)
#define LS2K1000_WD_SET_REG				(LS2K1000_ACPI_REG_BASE + 0x0034)
#define LS2K1000_WD_TIMER_REG			(LS2K1000_ACPI_REG_BASE + 0x0038)
#define LS2K1000_DVFS_CNT_REG			(LS2K1000_ACPI_REG_BASE + 0x003c)
#define LS2K1000_DVFS_STS_REG			(LS2K1000_ACPI_REG_BASE + 0x0040)
#define LS2K1000_MS_CNT_REG				(LS2K1000_ACPI_REG_BASE + 0x0044)
#define LS2K1000_MS_THT_REG				(LS2K1000_ACPI_REG_BASE + 0x0048)
#define	LS2K1000_THSENS_CNT_REG			(LS2K1000_ACPI_REG_BASE + 0x004c)
#define LS2K1000_GEN_RTC1_REG			(LS2K1000_ACPI_REG_BASE + 0x0050)
#define LS2K1000_GEN_RTC2_REG			(LS2K1000_ACPI_REG_BASE + 0x0054)

/* DMA regs */
#define LS2K1000_DMA_ORDER0				PHYS_TO_UNCACHED(0x1fe00c00)
#define LS2K1000_DMA_ORDER1				PHYS_TO_UNCACHED(0x1fe00c10)
#define LS2K1000_DMA_ORDER2				PHYS_TO_UNCACHED(0x1fe00c20)
#define LS2K1000_DMA_ORDER3				PHYS_TO_UNCACHED(0x1fe00c30)
#define LS2K1000_DMA_ORDER4				PHYS_TO_UNCACHED(0x1fe00c40)


/* RTC regs */
#define LS2K1000_RTC_REG_BASE				PHYS_TO_UNCACHED(0x1fe27800)
#define	LS2K1000_TOY_TRIM_REG				(LS2K1000_RTC_REG_BASE + 0x0020)
#define	LS2K1000_TOY_WRITE0_REG				(LS2K1000_RTC_REG_BASE + 0x0024)
#define	LS2K1000_TOY_WRITE1_REG				(LS2K1000_RTC_REG_BASE + 0x0028)
#define	LS2K1000_TOY_READ0_REG				(LS2K1000_RTC_REG_BASE + 0x002c)
#define	LS2K1000_TOY_READ1_REG				(LS2K1000_RTC_REG_BASE + 0x0030)
#define	LS2K1000_TOY_MATCH0_REG				(LS2K1000_RTC_REG_BASE + 0x0034)
#define	LS2K1000_TOY_MATCH1_REG				(LS2K1000_RTC_REG_BASE + 0x0038)
#define	LS2K1000_TOY_MATCH2_REG				(LS2K1000_RTC_REG_BASE + 0x003c)
#define	LS2K1000_RTC_CTRL_REG				(LS2K1000_RTC_REG_BASE + 0x0040)
#define	LS2K1000_RTC_TRIM_REG				(LS2K1000_RTC_REG_BASE + 0x0060)
#define	LS2K1000_RTC_WRITE0_REG				(LS2K1000_RTC_REG_BASE + 0x0064)
#define	LS2K1000_RTC_READ0_REG				(LS2K1000_RTC_REG_BASE + 0x0068)
#define	LS2K1000_RTC_MATCH0_REG				(LS2K1000_RTC_REG_BASE + 0x006c)
#define	LS2K1000_RTC_MATCH1_REG				(LS2K1000_RTC_REG_BASE + 0x0070)
#define	LS2K1000_RTC_MATCH2_REG				(LS2K1000_RTC_REG_BASE + 0x0074)

/* LPC regs */
#define LS2K1000_LPC_MEM_BASE				PHYS_TO_UNCACHED(0x1c000000)
#define LS2K1000_LPC_IO_BASE					PHYS_TO_UNCACHED(0x1f0d0000)
#define LS2K1000_LPC_REG_BASE				PHYS_TO_UNCACHED(0x1f0e0000)
#define LS2K1000_LPC_CFG0_REG				(LS2K1000_LPC_REG_BASE + 0x0)
#define LS2K1000_LPC_CFG1_REG				(LS2K1000_LPC_REG_BASE + 0x4)
#define LS2K1000_LPC_CFG2_REG				(LS2K1000_LPC_REG_BASE + 0x8)
#define LS2K1000_LPC_CFG3_REG				(LS2K1000_LPC_REG_BASE + 0xc)

/* For PS2 */
#define LS2K1000_PS2_DLL					PHYS_TO_UNCACHED(0x1ff4c008)
#define LS2K1000_PS2_DLH					PHYS_TO_UNCACHED(0x1ff4c009)


/* S3 Need */
#define STR_STORE_BASE					PHYS_TO_UNCACHED(0xfaaa000)
#define SLEEP_TYPE_S3           			0x5
#define STR_XBAR_CONFIG_NODE_a0(OFFSET, BASE, MASK, MMAP) \
        addi.d   v0, t0, OFFSET;     \
        li.d     t1, BASE;           \
        or      t1, t1, a0;         \
        st.d      t1, v0, 0x00;       \
        li.d     t1, MASK;           \
        st.d      t1, v0, 0x40;       \
        li.d     t1, MMAP;           \
        st.d      t1, v0, 0x80;

#endif
