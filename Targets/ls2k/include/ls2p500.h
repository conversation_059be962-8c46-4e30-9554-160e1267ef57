#ifndef _LS2H_H
#define _LS2H_H
#include "cpu.h"

/* CHIP CONFIG regs */
#define LS2P500_GENERAL_CFG0				PHYS_TO_UNCACHED(0x14000100)
#define LS2P500_GENERAL_CFG1				PHYS_TO_UNCACHED(0x14000104)
#define LS2P500_GENERAL_CFG2				PHYS_TO_UNCACHED(0x14000108)
#define LS2P500_GENERAL_CFG3				PHYS_TO_UNCACHED(0x1400010c)
#define LS2P500_GENERAL_CFG4				PHYS_TO_UNCACHED(0x14000110)
#define LS2P500_GENERAL_CFG5				PHYS_TO_UNCACHED(0x14000114)
#define LS2P500_SAMPLE_CFG0				PHYS_TO_UNCACHED(0x14000120)
#define LS2P500_SAMPLE_CFG1				PHYS_TO_UNCACHED(0x14000124)
#define LS2P500_CHIP_HPT_LO				PHYS_TO_UNCACHED(0x14000130)
#define LS2P500_CHIP_HPT_HI				PHYS_TO_UNCACHED(0x14000134)

//GPIO regs
#define LS2P500_GPIO_MULTI_CFG				PHYS_TO_UNCACHED(0x14000490)
#define LS2P500_GPIO_BASE				PHYS_TO_UNCACHED(0x14202000)

#define LS2P500_GPIO_BASE_BIT				PHYS_TO_UNCACHED(LS2P500_GPIO_BASE)

#define LS2P500_GPIO_BIT_OEN				PHYS_TO_UNCACHED(LS2P500_GPIO_BASE_BIT + 0x00)
#define LS2P500_GPIO_BIT_O				PHYS_TO_UNCACHED(LS2P500_GPIO_BASE_BIT + 0x10)
#define LS2P500_GPIO_BIT_I				PHYS_TO_UNCACHED(LS2P500_GPIO_BASE_BIT + 0x20)

#define LS2P500_GPIO_BIT_INT_EN				PHYS_TO_UNCACHED(LS2P500_GPIO_BASE_BIT + 0x30)
#define LS2P500_GPIO_BIT_INT_POL			PHYS_TO_UNCACHED(LS2P500_GPIO_BASE_BIT + 0x40)
#define LS2P500_GPIO_BIT_INT_EDGE			PHYS_TO_UNCACHED(LS2P500_GPIO_BASE_BIT + 0x50)
#define LS2P500_GPIO_BIT_INT_CLEAR			PHYS_TO_UNCACHED(LS2P500_GPIO_BASE_BIT + 0x60)
#define LS2P500_GPIO_BIT_INT_STS			PHYS_TO_UNCACHED(LS2P500_GPIO_BASE_BIT + 0x70)

#define LS2P500_GPIO_BASE_BYTE				PHYS_TO_UNCACHED(LS2P500_GPIO_BASE + 0x800)

#define LS2P500_GPIO_BYTE_OEN				PHYS_TO_UNCACHED(LS2P500_GPIO_BASE_BYTE + 0x000)
#define LS2P500_GPIO_BYTE_O				PHYS_TO_UNCACHED(LS2P500_GPIO_BASE_BYTE + 0x100)
#define LS2P500_GPIO_BYTE_I				PHYS_TO_UNCACHED(LS2P500_GPIO_BASE_BYTE + 0x200)

#define LS2P500_GPIO_BYTE_INT_EN			PHYS_TO_UNCACHED(LS2P500_GPIO_BASE_BYTE + 0x300)
#define LS2P500_GPIO_BYTE_INT_POL			PHYS_TO_UNCACHED(LS2P500_GPIO_BASE_BYTE + 0x400)
#define LS2P500_GPIO_BYTE_INT_EDGE			PHYS_TO_UNCACHED(LS2P500_GPIO_BASE_BYTE + 0x500)
#define LS2P500_GPIO_BYTE_INT_CLEAR			PHYS_TO_UNCACHED(LS2P500_GPIO_BASE_BYTE + 0x600)
#define LS2P500_GPIO_BYTE_INT_STS			PHYS_TO_UNCACHED(LS2P500_GPIO_BASE_BYTE + 0x700)

//USB PHY CFG
#define LS2P500_USB_PHY_CFG0				PHYS_TO_UNCACHED(0x14000500)
#define LS2P500_USB_PHY_CFG1				PHYS_TO_UNCACHED(0x14000504)

/* OTG regs */

/* USB regs */
#define LS2P500_EHCI_BASE				PHYS_TO_UNCACHED(0x1f050000)

/* GMAC regs */

/* SPI regs */
#define LS2P500_SPI0_BASE				PHYS_TO_UNCACHED(0x14010000)
#define LS2P500_SPI1_BASE				PHYS_TO_UNCACHED(0x14203000)
//#define LS2P500_SPI2_BASE				PHYS_TO_UNCACHED(0x1ff50000)
//#define LS2P500_SPI3_BASE				PHYS_TO_UNCACHED(0x1ff51000)
//#define LS2P500_SPI4_BASE				PHYS_TO_UNCACHED(0x1ff52000)
//#define LS2P500_SPI5_BASE				PHYS_TO_UNCACHED(0x1ff53000)

/* UART regs */
#define LS2P500_UART0_REG_BASE				PHYS_TO_UNCACHED(0x14200000)
#define LS2P500_UART1_REG_BASE				PHYS_TO_UNCACHED(0x14200400)
#define LS2P500_UART2_REG_BASE				PHYS_TO_UNCACHED(0x14200800)
#define LS2P500_UART3_REG_BASE				PHYS_TO_UNCACHED(0x14200c00)
#define LS2P500_UART4_REG_BASE				PHYS_TO_UNCACHED(0x15100000)
#define LS2P500_UART5_REG_BASE				PHYS_TO_UNCACHED(0x15100800)
#define LS2P500_UART6_REG_BASE				PHYS_TO_UNCACHED(0x15200000)
#define LS2P500_UART7_REG_BASE				PHYS_TO_UNCACHED(0x15100800)

/* I2C regs */
//APB configured addr 0x1fe0,i2c0 addr is 0x1fe01000
#define LS2P500_I2C0_REG_BASE				PHYS_TO_UNCACHED(0x14201000)
#define LS2P500_I2C0_PRER_LO_REG			(LS2P500_I2C0_REG_BASE + 0x0)
#define LS2P500_I2C0_PRER_HI_REG			(LS2P500_I2C0_REG_BASE + 0x1)
#define LS2P500_I2C0_CTR_REG				(LS2P500_I2C0_REG_BASE + 0x2)
#define LS2P500_I2C0_TXR_REG				(LS2P500_I2C0_REG_BASE + 0x3)
#define LS2P500_I2C0_RXR_REG				(LS2P500_I2C0_REG_BASE + 0x3)
#define LS2P500_I2C0_CR_REG				(LS2P500_I2C0_REG_BASE + 0x4)
#define LS2P500_I2C0_SR_REG				(LS2P500_I2C0_REG_BASE + 0x4)
#define GPIO_MULUSE_BASE                                PHYS_TO_UNCACHED(0x1fe104b0) //TODO,for compile,not use now

#define LS2P500_I2C1_REG_BASE				PHYS_TO_UNCACHED(0x14202800)
#define LS2P500_I2C1_PRER_LO_REG			(LS2P500_I2C1_REG_BASE + 0x0)
#define LS2P500_I2C1_PRER_HI_REG			(LS2P500_I2C1_REG_BASE + 0x1)
#define LS2P500_I2C1_CTR_REG				(LS2P500_I2C1_REG_BASE + 0x2)
#define LS2P500_I2C1_TXR_REG				(LS2P500_I2C1_REG_BASE + 0x3)
#define LS2P500_I2C1_RXR_REG				(LS2P500_I2C1_REG_BASE + 0x3)
#define LS2P500_I2C1_CR_REG				(LS2P500_I2C1_REG_BASE + 0x4)
#define LS2P500_I2C1_SR_REG				(LS2P500_I2C1_REG_BASE + 0x4)

#define LS2P500_I2C2_REG_BASE				PHYS_TO_UNCACHED(0x15101000)
#define LS2P500_I2C3_REG_BASE				PHYS_TO_UNCACHED(0x15101800)

#define CR_START					0x80
#define CR_STOP						0x40
#define CR_READ						0x20
#define CR_WRITE					0x10
#define CR_ACK						0x8
#define CR_IACK						0x1

#define SR_NOACK					0x80
#define SR_BUSY						0x40
#define SR_AL						0x20
#define SR_TIP						0x2
#define	SR_IF						0x1

/* PWM regs */
#define LS2P500_PWM0_REG_BASE				PHYS_TO_UNCACHED(0x14207000)
#define LS2P500_PWM1_REG_BASE				PHYS_TO_UNCACHED(0x14207010)

/* SDIO regs */
#define LS2P500_SDIO0_BASE 				PHYS_TO_UNCACHED(0x14210000)
#define LS2P500_SDIO1_BASE 				PHYS_TO_UNCACHED(0x14218000)

/* HPET regs */
#define LS2P500_HPET0_BASE 				PHYS_TO_UNCACHED(0x14204000)
#define LS2P500_HPET0_PERIOD				LS2P500_HPET0_BASE + 0x4
#define LS2P500_HPET0_CONF				LS2P500_HPET0_BASE + 0x10 
#define LS2P500_HPET0_MAIN				LS2P500_HPET0_BASE + 0xF0 

#define LS2P500_HPET1_BASE 				PHYS_TO_UNCACHED(0x14204800)
#define LS2P500_HPET2_BASE 				PHYS_TO_UNCACHED(0x1510b000)
#define LS2P500_HPET3_BASE 				PHYS_TO_UNCACHED(0x15205000)

/* RTC regs */
#define LS2P500_RTC_REG_BASE				PHYS_TO_UNCACHED(0x1510a000)
#define	LS2P500_TOY_TRIM_REG				(LS2P500_RTC_REG_BASE + 0x0020)
#define	LS2P500_TOY_WRITE0_REG				(LS2P500_RTC_REG_BASE + 0x0024)
#define	LS2P500_TOY_WRITE1_REG				(LS2P500_RTC_REG_BASE + 0x0028)
#define	LS2P500_TOY_READ0_REG				(LS2P500_RTC_REG_BASE + 0x002c)
#define	LS2P500_TOY_READ1_REG				(LS2P500_RTC_REG_BASE + 0x0030)
#define	LS2P500_TOY_MATCH0_REG				(LS2P500_RTC_REG_BASE + 0x0034)
#define	LS2P500_TOY_MATCH1_REG				(LS2P500_RTC_REG_BASE + 0x0038)
#define	LS2P500_TOY_MATCH2_REG				(LS2P500_RTC_REG_BASE + 0x003c)
#define	LS2P500_RTC_CTRL_REG				(LS2P500_RTC_REG_BASE + 0x0040)
#define	LS2P500_RTC_TRIM_REG				(LS2P500_RTC_REG_BASE + 0x0060)
#define	LS2P500_RTC_WRITE0_REG				(LS2P500_RTC_REG_BASE + 0x0064)
#define	LS2P500_RTC_READ0_REG				(LS2P500_RTC_REG_BASE + 0x0068)
#define	LS2P500_RTC_MATCH0_REG				(LS2P500_RTC_REG_BASE + 0x006c)
#define	LS2P500_RTC_MATCH1_REG				(LS2P500_RTC_REG_BASE + 0x0070)
#define	LS2P500_RTC_MATCH2_REG				(LS2P500_RTC_REG_BASE + 0x0074)

#define LS2P500_RST_CNT_REG				PHYS_TO_UNCACHED(0x14205000)
#define STR_STORE_BASE					PHYS_TO_UNCACHED(0x15000000+0x20000-24)
#define LS2P500_PM1_CNT_REG				PHYS_TO_UNCACHED(0x15000000+0x20000-8)
/* S3 Need */
/*
#define STR_XBAR_CONFIG_NODE_a0(OFFSET, BASE, MASK, MMAP) \
        daddi   v0, t0, OFFSET;     \
        dli     t1, BASE;           \
        or      t1, t1, a0;         \
        sd      t1, 0x00(v0);       \
        dli     t1, MASK;           \
        sd      t1, 0x40(v0);       \
        dli     t1, MMAP;           \
        sd      t1, 0x80(v0);
*/

#endif /*_LS2H_H*/
