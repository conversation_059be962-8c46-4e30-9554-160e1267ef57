#include <asm.h>
#include <regnum.h>
#include <cpu.h>
#include <pte.h>

#include "target/bonito.h"
#include "target/ls2p300.h"
//LEAF(emmc_boot)
//init emmc host
	li.d 	a5, LS2P300_EMMC_BASE
	
	li.d 	t0, 0x401
	st.w	t0, a5, SDICON	//enable clk
	li.d 	t0, 0x80000080
	st.w	t0, a5, SDIPRE	//config pre_scale less than 400KHz
	li.d 	t0, 0x1ff
	st.w	t0, a5, SDIINTMSK	//sdiintmask
	li.d 	t0, 0x3ff
	st.w	t0, a5, SDIINTEN	//sdiinten

init_card:
	//cmd0
	li.w	t0, 0x0
	st.w	t0, a5, SDICMDARG
	li.w	t0, 0/*cmd*/|0x140|(0<<9/*rsp*/)|(0<<10/*l_rsp*/)|(0<<12/*multi*/)|(0<<13/*crc*/)
	st.w	t0, a5, SDICMDCO<PERSON>
	bl		cmd_check
loop_cmd1:
	//cmd1
	li.w	t0, 0xc0ff8080
	st.w	t0, a5, SDICMDARG
	li.w	t0, 1/*cmd*/|0x140|(1<<9/*rsp*/)|(0<<10/*l_rsp*/)|(0<<12/*multi*/)|(0<<13/*crc*/)
	st.w	t0, a5, SDICMDCON
	bl		cmd_check
	ld.w	t0, a5, SDIRSP0
	li.w	t1, 0x80000000
	and		t0, t0, t1
	bne 	t0, t1, loop_cmd1
	
	//cmd2 make the card enter identification state
	li.w	t0, 0x0
	st.w	t0, a5, SDICMDARG
	li.w	t0, 2/*cmd*/|0x140|(1<<9/*rsp*/)|(1<<10/*l_rsp*/)|(0<<12/*multi*/)|(0<<13/*crc*/)
	st.w	t0, a5, SDICMDCON
	bl		cmd_check

	//cmd3 set the card's RCA
	li.w	t0, 0x10000
	st.w	t0, a5, SDICMDARG
	li.w	t0,  3/*cmd*/|0x140|(1<<9/*rsp*/)|(0<<10/*l_rsp*/)|(0<<12/*multi*/)|(0<<13/*crc*/)
	st.w	t0, a5, SDICMDCON
	bl		cmd_check

	//wait
	li.d	a0,	0x10000
	bl		emmc_delay
	
	//cmd7 select the card
	li.w	t0, 0x10000
	st.w	t0, a5, SDICMDARG
	li.w	t0,  7/*cmd*/|0x140|(1<<9/*rsp*/)|(0<<10/*l_rsp*/)|(0<<12/*multi*/)|(0<<13/*crc*/)
	st.w	t0, a5, SDICMDCON
	bl		cmd_check

	//cmd6 emmc high bus mode
	li.w	t0, 0x03b90100
	st.w	t0, a5, SDICMDARG
	li.w	t0,  6/*cmd*/|0x140|(1<<9/*rsp*/)|(0<<10/*l_rsp*/)|(0<<12/*multi*/)|(1<<13/*crc*/)
	st.w	t0, a5, SDICMDCON
	bl		cmd_check
	
	//cmd6 emmc 4 wire mode
	li.w	t0, 0x03b70100
	st.w	t0, a5, SDICMDARG
	li.w	t0,  6/*cmd*/|0x140|(1<<9/*rsp*/)|(0<<10/*l_rsp*/)|(0<<12/*multi*/)|(1<<13/*crc*/)
	st.w	t0, a5, SDICMDCON
	bl		cmd_check
	
	//cmd6 emmc boot sel
	li.w	t0, 0x03b30900
	st.w	t0, a5, SDICMDARG
	li.w	t0,  6/*cmd*/|0x140|(1<<9/*rsp*/)|(0<<10/*l_rsp*/)|(0<<12/*multi*/)|(1<<13/*crc*/)
	st.w	t0, a5, SDICMDCON
	bl		cmd_check
	
	//wait
	li.d	a0,	0x10000
	bl		emmc_delay
	
	//cmd6 emmc boot sel
	li.w	t0, 0x03b10200
	st.w	t0, a5, SDICMDARG
	li.w	t0,  6/*cmd*/|0x140|(1<<9/*rsp*/)|(0<<10/*l_rsp*/)|(0<<12/*multi*/)|(1<<13/*crc*/)
	st.w	t0, a5, SDICMDCON
	bl		cmd_check

sdio_dma_read:
	li.d 	a1, DMA_DESC_ADDR

	/* dma descriptor order */
	li.d 	a2, 0x0
	st.w	a2, a1, 0x0

	/* dma descriptor saddr */
	li.d 	a2, LOCK_CACHE_BASE + 0x400 #0x400: 1K
	st.w	a2, a1, 0x4

	/* dma descriptor daddr */
	li.d 	a2, LS2P300_EMMC_BASE + 0x40
	st.w	a2, a1, 0x8

	/* dma descriptor length(dma_cnt) */
	li.d 	a2, DMA_READ_WORDS
	st.w	a2, a1, 0xc

	/* dma descriptor step length */
	li.d 	a2, 0x0
	st.w	a2, a1, 0x10

	/* dma descriptor step times */
	li.d 	a2, 0x1
	st.w	a2, a1, 0x14

	/* dma descriptor cmd */
	li.d 	a2, 0x1			#cmd:read nand, mask interrupt
	st.w	a2, a1, 0x18

enable_dma:
	li.d 	a2, PHYS_TO_UNCACHED(DMA_CONFREG_BASE)		#dma order reg
	li.d 	a1, DMA_DESC_ADDR | 0x8
	st.w	a1, a2, 0x0
	nop

sdio_read_multi_blk:
	li.d 	t0, 0x1ff
	st.w	t0, a5, SDIINTMSK

	li.d 	t0, 0x200   //512 bytes
	st.w	t0, a5, SDIBSIZE

	li.d 	t0, 0xffffff   //timer 
	st.w	t0, a5, SDIDTIMER

	li.d 	t0,	(0x1c000 + SDIO_BLK_CNT)	//set 4 wire mode, set block num.
	st.w	t0, a5, SDIDATCON

	li.d 	t0, START_ADDRS
	st.w	t0, a5, SDICMDARG
	li.d 	t0, (CMD_INDEX | CMST | (WAIT_RSP<<9) | (LONG_RSP<<10) | (CHECK_CRC<<13) | (1 << 12))
	st.w	t0, a5, SDICMDCON

	li.d	a0,	0x10000
	bl		emmc_delay

data_check:
1:
	ld.w	a1, a5, SDIINTMSK
	li.w	t0,	0x1
	and		a1, a1, t0
	bne		a1, t0,	1b
	nop

	li.d 	t0, LOCK_CACHE_BASE + 0x400
	jirl	zero, t0, 0

/*
 * a0: loop count
 */
emmc_delay:
	addi.d	    a0,	a0,	-1
	bnez		a0, emmc_delay
	jirl		zero, ra, 0
/*
 * a5: EMMC_BASE, a4: ra
 */
cmd_check:
	or		a4, ra, zero
	li.w	a0, 0x2000
	bl		emmc_delay
	li.w	t0, 0x40	//cmdfin
1:
	ld.w	a1, a5, SDIINTMSK
	and		a1, a1, t0
	bne		t0, a1, 1b
	
	//clear interrupt
	li.d 	t0, 0x1ff
	st.w	t0, a5, SDIINTMSK	//sdiintmask

	or		ra, a4, zero
	jirl	zero, ra, 0
//END(emmc_boot)
