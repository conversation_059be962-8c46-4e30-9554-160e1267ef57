/*
 * This file is for 2P0300 pin and gpio ctrl
 */
#include "target/pincfgs.h"
#include <pmon.h>
#include <string.h>
pin_cfgs_t default_pin_cfgs[48] = {
	//0:GPIO, 1:first reuse, 2:second reuse, 3:main
	{0, 3}, //3:uart0_rx		1:gmac_col		2:sdio1_d[4]	
	{1, 3}, //3:uart0_tx		1:gmac_crs		2:sdio1_d[5]	
	{2, 3}, //3:i2c0_scl		1:sca_uart_rx	2:sdio1_d[6]	
	{3, 3}, //3:i2c1_sda		1:sca_uart_tx	2:sdio1_d[7]	
	{4, 3}, //3:spi0_clk	    1:i2c_scl		2:prt_pmio[0]	
	{5, 3}, //3:spi0_miso	    1:i2c_sda		2:prt_pmio[1]	
	{6, 3}, //3:spi0_mosi	    1:prt_i2c_scl	2:prt_pmio[2]	
	{7, 3}, //3:spi0_cs[0]	    1:prt_i2c_sda	2:prt_pmio[3]	
	{8, 3}, //3:gmac_rx_ctl	    1:uart1_rx	    2:prt_pmio[4]	
	{9, 3}, //3:gmac_rx[0]	    1:uart1_tx	    2:prt_pmio[5]
	{10,3}, //3:gmac_rx[1]	    1:uart1_rts	    2:prt_pmio[6]
	{11,3}, //3:gmac_rx[2]	    1:uart1_cts	    2:prt_pmio[7]
	{12,3}, //3:gmac_rx[3]	    1:sys_pwm[0]	2:prt_pmio[8] 	
	{13,3}, //3:gmac_tx_ctl     1:sys_pwm[1]	2:prt_pmio[9]
	{14,3}, //3:gmac_tx[0]	    1:sys_pwm[2]	2:prt_pmio[10]
	{15,3}, //3:gmac_tx[1]	    1:sys_pwm[3]	2:prt_pmio[11]
	{16,3}, //3:gmac_tx[2]		1:sys_pwm[4]	2:prt_pmio[12]
	{17,3}, //3:gamc_tx[3]		1:sys_pwm[5]	2:prt_pmio[13]	
	{18,3}, //3:gmac_mdck		1:sys_pwm[6]	2:prt_pmio[14]	
	{19,3}, //3:gmac_mdio		1:sys_pwm[7]	2:prt_pmio[15]	
	{20,3}, //3:sdio0_clk		1:sys_pmio[0]	2:prt_pwm[0]	
	{21,3}, //3:sdio0_cmd		1:sys_pmio[1]	2:prt_pwm[1]	
	{22,3}, //3:sdio0_d[0]		1:sys_pmio[2]	2:prt_pwm[2]	
	{23,3}, //3:sdio0_d[1]		1:sys_pmio[3]	2:prt_pwm[3]	
	{24,3}, //3:sdio0_d[2]		1:sys_pmio[4]	2:prt_pwm[4]	
	{25,3}, //3:sdio0_d[3]		1:sys_pmio[5]	2:prt_pwm[5]	
	{26,3}, //3:sdio0_d[4]		1:sys_pmio[6]	2:prt_pwm[6]	
	{27,3}, //3:sdio0_d[5]		1:sys_pmio[7]	2:prt_pwm[7]	
	{28,3}, //3:sdio0_d[6]		1:sys_pmio[8]	2:uart0_rts	
	{29,3}, //3:sdio0_d[7]		1:sys_pmio[9]	2:uart0_cts	
	{30,3}, //3:sdio1_clk		1:sys_pmio[10]	2:gmac_col	
	{31,3}, //3:sdio1_cmd		1:sys_pmio[11]	2:gmac_crs	
	{32,3}, //3:sdio1_d[0]		1:sys_pmio[12]	2:prt_uart0_rx	
	{33,3}, //3:sdio1_d[1]		1:sys_pmio[13]	2:prt_uart0_tx	
	{34,3}, //3:sdio1_d[2]		1:sys_pmio[14]	2:prt_uart1_rx	
	{35,3}, //3:sdio1_d[3]		1:sys_pmio[15]	2:prt_uart1_tx	
	{36,3}, //3:sys_pmio[0]   	1:sys_pwm[0]	2:prt_pmio[24]  
	{37,3}, //3:sys_pmio[1]   	1:sys_pwm[1]	2:prt_pmio[25]	
	{38,3}, //3:sys_pmio[2]		1:sys_pwm[2]	2:prt_pmio[26]  
	{39,3}, //3:sys_pmio[3]		1:sys_pwm[3]	2:prt_pmio[27]  
	{40,3}, //3:sys_pmio[4]		1:sys_pwm[4]	2:prt_pmio[28]  
	{41,3}, //3:sys_pmio[5]		1:sys_pwm[5]	2:prt_pmio[29]  
	{42,3}, //3:sys_pmio[6]		1:sys_pwm[6]	2:prt_pmio[30]  
	{43,3}, //3:sys_pmio[7]		1:sys_pwm[7]	2:prt_pmio[31]  
	{44,3}, //3:sys_pmio[8]		1:-	            2:uart1_tx  
	{45,0}, //3:sys_pmio[9]		1:spi_cs[1]	    2:uart1_rx  
	{46,1}, //3:sys_pmio[10]	1:spi_cs[2]	    2:uart1_rts  
	{47,1}, //3:sys_pmio[11]	1:spi_cs[3]	    2:uart1_cts  
};

void ls2p300_gpio_out(int gpio_num, int val)
{
	set_pin_mode(gpio_num, 0);
	uint64_t *addr_dir = LS2P300_GPIO_BIT_OEN+(gpio_num/8);
	uint64_t *addr_out = LS2P300_GPIO_BIT_O+(gpio_num/8);
	readb(addr_dir) &= ~(1ULL << (gpio_num%8));
	readb(addr_out) = readb(addr_out) & ~(1ULL << (gpio_num%8)) | ((unsigned long long)!!val << (gpio_num%8));
}

int ls2p300_gpio_in(int gpio_num)
{
	set_pin_mode(gpio_num, 0);
	uint64_t *addr_dir = LS2P300_GPIO_BIT_OEN+(gpio_num/8);
	uint64_t *addr_in = LS2P300_GPIO_BIT_I+(gpio_num/8);
	readb(addr_dir) |= (1ULL << (gpio_num%8));
	return !!(readb(addr_in) & (1ULL << (gpio_num%8)));
}

int cmd_ls2p300_gpio_out(int ac, unsigned char *av[])
{
	if (ac != 3) {
		printf("gpio_out <gpio_num> <output_val>\n");
		return 0;
	}
	ls2p300_gpio_out(strtoul(av[1], NULL, 0), strtoul(av[2], NULL, 0));
	return 0;
}

int cmd_ls2p300_gpio_in(int ac, unsigned char *av[])
{
	if (ac != 2) {
		printf("gpio_in <gpio_num>\n");
		return 0;
	}
	printf("gpio read val: %d\n", ls2p300_gpio_in(strtoul(av[1], NULL, 0)));
	return 0;
}

static const Cmd Cmds[] =
{
	{"MyCmds"},
	{"gpio_out","",0,"set gpio out put <gpio num> <val>", cmd_ls2p300_gpio_out , 0, 3, CMD_REPEAT},
	{"gpio_in","",0,"set gpio in, and get val", cmd_ls2p300_gpio_in , 0, 2, CMD_REPEAT},
	{0,0}
};

static void init_cmd __P((void)) __attribute__ ((constructor));

static void
init_cmd()
{
	cmdlist_expand(Cmds,1);
}
