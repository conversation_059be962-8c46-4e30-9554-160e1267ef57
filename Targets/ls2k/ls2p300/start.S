/*
 * Copyright (c) 2021 Loongson Technology Corporation Limited (www.loongson.cn)
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *	This product includes software developed by Opsycon AB, Sweden.
 * 4. The name of the author may not be used to endorse or promote products
 *    derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 */

#ifndef _KERNEL
#define _KERNEL
#endif

#include <asm.h>
#include <regnum.h>
#include <cpu.h>
#include <pte.h>

#include "pmon/dev/ns16550.h"
#include "target/bonito.h"
#include "target/ls2p300.h"
#include "target/cacheops.h"

#define DEBUG_LOCORE
#ifdef DEBUG_LOCORE
#define TTYDBG(x) \
	.section .rodata;98: .asciz x; .text; la a0, 98b; bl stringserial
#define TTYDBG_COM1(x) \
	.section .rodata;98: .asciz x; .text; la a0, 98b; bl stringserial_COM1
#else
#define TTYDBG(x)
#define TTYDBG_COM1(x)
#endif
#define	PRINTSTR TTYDBG
#define PRINT_CSR(offset)	\
	PRINTSTR("\r\ncsr 0x");	\
	li.w	a0, offset;	\
	bl	hexserial;	\
	PRINTSTR(" ->0x");	\
	csrrd	a0, offset;	\
	bl	hexserial64;	\
	PRINTSTR("\r\n");


#define CONFIG_CACHE_64K_4WAY 1
##define USEPCI

#define msize		s2

/*
 * Register usage:
 * 
 * s0 link versus load offset, used to relocate absolute adresses.
 * s1 free
 * s2 memory size.
 * s3 st.dShape.
 * s4 Bonito base address.
 * s5 dbg.
 * s6 st.dCfg.
 * s7 rasave.
 * s8 L3 Cache size.
 */


	.globl	_start
	.globl	start
	.globl	__main
_start:
start:
	.globl	stack
stack = start + LOCK_CACHE_SIZE	 /* Place PMON stack in the end of 2M RAM */
	csrrd	t0, 0xc0
	li.w	t1, 0xf000
	and	t0, t0, t1
	li.w	t1, 0xa000 //LA264
	beq	t0, t1, 1f
	li.d	t0, 0xe0000000 //RT
	jirl	zero, t0, 0
1:
	//set CPUCFG 2 bit27
	li.d	t0, (0x1 << 27)
	csrxchg	zero, t0, 0xc1

	/* enable perf counter as cp0 counter */
	li.w	t0, (0x1 << 16)
	csrxchg t0, t0, 0x200

	li.d	t0, UNCACHED_MEMORY_ADDR | 0xf
	csrwr	t0, 0x180
	li.d	t0, CACHED_MEMORY_ADDR | 0x1f
	csrwr	t0, 0x181
//set counter
#if defined(CLK_REF_100M)
 	li.d  t0, 0x0001000105f5e100 //对应100M
#elif defined(CLK_REF_24M) 
 	li.d  t0, 0x00010001016e3600 //对应24M
#elif defined(CLK_REF_20M) 
 	li.d  t0, 0x0001000101312d00 //对应20M
#endif
	csrwr t0, 0xc2

	/* spi speedup */
#if 1
	li.d    t0, 0x14010000
//	li.w    t1, 0x37
	li.w    t1, 0x27
	st.b    t1, t0, 0x4
#endif
/*
 * should before execution jr shutdown slave core
 * otherwise speculative execution cause error
 */
	/* clear Vint cofigure */
	li.d	t0, (0x7 << 16)
	csrxchg zero, t0, 0x4
	/* set ebase address */
	li.d	t0, PHYS_TO_CACHED(0x1c001000)
	csrwr	t0, 0xc
	/* set TLB excption address */
	li.d	t0, 0x000000001c001000
	csrwr	t0, 0x88

	/* disable interrupt */
	li.d	t0, (1 << 2)
	csrxchg zero, t0, 0x0

	/* don't change this code,jumping to uncached address  emmc boot*/
#ifdef EMMC_BOOT
	li.d	t1, UNCACHED_MEMORY_ADDR
#else
	li.d	t1, CACHED_MEMORY_ADDR /*SPI FLASH*/ 
#endif
	bl	1f
1:
	addi.d	t0, ra, 12
	or	t0, t1, t0
	jirl	zero, t0, 0
	li.w	t0, 0xb0
	csrwr	t0, 0x0

	bl watchdog_close
	/*default mem config space*/
	li.d	t0, PHYS_TO_UNCACHED(0x14000100)
	ld.w	t1, t0, 0x0
	li.w	t2, ~(0x3 <<  4)
	and	t1, t1, t2
	li.w	t2, (1 << 5)
	or	t1, t1, t2
	st.w	t1, t0, 0x0

	/*[27:21] en [18:13] coherent*/
	/*0:usb    1:gmac 2:image 3:apb-dma 
	4:pr-dma  6:sc-dma*/
	li.d	t0, PHYS_TO_UNCACHED(0x14000104)
	ld.w	t1, t0, 0x0
	li.w	t2, ((0x6f << 13) | (0x6f << 21))
	or	t1, t1, t2
	st.w	t1, t0, 0x0

#ifdef EMMC_BOOT
#include "emmc_boot.S"
    .org	0x400
bootup:
#else
#endif
	/* calculate ASM stage print function s0 address */
    move s0, zero
	la	sp, stack
	la	gp, _gp
	/*gmac_mii_sel  rgmii mode*/
	li.d	t0, PHYS_TO_UNCACHED(0x14000100)
	ld.w	t1, t0, 0x0
	li.w	t2, ~(0x1 << 0)
	and	t1, t1, t2
	st.w	t1, t0, 0x0

	/* select uart0 */
	li.d	t0, PHYS_TO_UNCACHED(0x14000490)
	ld.w	t1, t0, 0x0
	li.w	t2, 0xf
	or	t1, t1, t2
	st.w	t1, t0, 0x0

	bl	initserial

bsp_start:
	PRINTSTR("\r\nPMON2000 LA Initializing. Standby...\r\n")
	dbar 0
	ibar 0

	bl	locate			/* Get current execute address */

	/* all exception entry */
	.org 0x1000
	/* s0 in different stage should fixup */
	la	a0, start
	li.d	a1, PHYS_TO_UNCACHED(0x1c000000)
	sub.d	a0, a0, a1
	li.d	a1, 0x00000000ffff0000
	and	a0, a0, a1
	beq	a0, s0, 1f
	move	s0, zero
1:
	and	s0, s0, a0
	csrrd	t0, 0x8a
	andi	t0, t0, 0x1
	bnez	t0, 2f

	PRINTSTR("\r\nCPU Trigger exception!\r\n")
	PRINT_CSR(0x4);
	PRINT_CSR(0x5);
	PRINT_CSR(0x6);
	PRINT_CSR(0x7);
	PRINT_CSR(0x8);
1:
	b	1b
2:
	li.d	t1, CACHED_MEMORY_ADDR
	bl	1f
1:
	addi.d	t0, ra, 12
	or	t0, t1, t0
	jirl	zero, t0, 0

	li.d	t0, 0xb0
	csrwr	t0, 0
	PRINTSTR("\r\nTLB exception!\r\n");
	PRINT_CSR(0x89);
	PRINT_CSR(0x8a);
1:
	b	1b

locate:
#include "clksetting_2p300.S"

#ifdef EMMC_BOOT
	PRINTSTR("run in cache.\r\n")
	move	a0, msize
    la	    ra, cache_main
	jirl	zero, ra, 0
#else
lock_scache:
	li.d	t0, PHYS_TO_UNCACHED(0x14000200)
	li.d	t1, ~(LOCK_CACHE_SIZE - 0x10000 - 1)
	st.d	t1, t0, 0x40
	li.d	t1, (LOCK_CACHE_BASE & 0xffffffffffff) | (1 << 63)
	st.d	t1, t0, 0x0

	li.d	t0, PHYS_TO_UNCACHED(0x14000208)
	li.d	t1, ~(0x10000 - 1)
	st.d	t1, t0, 0x40
	li.d	t1, ((LOCK_CACHE_BASE + LOCK_CACHE_SIZE - 0x10000) & 0xffffffffffff) | (1 << 63)
	st.d	t1, t0, 0x0

	li.d	t0, PHYS_TO_UNCACHED(0x14000210)
	li.d	t1, ~(0x8000 - 1)
	st.d	t1, t0, 0x40
	li.d	t1, ((LOCK_CACHE_BASE + LOCK_CACHE_SIZE) & 0xffffffffffff) | (1 << 63)
	st.d	t1, t0, 0x0

clear_scache:
	/* clear scache */
	li.d	a0, LOCK_CACHE_BASE
	li.d	a2, LOCK_CACHE_BASE + LOCK_CACHE_SIZE + 0x8000
1:
	st.d	zero, a0, 0
	ld.d	t0, a0, 0
	addi.d	a0, a0, 8
	bne	a2, a0, 1b
	
	/* copy flash code to scache */
	li.d	a1, PHYS_TO_CACHED(0x1c000000)
	la	a0, start
	la	a2, edata
1:
	ld.d	a3, a1, 0
	st.d	a3, a0, 0
	addi.d	a0, a0, 8
	addi.d	a1, a1, 8
	bne	a2, a0, 1b

	/* jump to locked cache address */
	li.d	t0, PHYS_TO_CACHED(0x1c00000c)
	li.d	t1, 0xfffff
	bl	1f
1:
	and	t1, ra, t1
	add.d	ra, t1, t0
	jirl	zero, ra, 0

	PRINTSTR("run in cache.\r\n")
	move	s0, zero
	move	a0, msize
	la	ra, cache_main
	jirl	zero, ra, 0
#endif

LEAF(watchdog_close)
	//disable watch DOG.
	li.d	t1, PHYS_TO_UNCACHED(0x14205000)
	ld.w	t2, t1, 0x0
	li.w	t3, ~0x2
	and	t2, t2, t3
	st.w	t2, t1, 0x0
	jirl	zero, ra, 0
END(watchdog_close)

#include "cpulib.S"
#include "serial.S"
