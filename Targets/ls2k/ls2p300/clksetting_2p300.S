#define SOFT_CLKSEL

#ifdef SOFT_CLKSEL
#if  defined(CLK_REF_100M)
#define CO_PREQ		100
#define SYS_REFC	4
#define DDR_REFC	4
#define VID_REFC	4
#elif defined(CLK_REF_24M) 
#define CO_PREQ		24
#define SYS_REFC	1
#define DDR_REFC	1
#define VID_REFC	1
#elif defined(CLK_REF_20M) 
#define CO_PREQ		20
#define SYS_REFC	1
#define DDR_REFC	1
#define VID_REFC	1
#endif

/* SYS PLL */
#if  defined(CLK_REF_100M)
#define SYS_HCLK	2500
#define LA132_FREQ	500						//	6/8=375Mhz
#define NODE_FREQ	1250					//CPU 1250Mhz
#elif defined(CLK_REF_24M) 
#define SYS_HCLK	3000
#define LA132_FREQ	500
#define NODE_FREQ	1000					//CPU 1000Mhz
#elif defined(CLK_REF_20M) 
#define SYS_HCLK	2500
#define LA132_FREQ	500
#define NODE_FREQ	1250					//CPU 1250Mhz
#endif
#define SOC_FREQ	500					//SYS0
#define SYS_LOOPC	(SYS_HCLK/(CO_PREQ/SYS_REFC))		/*1.2G~3.2G   2.4Gused*/
#define NODE_DIV	(CO_PREQ/SYS_REFC*SYS_LOOPC/NODE_FREQ)
#define LA132_DIV	(CO_PREQ/SYS_REFC*SYS_LOOPC/LA132_FREQ)
#define SOC_DIV		(CO_PREQ/SYS_REFC*SYS_LOOPC/SOC_FREQ)

/* DDR PLL */
#if  defined(DDR3_MODE)
#define DDR_HCLK		2600
#define NETWORK_FREQ		400				//NETWORK 300~400Mhz
#define IMG_FREQ		370				//SYS1 200~300Mhz
#else
//#define DDR_FREQ		800				//MEM 400~600Mhz
#define DDR_HCLK		2400
#define NETWORK_FREQ	400				//NETWORK 300~400Mhz
#define IMG_FREQ		400				//SYS1 200~300Mhz
#endif
#define DDR_LOOPC	(DDR_HCLK/(CO_PREQ/DDR_REFC))
#define DDR_DIV		(CO_PREQ/DDR_REFC*DDR_LOOPC/DDR_FREQ)
#define NETWORK_DIV	(CO_PREQ/DDR_REFC*DDR_LOOPC/NETWORK_FREQ)	//NETWORK 300~400MHz
#define	IMG_DIV		(CO_PREQ/DDR_REFC*DDR_LOOPC/IMG_FREQ)	//IMG 200~300MHz

/* VID PLL */
#define VID_HCLK	2400
#define PRVID_FREQ	300					//PRVID 200~300MHz
#define SCVID_FREQ	300					//SCVID 200~300MHz
#define GMAC_FREQ	300					//GMAC  125MHz
#define VID_LOOPC	(VID_HCLK/(CO_PREQ/VID_REFC))
#define PRVID_DIV	(CO_PREQ/VID_REFC*VID_LOOPC/PRVID_FREQ)
#define SCVID_DIV	(CO_PREQ/VID_REFC*VID_LOOPC/SCVID_FREQ)
#define GMACBP_DIV	(CO_PREQ/VID_REFC*VID_LOOPC/GMAC_FREQ)

#if ((DDR_LOOPC > 512) | (SYS_LOOPC > 512) | (VID_LOOPC > 512))
PLL LOOPC overflow
#endif

#define SEL_PLL0	(0x1)
#define SEL_PLL1	(0x2)
#define SEL_PLL2	(0x4)
#define PLL_L1_ENA	(0x1 << 3)
#define PLL_L1_LOCKED	(0x1 << 7)
#if 0
	TTYDBG ("Soft CLK SEL adjust begin\r\n")
	TTYDBG ("\r\nSYS	:")
#endif
	li.d	t0, PHYS_TO_UNCACHED(0x15103050)
	li.w	t1, (0x1 << 20)|(0x1 << 16)|(0x1<<10)|(0x1<<8)|(0x0<<6)|(0x0<<4)|(0x0<<2)|(0x0<<0)
	st.w	t1, t0, 0

	//apb and boot 1/2 of SOC freq
	li.d	t0, PHYS_TO_UNCACHED(0x15103020)
	ld.w	t1, t0, 0
	//li.w    t2, ~((0xf << 16) | (0xf << 12)) //apb boot
	li.w    t2, ~((0xf << 28) | (0xf << 24) | (0xf << 20)| (0xf << 16)|(0xf << 12)|(0xf << 8)|(0xf << 4)) //apb boot usb
	and     t1, t1, t2
	//li.w    t2, ((0x3 << 16) | (0x3 << 12))
	li.w    t2, ((0x1 << 28) | (0x1 << 24) | (0x5 << 20) | (0x5 << 16)| (0x5 <<	12)| (0x4 << 8)| (0x3 << 4))//200M
	or	t1, t1, t2
	st.w	t1, t0, 0

	//scdev and prdev freq 1/2 of ls132
	li.d	t0, PHYS_TO_UNCACHED(0x15103024)
	ld.w	t1, t0, 0
	li.w    t2, ~((0xf << 8)|(0xf << 4)|0xf) //prdev
	and     t1, t1, t2
	li.w    t2, (0x5 << 8)|(0x5 << 4)|(0x5)
	or	t1, t1, t2
	st.w	t1, t0, 0

	li.d	t0, PHYS_TO_UNCACHED(0x15103028)
	ld.w	t1, t0, 0
	li.w    t2, ~((0xf << 8)|(0xf << 4)|0xf) //scdev
	and     t1, t1, t2
	li.w    t2, (0x5 << 8)|(0x5 << 4)|(0x5)
	or	t1, t1, t2
	st.w	t1, t0, 0

	//node ls132 dev pll config
	li.d	t0, PHYS_TO_UNCACHED(0x15103000)
	li.w	t1, (0x1 << 5)	//power down pll L1 first
	st.w	t1, t0, 0
	li.w	t1, (NODE_DIV << 24) | (SYS_LOOPC << 15) | (SYS_REFC << 8)
	st.w	t1, t0, 0
	
	li.d	t2, PHYS_TO_UNCACHED(0x15103004)
	ld.w	t4, t2, 0
	li.w	t3, (SOC_DIV << 8) | (LA132_DIV) | (0xd00000) 
	or      t4, t4, t3
	st.w	t4, t2, 0x0
	
	ori	t1, t1, PLL_L1_ENA
	st.w	t1, t0, 0

11:
	ld.w	a0, t0, 0
	li.w	a1, PLL_L1_LOCKED
	and	a0, a1, a0
	beqz	a0, 11b //wait_locked_sys

	ld.w	a0, t0, 0
	ori	a0, a0, SEL_PLL0 | SEL_PLL1 | SEL_PLL2
	st.w	a0, t0, 0
	ld.w	a0, t0, 0

	bl	hexserial

//	TTYDBG ("\r\nDDR	:")

	li.d	t0, PHYS_TO_UNCACHED(0x15103008)
	li.w	t1, (0x1 << 5)	//power down pll first
	st.w	t1, t0, 0
	li.w	t1, (DDR_DIV << 24) | (DDR_LOOPC << 15) | (DDR_REFC << 8)
	
    li.d	t2, PHYS_TO_UNCACHED(0x1510300c)
	ld.w	t4, t2, 0
	li.w	t3, (IMG_DIV << 8) | (NETWORK_DIV) | 0xd00000 
	or      t4, t4, t3
	st.w	t4, t2, 0x0

	st.w	t1, t0, 0
	ori	t1, t1, PLL_L1_ENA
	st.w	t1, t0, 0

21:
	ld.w	a0, t0, 0
	li.w	a1, PLL_L1_LOCKED
	and	a0, a0, a1
	beqz	a0, 21b //wait_locked_ddr

	ld.w	a0, t0, 0
	ori	a0, a0, SEL_PLL0 | SEL_PLL1 | SEL_PLL2
	st.w	a0, t0, 0
	ld.w	a0, t0, 0

//	bl	hexserial

//	TTYDBG ("\r\nVID	:")
	li.d	t0, PHYS_TO_UNCACHED(0x15103010)
	li.w	t1, (0x1 << 5)	//power down pll first
	st.w	t1, t0, 0
	li.w	t1, (PRVID_DIV << 24) | (VID_LOOPC << 15) | (VID_REFC << 8)
    
    li.d	t2, PHYS_TO_UNCACHED(0x15103014)
	ld.w	t4, t2, 0
	li.w	t3, (GMACBP_DIV << 8) | (SCVID_DIV) | 0xd00000
	or      t4, t4, t3
	st.w	t4, t2, 0x0
	
	st.w	t1, t0, 0
	ori	t1, t1, PLL_L1_ENA
	st.w	t1, t0, 0

21:
	ld.w	a0, t0, 0
	li.w	a1, PLL_L1_LOCKED
	and	a0, a0, a1
	beqz	a0, 21b //wait_locked_ddr

	ld.w	a0, t0, 0
	ori	a0, a0, SEL_PLL0 | SEL_PLL1 | SEL_PLL2
	st.w	a0, t0, 0
	ld.w	a0, t0, 0

//	bl	hexserial
#endif
