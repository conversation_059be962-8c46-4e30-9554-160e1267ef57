#include <sys/types.h>
#include "target/bonito.h"
#include "target/ls2p300.h"

#if defined(EMMC_BOOT)
#if 0
void loop_delay(unsigned long long loops)
{
    volatile unsigned long long counts = loops;
    while (counts--);
}
#endif
static int emmc_cmd_check(unsigned long emmc_base, int cmd_index, int wait_rsp) 
{
	int cmd_fin = 0;
	volatile unsigned int rsp_cmdindex = 0;
	volatile unsigned int sdiintmsk = 0;

	while (cmd_fin == 0) {
		sdiintmsk = *(volatile unsigned int *)(emmc_base + SDIINTMSK);
		cmd_fin   = sdiintmsk & 0x3c0;
	}

	if (sdiintmsk & 0x100) {
		early_printf("cmd %d crc err \n",cmd_index);
	} else if (sdiintmsk & 0x80) {
		early_printf("cmd %d timeout \n",cmd_index);
	} else if (sdiintmsk & 0x40) {
	}

	*(volatile unsigned int *)(emmc_base + SDIINTMSK)  = (sdiintmsk & 0x1e0);
	return sdiintmsk;
}

static void emmc_send_cmd(unsigned long emmc_base, int cmd_index, int wait_rsp, int long_rsp, int check_crc, int cmd_arg) 
{
	int sdicmdcon;
	
	//* cmd 18 and cmd 25 need auto stop *//
	if (cmd_index==18 || cmd_index==25) {
		sdicmdcon = (cmd_index) & 0x3f | 0x140 | (wait_rsp << 9) | (long_rsp << 10) | (1 << 12) | (check_crc << 13);
	} else {
		sdicmdcon = (cmd_index) & 0x3f | 0x140 | (wait_rsp << 9) | (long_rsp << 10) | (0 << 12) | (check_crc << 13);
	}

	*(volatile unsigned int *)(emmc_base + SDICMDARG) = cmd_arg;
	*(volatile unsigned int *)(emmc_base + SDICMDCON) = sdicmdcon;
}

void emmc_load()
{
    unsigned long emmc_base = LS2P300_EMMC_BASE;
	volatile unsigned int sdiintmsk = 0;
	int cmd_index, wait_rsp, long_rsp, check_crc, cmd_arg;
	int cmd_fin = 0, data_fin = 0;
	unsigned int blk_num = 0x100000 / 512;
	*(volatile unsigned int *)(DMA_DESC_ADDR + DMA_DESC_OADDRLOW) = 0x0;
	*(volatile unsigned int *)(DMA_DESC_ADDR + DMA_DESC_SADDRLOW) = 0x100000;
	*(volatile unsigned int *)(DMA_DESC_ADDR + DMA_DESC_DADDR) = EMMC_DES_ADDR;
	*(volatile unsigned int *)(DMA_DESC_ADDR + DMA_DESC_LENGTH) = blk_num * 128;
	*(volatile unsigned int *)(DMA_DESC_ADDR + DMA_DESC_STEPLENGTH) = 0x0;
	*(volatile unsigned int *)(DMA_DESC_ADDR + DMA_DESC_STEPTIMES) = 0x1;
	*(volatile unsigned int *)(DMA_DESC_ADDR + DMA_DESC_CMD) = 0x1;
	*(volatile unsigned int *)DMA_CONFREG_BASE = (unsigned int)DMA_DESC_ADDR | 0x8;	// sdio0 use dma3, addr of desc is 0x10000

	*(volatile unsigned int *)(emmc_base + SDIBSIZE)   = 0x200; 
	*(volatile unsigned int *)(emmc_base + SDIDTIMER)  = 0x7fffff;
	*(volatile unsigned int *)(emmc_base + SDIDATCON)  = 0x1c000 | blk_num;
	*(volatile unsigned int *)(emmc_base + SDIINTMSK)  = 0x1ff;

	cmd_index = 18;
	wait_rsp = 1;
	long_rsp = 0;
	check_crc = 1;
	cmd_arg = 0x0;
	emmc_send_cmd(emmc_base,cmd_index,wait_rsp,long_rsp,check_crc,cmd_arg);  //
	emmc_cmd_check(emmc_base,cmd_index, wait_rsp);

	loop_delay(1000);
	int cnt = 0;

	early_printf("Copy bios data to mem.\n");
	while(data_fin == 0)
	{
		if (cnt++ == 100000) {
			early_printf(".");
			cnt = 0;
		}
		sdiintmsk = *(volatile unsigned int *)(emmc_base + SDIINTMSK);
		data_fin  = sdiintmsk & 0x1f;
	}

	early_printf("\n");

	if (sdiintmsk & 0x8) {
		early_printf("crc state err \n");
	} else if (sdiintmsk & 0x4) {
		early_printf("data crc err \n");
	} else if (sdiintmsk & 0x10) {
		early_printf("program err \n");
	} else if (sdiintmsk & 0x2) {
		early_printf("data time out \n");
	}

	*(volatile unsigned int *)(emmc_base + SDIINTMSK)  = (sdiintmsk & 0x1f);
}

void soc_flush_cache(unsigned long long base, unsigned int size)
{
    int i;
    for (i = 0; i < size; i += 0x40) {
        asm (
            "cacop 0x17, %0, 0\n\t"
            :
            : "r"(base)
        );
        base += 0x40;
    }
}
#endif
