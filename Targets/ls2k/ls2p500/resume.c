#include <include/stdio.h>
#include <sys/types.h>
#include <target/ls2p500.h>

#ifdef LS_STR
uint64_t mem_read64(unsigned long offset)
{
    return readq(STR_STORE_BASE + offset);
}

void check_str()
{
    uint64_t str_sp, str_ra;
    uint32_t system_s;

    system_s = readl(LS2P500_PM1_CNT_REG);
    tgt_printf("Sleep type: %x - system is ", system_s);
    if (system_s  != 0x12345678) {
	tgt_printf("normal boot.\n");
	return;
    }
    tgt_printf("S3 boot.\n");

    ls_mci_resume();

    str_ra = mem_read64(0x0);
    str_sp = mem_read64(0x8);

    //	tgt_fpudisable();
    /* misc:0x1008,0000 -- 0x100f,ffff */
    /* acpi offset 0x50000 of misc */
    /* 0x50 of acpi is cmos reg which storage s3 flag, now clear this flag */
    readl(LS2P500_PM1_CNT_REG) = 0;

    printf("Jump to kernel....\n");
    //used sp
    __asm__ __volatile__(
	    "or	$r3, %0, $r0		\n"
	    "or	$r1, %1, $r0		\n"
	    "jirl	$r0, $r1, 0		\n"
	    : /* No outputs */
	    :"r"(str_sp), "r"(str_ra)
	    );
}
#endif
