/*
 * This file is for 2P0500 pin and gpio ctrl
 */
#include "target/pincfgs.h"
#include <pmon.h>
#include <string.h>
pin_cfgs_t default_pin_cfgs[44] = {
	//0:GPIO, 1:first reuse, 2:second reuse, 3:main
	{0, 3}, //3:main_uart0_rx	1:sdio1_d[4]		2:gmac1_col	
	{1, 3}, //3:main_uart0_tx	1:sdio1_d[5]		2:gmac1_crs	
	{2, 3}, //3:main_uart1_rx	1:sdio1_d[6]		2:gmac1_ptp_trig	
	{3, 3}, //3:main_uart1_tx	1:sdio1_d[7]		2:gmac1_ptp_pps	
	{4, 3}, //3:main_i2c0_scl	1:main_uart2_rx		2:sdio0_d[4]	
	{5, 3}, //3:main_i2c0_sda	1:main_uart2_tx		2:sdio0_d[5]	
	{6, 3}, //3:main_i2c1_scl	1:main_uart3_rx		2:sdio0_d[6]	
	{7, 3}, //3:main_i2c1_sda	1:main_uart3_tx		2:sdio0_d[7]	
	{8, 3}, //3:main_spi0_clk	1:main_uart0_rts	2:pm1io[0]	
	{9, 3}, //3:main_spi0_miso	1:main_uart0_cts	2:pm1io[1]
	{10,3}, //3:main_spi0_mosi	1:main_uart0_dsr	2:pm1io[2]
	{11,3}, //3:main_spi0_cs[0]	1:main_uart0_dtr	2:pm1io[3]
#ifdef USE_CONSOLE_COM2
	{12,1}, //3:main_spi1_clk	1:main_uart0_dcd	2:pm1io[4]	0:GPIO 	
	{13,1}, //3:main_spi1_miso	1:main_uart0_ri		2:pm1io[5]	0:GPIO:SGM706 input
#else
	{12,3}, //3:main_spi1_clk	1:main_uart0_dcd	2:pm1io[4]	0:GPIO 	
	{13,3}, //3:main_spi1_miso	1:main_uart0_ri		2:pm1io[5]	0:GPIO:SGM706 input
#endif
	{14,3}, //3:main_spi1_mosi	1:main_uart1_rts	2:pm1io[6]	0:GPIO:GPIO_REBOOT
	{15,3}, //3:main_spi1_cs	1:main_uart1_cts	2:pm1io[7]	0:GPIO:SGM706 output
	{16,3}, //3:sdio0_clk		1:-			2:pm0io[0]
	{17,1}, //3:sdio0_cmd		1:main_spi0_cs[1]	2:pm0io[1]	
	{18,1}, //3:sdio0_d[0]		1:main_spi0_cs[2]	2:pm0io[2]	
	{19,1}, //3:sdio0_d[1]		1:main_spi0_cs[3]	2:pm0io[3]	
	{20,3}, //3:sdio0_d[2]		1:main_pwm[0]		2:pm0io[4]	
	{21,3}, //3:sdio0_d[3]		1:main_pwm[1]		2:pm0io[5]	
	{22,3}, //3:sdio1_clk		1:main_pwm[2]		2:pm0io[6]	
	{23,3}, //3:sdio1_cmd		1:main_pwm[3]		2:pm0io[7]	
	{24,3}, //3:sdio1_d[0]		1:main_pwm[4]		2:pm0io[8]	
	{25,3}, //3:sdio1_d[1]		1:main_pwm[5]		2:pm0io[9]	
	{26,3}, //3:sdio1_d[2]		1:main_pwm[6]		2:pm0io[10]	
	{27,3}, //3:sdio1_d[3]		1:main_pwm[7]		2:pm0io[11]	
	{28,3}, //3:pm2io[0]		1:main_pwm[8]		2:pm0io[12]	
	{29,3}, //3:pm2io[1]		1:main_pwm[9]		2:pm0io[13]	
	{30,3}, //3:pm2io[2]		1:main_pwm[10]		2:pm0io[14]	
	{31,3}, //3:pm2io[3]		1:main_pwm[11]		2:pm0io[15]	
	{32,2}, //3:pm2io[4]		1:main_pwm[12]		2:gmac1_rx_ctl	
	{33,2}, //3:pm2io[5]		1:main_pwm[13]		2:gmac1_rx[0]	
	{34,2}, //3:pm2io[6]		1:main_pwm[14]		2:gmac1_rx[1]	
	{35,2}, //3:pm2io[7]		1:main_pwm[15]		2:gmac1_rx[2]	
	{36,2}, //3:pm2io[8]		1:main_pwm[16]		2:gmac1_rx[3]	
	{37,2}, //3:pm2io[9]		1:main_pwm[17]		2:gmac1_tx_ctl	
	{38,2}, //3:pm2io[10]		1:main_pwm[18]		2:gmac1_tx[0]	
	{39,2}, //3:pm2io[11]		1:main_pwm[19]		2:gmac1_tx[1]	
	{40,2}, //3:pm2io[12]		1:main_pwm[20]		2:gmac1_tx[2]	
	{41,2}, //3:pm2io[13]		1:main_pwm[21]		2:gmac1_tx[3]	
	{42,2}, //3:pm2io[14]		1:main_pwm[22]		2:gmac1_mdck	
	{43,2}, //3:pm2io[15]		1:main_pwm[23]		2:gmac1_mdio	
};

void ls2p500_gpio_out(int gpio_num, int val)
{
	set_pin_mode(gpio_num, 0);
	uint64_t *addr_dir = LS2P500_GPIO_BIT_OEN;
	uint64_t *addr_out = LS2P500_GPIO_BIT_O;
	readq(addr_dir) &= ~(1ULL << gpio_num);
	readq(addr_out) = readq(addr_out) & ~(1ULL << gpio_num) | ((unsigned long long)!!val << gpio_num);
}

int ls2p500_gpio_in(int gpio_num)
{
	set_pin_mode(gpio_num, 0);
	uint64_t *addr_dir = LS2P500_GPIO_BIT_OEN;
	uint64_t *addr_in = LS2P500_GPIO_BIT_I;
	readq(addr_dir) |= (1ULL << gpio_num);
	return !!(readq(addr_in) & (1ULL << gpio_num));
}

int cmd_ls2p500_gpio_out(int ac, unsigned char *av[])
{
	if (ac != 3) {
		printf("gpio_out <gpio_num> <output_val>\n");
		return 0;
	}
	ls2p500_gpio_out(strtoul(av[1], NULL, 0), strtoul(av[2], NULL, 0));
	return 0;
}

int cmd_ls2p500_gpio_in(int ac, unsigned char *av[])
{
	if (ac != 2) {
		printf("gpio_in <gpio_num>\n");
		return 0;
	}
	printf("gpio read val: %d\n", ls2p500_gpio_in(strtoul(av[1], NULL, 0)));
	return 0;
}

static const Cmd Cmds[] =
{
	{"MyCmds"},
	{"gpio_out","",0,"set gpio out put <gpio num> <val>", cmd_ls2p500_gpio_out , 0, 3, CMD_REPEAT},
	{"gpio_in","",0,"set gpio in, and get val", cmd_ls2p500_gpio_in , 0, 2, CMD_REPEAT},
	{0,0}
};

static void init_cmd __P((void)) __attribute__ ((constructor));

static void
init_cmd()
{
	cmdlist_expand(Cmds,1);
}
