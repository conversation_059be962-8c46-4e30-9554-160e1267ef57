
#ifdef LS_STR
/*
 * 2K500 STR config start
 */
	.org 0x500
	/*************************************************************************
	/* This Code Must Be Execute Before Memory SelfRefresh Begain,
	/* Because Once We Enter SelfRefresh Mode,Memory Can't Be Accessed Any More
	/* We Leave Poweroff Op Later(After Enter SelfRefresh Mode)
	**************************************************************************/
	/* store ra and sp to memory */

	li.d	t0, STR_STORE_BASE
	st.d	a0, t0, 0x0	//store ra
	st.d    a1, t0, 0x8	//store sp

	li.w      t1, 0x100
1:
	addi.w  t1, t1, -1
	bnez    t1, 1b

	/*set memory controller selfrefresh*/
	/* Enable DDR control register */
	li.d	t0, LS2P500_GENERAL_CFG0
	ld.w    t1, t0, 0
	li.w	t2, (1 << 1)
	or		t1, t1, t2
	st.w    t1, t0, 0

	li.w	t2, ~(1 << 0)
	and		t1, t1, t2
	st.w    t1, t0, 0
	dbar    0 //sync

	/* Is necessary? */
/*
	li.w  	a0, 0x0
	li.d 	t0, 0x800000001fe10000

	STR_XBAR_CONFIG_NODE_a0(0x10, \
			0x0000000000000000, \
			0xfffffffff0000000, \
			0x00000000000000f0)
*/
	li.d 	a0, 0x800000000ff00000
	ld.d	t1, a0, 0x198
	li.d	t3, 0x0000000f00000000
	or  	t1, t1, t3  /* bit32 for self refresh*/
	st.d  	t1, a0, 0x198
	dbar    0

	/* Don't forget to recovery the XbarII config Window is necessary? */
/*
	li.w  a0,0x0
	li.d t0, 0x800000001fe10000
	STR_XBAR_CONFIG_NODE_a0(0x10, \
			0x0000000000000000, \
			0xfffffffff0000000, \
			0x00000000000000f0)
*/
	dbar    0 //sync
	dbar    0 //sync
	dbar    0 //sync
	dbar    0 //sync

	/* delay */
	li.w	t0, 0x4000
1:
	addi.w	t0, t0, -1
	bnez	t0, 1b

	/* set mark on sram end */
	li.d 	a0, LS2P500_PM1_CNT_REG
	li.w	t0, 0x12345678
	st.w	t0, a0, 0

	/* boot slave core */
	li.d 	a0, 0x8000000015104034
	st.b	zero, a0, 0
	li.w	t0, 0x20
	st.b	t0, a0, 0
	li.w	t0, 0x10
	st.b	t0, a0, 0
	li.w	t0, 0x30
	st.b	t0, a0, 0
	idle 0

	/* delay */
	li.w  t0, 0x40000
2:
	addi.w  t0, t0, -1
	bnez    t0, 2b

1:
	b  1b
#endif
