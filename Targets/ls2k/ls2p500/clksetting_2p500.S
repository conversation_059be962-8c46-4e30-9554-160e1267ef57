#define SOFT_CLKSEL

#ifdef SOFT_CLKSEL
/* SYS PLL */
#if 0
#define NODE_FREQ	600					//CPU 600Mhz
#define LA132_FREQ	300
#define SOC_FREQ	125
#define NODE_REFC	4
#define NODE_DIV	5
#define NODE_LOOPC	(NODE_FREQ*NODE_REFC*NODE_DIV/100)		//120
#define LA132_DIV	(100*NODE_LOOPC/NODE_REFC/LA132_FREQ)		//15
#define SOC_DIV		(100*NODE_LOOPC/NODE_REFC/SOC_FREQ)		//6
#else
#define NODE_FREQ	750					//CPU 600Mhz
#define LA132_FREQ	300
#define SOC_FREQ	125
#define NODE_REFC	4
#define NODE_DIV	4
#define NODE_LOOPC	(NODE_FREQ*NODE_REFC*NODE_DIV/100)		//120
#define LA132_DIV	(100*NODE_LOOPC/NODE_REFC/LA132_FREQ)		//15
#define SOC_DIV		(100*NODE_LOOPC/NODE_REFC/SOC_FREQ)		//6
#endif

/* DDR PLL */
//#define DDR_FREQ	400					//MEM 400~600Mhz
#define DDR_REFC	4
#define DDR_DIV		6
#define DDR_LOOPC	(DDR_FREQ*DDR_REFC*DDR_DIV/100)
//#define NETWORK_DIV	((DDR_FREQ*DDR_DIV+100)/200)		//NETWORK 300~400MHz
#define NETWORK_DIV	8					//NETWORK 300~400MHz
#define IMG_DIV		(DDR_DIV * 2)				//IMG 200~300MHz

#define PRVID_FREQ	300					//PRVID 200~300MHz
#define SCVID_FREQ	300					//SCVID 200~300MHz
#define VID_REFC	4
#define PRVID_DIV	5
#define SCVID_DIV	5
#define VID_LOOPC	(PRVID_FREQ*VID_REFC*PRVID_DIV/100)

#if ((DDR_LOOPC > 255) | (NODE_LOOPC > 255) | (VID_LOOPC > 255))
PLL LOOPC overflow
#endif

#define SEL_PLL0	(0x1)
#define SEL_PLL1	(0x2)
#define SEL_PLL2	(0x4)
#define PLL_L1_ENA	(0x1 << 3)
#define PLL_L1_LOCKED	(0x1 << 7)

	TTYDBG ("Soft CLK SEL adjust begin\r\n")
	TTYDBG ("\r\nSYS	:")

	//emmc 6/8 of SOC freq
	li.d	t0, PHYS_TO_UNCACHED(0x15103020)
	ld.w	t1, t0, 0
	li.w    t2, ~((0xf << 20)) //emmc
	and     t1, t1, t2
	li.w    t2, ((0x7 << 20))
	or	t1, t1, t2
	st.w	t1, t0, 0
	//gmac clksel GMACBP-PLL
	li.d	t0, PHYS_TO_UNCACHED(0x1510302c)
	ld.w	t1, t0, 0
	li.w    t2, (0x1 << 31)
	or     t1, t1, t2
	st.w	t1, t0, 0


	//scdev and prdev freq 1/2 of ls132
	li.d	t0, PHYS_TO_UNCACHED(0x15103024)
	ld.w	t1, t0, 0
	li.w    t2, ~(0xf << 8) //prdev
	and     t1, t1, t2
	li.w    t2, (0xA << 8)
	or	t1, t1, t2
	st.w	t1, t0, 0

	li.d	t0, PHYS_TO_UNCACHED(0x15103028)
	ld.w	t1, t0, 0
	li.w    t2, ~(0xf << 8) //scdev
	and     t1, t1, t2
	li.w    t2, (0xA << 8)
	or	t1, t1, t2
	st.w	t1, t0, 0

	li.d	t0, PHYS_TO_UNCACHED(0x15103000)
	ld.w	t1, t0, 0
	bstrins.d t1, zero, 7,7
	beqz t1, 10f
	li.d	t0, PHYS_TO_UNCACHED(0x14001400)
	li.w t1, -1
	st.w t1, t0, 0x2c
	st.w t1, t0, 0x6c
	b 22f

10:
	//node ls132 dev pll config
	li.d	t0, PHYS_TO_UNCACHED(0x15103000)
	li.w	t1, (0x1 << 5)	//power down pll L1 first
	st.w	t1, t0, 0
	li.w	t1, (NODE_DIV << 24) | (NODE_LOOPC << 16) | (NODE_REFC << 8)
	li.w	t2, (SOC_DIV << 8) | (LA132_DIV)
	st.w	t1, t0, 0
	st.w	t2, t0, 0x4
	ori	t1, t1, PLL_L1_ENA
	st.w	t1, t0, 0

11:
	ld.w	a0, t0, 0
	li.w	a1, PLL_L1_LOCKED
	and	a0, a1, a0
	beqz	a0, 11b //wait_locked_sys

	ld.w	a0, t0, 0
	ori	a0, a0, SEL_PLL0 | SEL_PLL1 | SEL_PLL2
	st.w	a0, t0, 0
	ld.w	a0, t0, 0

	bl	hexserial

	TTYDBG ("\r\nDDR	:")

	li.d	t0, PHYS_TO_UNCACHED(0x15103008)
	li.w	t1, (0x1 << 5)	//power down pll first
	st.w	t1, t0, 0
	li.w	t1, (DDR_DIV << 24) | (DDR_LOOPC << 16) | (DDR_REFC << 8)
	li.w	t2, (IMG_DIV << 8) | (NETWORK_DIV)
	st.w	t1, t0, 0
	st.w	t2, t0, 0x4
	ori	t1, t1, PLL_L1_ENA
	st.w	t1, t0, 0

21:
	ld.w	a0, t0, 0
	li.w	a1, PLL_L1_LOCKED
	and	a0, a0, a1
	beqz	a0, 21b //wait_locked_ddr

	ld.w	a0, t0, 0
	ori	a0, a0, SEL_PLL0 | SEL_PLL1 | SEL_PLL2
	st.w	a0, t0, 0
	ld.w	a0, t0, 0

	bl	hexserial

	TTYDBG ("\r\nVID	:")

	li.d	t0, PHYS_TO_UNCACHED(0x15103010)
	li.w	t1, (0x1 << 5)	//power down pll first
	st.w	t1, t0, 0
	li.w	t1, (PRVID_DIV << 24) | (VID_LOOPC << 16) | (VID_REFC << 8)
	li.w	t2, SCVID_DIV
	st.w	t1, t0, 0
	st.w	t2, t0, 0x4
	ori	t1, t1, PLL_L1_ENA
	st.w	t1, t0, 0

21:
	ld.w	a0, t0, 0
	li.w	a1, PLL_L1_LOCKED
	and	a0, a0, a1
	beqz	a0, 21b //wait_locked_ddr

	ld.w	a0, t0, 0
	ori	a0, a0, SEL_PLL0 | SEL_PLL1
	st.w	a0, t0, 0
	ld.w	a0, t0, 0

	bl	hexserial
22:
#endif
