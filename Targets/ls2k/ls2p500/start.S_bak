/*
 * Copyright (c) 2021 Loongson Technology Corporation Limited (www.loongson.cn)
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *	This product includes software developed by Opsycon AB, Sweden.
 * 4. The name of the author may not be used to endorse or promote products
 *    derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 */

#ifndef _KERNEL
#define _KERNEL
#endif

#include <asm.h>
#include <regnum.h>
#include <cpu.h>
#include <pte.h>

#include "pmon/dev/ns16550.h"
#include "target/bonito.h"
#include "target/ls2p500.h"

#include "target/cacheops.h"

#define DEBUG_LOCORE
#ifdef DEBUG_LOCORE
#define TTYDBG(x) \
	.section .rodata;98: .asciz x; .text; la a0, 98b; bl stringserial
#define TTYDBG_COM1(x) \
	.section .rodata;98: .asciz x; .text; la a0, 98b; bl stringserial_COM1
#else
#define TTYDBG(x)
#define TTYDBG_COM1(x)
#endif
#define	PRINTSTR TTYDBG
#define PRINT_CSR(offset)	\
	PRINTSTR("\r\ncsr 0x");	\
	li.w	a0, offset;	\
	bl	hexserial;	\
	PRINTSTR(" ->0x");	\
	csrrd	a0, offset;	\
	bl	hexserial64;	\
	PRINTSTR("\r\n");


#define CONFIG_CACHE_64K_4WAY 1
##define USEPCI

#define msize		s2

/*
 * Register usage:
 *
 * s0 link versus load offset, used to relocate absolute adresses.
 * s1 free
 * s2 memory size.
 * s3 st.dShape.
 * s4 Bonito base address.
 * s5 dbg.
 * s6 st.dCfg.
 * s7 rasave.
 * s8 L3 Cache size.
 */


	.globl	_start
	.globl	start
	.globl	__main
_start:
start:
	.globl	stack
stack = start + LOCK_CACHE_SIZE	 /* Place PMON stack in the end of 2M RAM */
	csrrd	t0, 0xc0
	li.w	t1, 0xf000
	and	t0, t0, t1
#if 0
	li	t1, 0xb000
	beq	t0, t1, 1f
#else
	li.w	t1, 0x8000
	bne	t0, t1, 1f
#endif
	li.d	t0, 0xe0000000
	jirl	zero, t0, 0
1:
	/*default mem config space*/
	li.d	t0, PHYS_TO_UNCACHED(0x14000100)
	ld.w	t1, t0, 0x0
	li.w	t2, ~0x3
	and	t1, t1, t2
	li.w	t2, (1 << 1)
	or	t1, t1, t2
	st.w	t1, t0, 0x0

	/*config resume coherent*/
	/*[51:44] en [59:52] coherent*/
	/*0:cpu    1:usb     2:gmac1    3:resume(gmac0 otg)   4:imp(jpeg jbig) 
		5:apb-dma(sdio nand)  6:pr-dma   7:sc-dma*/
	li.d	t0, PHYS_TO_UNCACHED(0x14000104)
	ld.w	t1, t0, 0x0
	li.w	t2, ((0x7f << 13) | (0x7f << 21))
	or	t1, t1, t2
	st.w	t1, t0, 0x0

	/*gmac1_mii_sel  rgmii mode*/
	li.d	t0, PHYS_TO_UNCACHED(0x14000100)
	ld.w	t1, t0, 0x0
	li.w	t2, ~(0x1 << 14)
	and	t1, t1, t2
	st.w	t1, t0, 0x0

	li.d	t0, (1 << (24 + 32))	//set CPUCFG 0 bit24
	csrxchg	t0, t0, 0xc0

	li.d	t0, (0xa << (16 + 32))	//set CPUCFG 0x13 bit16-23
	li.d	t1, (0xff << (16 + 32))
	csrxchg	t0, t1, 0xc9

	/* enable perf counter as cp0 counter */
	li.w	t0, (0x1 << 16)
	csrxchg t0, t0, 0x200

	/* open auto flush SFB */
	li.w	t0, (0x1 << 9)
	csrxchg t0, t0, 0x80

	/* fast_ldq_dis close */
	li.w	t0, (0x1 << 12)
	csrxchg zero, t0, 0x80

	/* low power setting */
	li.w	t0, 0xe
	csrxchg t0, t0, 0xf0

	li.d	t0, UNCACHED_MEMORY_ADDR | 0x1
	csrwr	t0, 0x180
	li.d	t0, CACHED_MEMORY_ADDR | 0x11
	csrwr	t0, 0x181

/*
 * should before execution jr shutdown slave core
 * otherwise speculative execution cause error
 */
	/* clear Vint cofigure */
	li.d	t0, (0x7 << 16)
	csrxchg zero, t0, 0x4
	/* set ebase address */
	li.d	t0, PHYS_TO_CACHED(0x1c001000)
	csrwr	t0, 0xc
	/* set TLB excption address */
	li.d	t0, 0x000000001c001000
	csrwr	t0, 0x88

	/* enable llexc */
	li.w	t0, (1 << 3)
	csrxchg zero, t0, 0xc1

	/* disable interrupt */
	li.d	t0, (1 << 2)
	csrxchg zero, t0, 0x0

	la	sp, stack
	la	gp, _gp
#if 0
	/* spi speedup */
	li.d	t0, PHYS_TO_UNCACHED(0x1fd00000)
	li.w	t1, 0x47
	st.b	t1, t0, 0x4

//#define SPI_QUAD_IO
#ifdef	SPI_QUAD_IO
	/* spi quad_io */
	li.w	t1, 0xb
	st.b	t1, t0, 0x6
spi_quad_io:
	ld.bu	t2, t0, 0x6
	bne	t2, t1, spi_quad_io
#endif
#endif
	/* don't change this code,jumping to cached address */
	li.d	t1, CACHED_MEMORY_ADDR
	bl	1f
1:
	addi.d	t0, ra, 12
	or	t0, t1, t0
	jirl	zero, t0, 0
	/* now pc run to 0x98xxxxxxxxxxxxxx */
	/* DA disable for 0x98xxxxxxxxxxxxxx and 0x90xxxxxxxxxxxx address can be used */
	li.w	t0, 0xb0
	csrwr	t0, 0x0

	/* calculate ASM stage print function s0 address */
	la	s0, start
	li.d	a0, PHYS_TO_UNCACHED(0x1c000000)

	/* if change locked cache address may need change the following code */
	sub.d	s0, s0, a0

	la	sp, stack
	la	gp, _gp



#ifdef USE_CONSOLE_COM2
	li.d	t0, PHYS_TO_UNCACHED(0x14000100)
	ld.w	t1, t0, 0x0
	li.w	t2, (0x7<<28)
	//li	t2, 0xff000f
	or	t1, t1, t2
	st.w	t1, t0, 0x0
#endif

	/* select uart0 */
	li.d	t0, PHYS_TO_UNCACHED(0x14000490)
	ld.w	t1, t0, 0x0
#ifndef USE_CONSOLE_COM2
	li.w	t2, 0xf
#else
	li.w	t2, ~(0xf<<24)
	and	t1, t1, t2
	li.w	t2, (0x5<<24)
#endif
	or	t1, t1, t2
	st.w	t1, t0, 0x0
#ifndef USE_CONSOLE_COM2
	li.d	a0, COM1_BASE_ADDR /*uart0*/
#else
	li.d	a0, COM2_BASE_ADDR /*uart2*/
#endif

	bl	initserial

bsp_start:
	PRINTSTR("\r\nPMON2000 LOONGARCH Initializing. Standby...\r\n")
	dbar 0
	ibar 0
	nop

	bl	locate			/* Get current execute address */
	nop

	/* this code start address is 0x500 */
//#include "resume.S"

	/* all exception entry */
	.org 0x1000
	/* s0 in different stage should fixup */
	la	a0, start
	li.d	a1, PHYS_TO_UNCACHED(0x1c000000)
	sub.d	a0, a0, a1
	li.d	a1, 0x00000000ffff0000
	and	a0, a0, a1
	beq	a0, s0, 1f
	move	s0, zero
1:
	and	s0, s0, a0
	csrrd	t0, 0x8a
	andi	t0, t0, 0x1
	bnez	t0, 2f

	PRINTSTR("\r\nCPU Trigger exception!\r\n")
	PRINT_CSR(0x4);
	PRINT_CSR(0x5);
	PRINT_CSR(0x6);
	PRINT_CSR(0x7);
	PRINT_CSR(0x8);
1:
	b	1b
2:
	li.d	t1, CACHED_MEMORY_ADDR
	bl	1f
1:
	addi.d	t0, ra, 12
	or	t0, t1, t0
	jirl	zero, t0, 0

	li.d	t0, 0xb0
	csrwr	t0, 0
	PRINTSTR("\r\nTLB exception!\r\n");
	PRINT_CSR(0x89);
	PRINT_CSR(0x8a);
1:
	b	1b

locate:
	PRINTSTR("\r\ninitserial good ^_^...\r\n")

#include "clksetting_2p500.S"

	//clk chaneged need this code
	//bl	initserial_later

start_now:
#if 0
	/*map 1fe1 -> 1400*/
	dli	t0, PHYS_TO_UNCACHED(0x14002078)
	dli	t1, 0x000000001fe10000
	st.d	t1, t0, 0x0

	dli	t0, PHYS_TO_UNCACHED(0x140020f8)
	dli	t1, 0xffffffffffff0000
	st.d	t1, t0, 0x0

	dli	t0, PHYS_TO_UNCACHED(0x14002178)
	dli	t1, 0x0000000014000085
	st.d	t1, t0, 0x0
#endif

	PRINTSTR("\r\nPMON2000 LOONGARCH Initializing. Standby...\r\n")

	bnez	s0, 1f

	li.w	a0, 128
	la	ra, init_loongarch
	jirl	zero, ra, 0
1:

//#include "machine/newtest/newdebug.S"
#include "ddr_dir/ddr_entry.S"

#if 1
	PRINTSTR("\r\nlock scache ")
	li.d	a0, LOCK_CACHE_BASE
	bl	hexserial
	PRINTSTR(" - ")
	li.d	a0, LOCK_CACHE_BASE + LOCK_CACHE_SIZE
	bl	hexserial

	li.d	t0, PHYS_TO_UNCACHED(0x14000200)
	li.d	t1, ~(LOCK_CACHE_SIZE - 1)
	st.d	t1, t0, 0x40
	li.d	t1, (LOCK_CACHE_BASE & 0xffffffffffff) | (1 << 63)
	st.d	t1, t0, 0x0

	PRINTSTR("\r\nLock Scache Done.\r\n")
	/* copy flash code to scache */
	li.d	a1, PHYS_TO_CACHED(0x1c000000)
	la	a0, start
	la	a2, edata
1:
	ld.d	a3, a1, 0
	st.d	a3, a0, 0
	addi.d	a0, a0, 8
	addi.d	a1, a1, 8
	bne	a2, a0, 1b

	PRINTSTR("copy text section to lock cache done.\r\n")
	/*clear cache mem BSS in this space*/
	la	a0, _edata
	la	a1, _end
1:
	st.d	zero, a0, 0
	addi.d	a0, a0, 8
	blt	a0, a1, 1b

	li.d	a0, LOCK_CACHE_BASE
	li.d	a1, LOCK_CACHE_BASE + LOCK_CACHE_SIZE
#if 1
1:
	/* ensure locked cache address accessible so this code can`t remove*/
	ld.d	zero, a0, 0
	addi.d	a0, a0, 0x40
	bne	a1, a0, 1b
#endif
	/* jump to locked cache address */
	/* ra addr is 0xffffffff9fcxxxxx */
	li.d	t0, PHYS_TO_CACHED(0x1c00000c)
	li.d	t1, 0xfffff
	bl	1f
1:
	and	t1, ra, t1
	add.d	ra, t1, t0
	jirl	zero, ra, 0

	PRINTSTR("run in cache.\r\n")
	move	s0, zero

	la	ra, cache_main
	jirl	zero, ra, 0
#endif

LEAF(get_cpuprid)
	csrrd	a0, 0xc0
	jirl	zero, ra, 0
END(get_cpuprid)

/*
 * Simple character printing routine used before full initialization
 */
/* baud rate definitions, matching include/termios.h */
#define B0	0
#define B50	50
#define B75	75
#define B110	110
#define B134	134
#define B150	150
#define B200	200
#define B300	300
#define B600	600
#define B1200	1200
#define B1800	1800
#define B2400	2400
#define B4800	4800
#define B9600	9600
#define B19200	19200
#define B38400	38400
#define B57600	57600
#define B115200	115200

#define UART_REF_CLK	100000000
#define UART_DIV_HI	(((UART_REF_CLK + (115200*8)) / (115200*16)) >> 8)
#define UART_DIV_LO	(((UART_REF_CLK + (115200*8)) / (115200*16)) & 0xff)
/*************************************
 *used: a0~a1
 *************************************/
LEAF(initserial)
//	li.d	a0, COM1_BASE_ADDR
	li.w	a1, 0x80
	st.b	a1, a0, 3

	li.w	a1, UART_DIV_HI
	st.b	a1, a0, 1
	li.w	a1, UART_DIV_LO
	st.b	a1, a0, 0
	li.w	a1, 3	#CFCR_8BITS
	st.b	a1, a0, 3

	li.w	a1, 71
	st.b	a1, a0, 2
	jirl	zero, ra, 0
END(initserial)
#if 0
#define UART_REF_CLK_PLL	(GMAC_FREQ * GMAC_DIV / SB_DIV * 1000000)	//SB PLL
#define UART_DIV_HI_2		(((UART_REF_CLK_PLL + (115200*8)) / (115200*16)) >> 8)
#define UART_DIV_LO_2		(((UART_REF_CLK_PLL + (115200*8)) / (115200*16)) & 0xff)
LEAF(initserial_later)
	li.d	a0, COM1_BASE_ADDR
	li.w	a1, 0x80
	st.b	a1, a0, 3

	li.w	a1, UART_DIV_HI_2
	st.b	a1, a0, 1
	li.w	a1, UART_DIV_LO_2
	st.b	a1, a0, 0
1:
	ld.b	a0, a0, 0
	bne	a1, a0, 1b

	li.d	a0, COM1_BASE_ADDR
	li.w	a1, 3	#CFCR_8BITS
	st.b	a1, a0, 3

	li.w	a1, 71
	st.b	a1, a0, 2
	jirl	zero, ra, 0
END(initserial_later)
#endif
/******************************************************
 *used: a0~a2
 ******************************************************/
LEAF(tgt_putchar)
#ifndef USE_CONSOLE_COM2
	li.d	a1, COM1_BASE_ADDR /*uart0*/
#else
	li.d	a1, COM2_BASE_ADDR /*uart2*/
#endif
1:
	ld.bu	a2, a1, 0x5
	andi	a2, a2, 0x20
	beqz	a2, 1b

	st.b	a0, a1, 0
	//	or	a2, zero, a1

	jirl	zero, ra, 0
END(tgt_putchar)

/******************************************************
 *used: a0~a4, s0
 ******************************************************/
LEAF(stringserial)
	or	a4, ra, zero
	sub.d	a3, a0, s0
	ld.bu	a0, a3, 0
1:
	beqz	a0, 2f

	bl	tgt_putchar

	addi.d	a3, a3, 1
	ld.bu	a0, a3, 0
	b	1b

2:
	ori	ra, a4, 0
	jirl	zero, ra, 0
END(stringserial)

/*****************************************************
 *used: a0~a5, s0
 *****************************************************/
LEAF(hexserial)
	ori	a4, ra, 0
	ori	a3, a0, 0
	li.d	a5, 8
1:
	rotri.w a0, a3, 28
	or	a3, a0, zero
	andi	a0, a0, 0xf

	la	a1, hexchar
	sub.d	a1, a1, s0

	add.d	a1, a1, a0
	ld.bu	a0, a1, 0

	bl	tgt_putchar

	addi.d	a5, a5, -1
	bnez	a5, 1b

	ori	ra, a4, 0
	jirl	zero, ra, 0
END(hexserial)


/*****************************************************
 *used: a0~a5, s0
 *****************************************************/
LEAF(hexserial64)
	ori	a4, ra, 0
	ori	a3, a0, 0
	li.d	a5, 16
1:
	rotri.d a0, a3, 60
	or	a3, a0, zero
	andi	a0, a0, 0xf

	la	a1, hexchar
	sub.d	a1, a1, s0

	add.d	a1, a1, a0
	ld.bu	a0, a1, 0

	bl	tgt_putchar

	addi.d	a5, a5, -1
	bnez	a5, 1b

	ori	ra, a4, 0
	jirl	zero, ra, 0
END(hexserial64)

	.section .rodata
hexchar:
	.ascii	"0123456789abcdef"
	.text
	.align 5

LEAF(tgt_testchar)
#ifndef USE_CONSOLE_COM2
	li.d	a0, COM1_BASE_ADDR /*uart0*/
#else
	li.d	a0, COM2_BASE_ADDR /*uart2*/
#endif
	ld.bu	a1, a0, NSREG(NS16550_LSR)
	andi	a0, a1, LSR_RXRDY
	jirl	zero, ra, 0
END(tgt_testchar)

LEAF(tgt_getchar)
#ifndef USE_CONSOLE_COM2
	li.d	a0, COM1_BASE_ADDR /*uart0*/
#else
	li.d	a0, COM2_BASE_ADDR /*uart2*/
#endif
1:
	ld.bu	a1, a0, NSREG(NS16550_LSR)
	andi	a1, a1, LSR_RXRDY
	beqz	a1, 1b
	ld.b	a0, a0, NSREG(NS16550_DATA)
	jirl	zero, ra, 0
END(tgt_getchar)

    .text
    .global  nvram_offs
    .align 12
nvram_offs:
    .dword 0x0
    .align 12
#######################################


    .section .rdata
    .global ddr2_reg_data
    .global ddr3_reg_data
    .align  5
#include "ddr_dir/loongson_mc2_param.S"
