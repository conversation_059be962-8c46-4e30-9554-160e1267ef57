#include <stdio.h>

#include <stdlib.h>
#include <ctype.h>
#undef _KERNEL
#include <errno.h>
#include <pmon.h>
#include <cpu.h>
#include "types.h"
#include <pflash.h>
#include <fcntl.h>
#include "target/bonito.h"
#include <linux/mtd/qspinand.h>
#include <linux/spi.h>
#include <linux/bitops.h>
#include "m25p80.h"
#define QSPICR		0x0
#define QSPICSR		0x1
#define QSPIDR		0x2
#define SPISR		0x3
#define RSSR		0xf

extern unsigned char qspi_flags;

#define QSPI_BASE 0x14010000 //spi0
char* ls_qspi_base = PHYS_TO_UNCACHED(QSPI_BASE);	
#define RFEMPTY 1
#define write8(addr, val)	(*(volatile unsigned char *)(addr) = val)
#define read8(addr)		(*(volatile unsigned char *)(addr))

#define read32(addr)		*(volatile int *)(addr)

#define SET_SPI(addr, val)      write8(ls_qspi_base + addr, val)
#define GET_SPI(addr)           read8(ls_qspi_base + addr)
#define SET_QSPI(addr, val)	write8(ls_qspi_base + addr, val)
#define GET_QSPI(addr)	 	read8(ls_qspi_base + addr)



void init_qspi()
{
	uint8_t	qspi_csr;
	if (!ls_qspi_base)
		ls_qspi_base  = PHYS_TO_UNCACHED(QSPI_BASE);
	qspi_flags = 1;
	SET_QSPI(0xf, 0x1);	//switch register space from spi  to qspi
	SET_QSPI(0x3, 0x1); //switch spi interface from spi to qspi
	SET_QSPI(0x4, 0x1); 
#if 1
	*(volatile unsigned int *)(0x8000000014000498) &= ~(0x3<<26);	
	*(volatile unsigned char*)(0x800000001420182d) = 0;
#if 1
	*(volatile unsigned int *)(0x8000000014000490) &= ~(0x3<<14);	
	*(volatile unsigned char*)(0x8000000014201807) = 0;
	*(volatile unsigned char*)(0x8000000014201907) = 1;
#endif
#endif
	qspi_flags = 1;
}

void exit_qspi()
{
	if (!ls_qspi_base)
		ls_qspi_base  = PHYS_TO_UNCACHED(QSPI_BASE);
		//ls7a_spi_base  = PHYS_TO_UNCACHED(0x1fe001f0);
		//ls7a_spi_base  = PHYS_TO_UNCACHED(read32(BONITO_PCICFG0_BASE_VA | (22ULL << 11) + 0x10) & 0xfffffff0);
	SET_QSPI(0x3, 0x0);	//switch spi interface from qspi to spi
	SET_QSPI(0xf, 0x0);	//switch register space form qspi to spi
	*(volatile unsigned int *)(0x8000000014000490) |= (0x3<<14);	
	qspi_flags = 0;
}

/*WE ASSUME THAT WE HAVE SWITCHED TO QSPI */
void set_qspi_cs()
{
#if 0
	uint8_t	qspi_csr	=	GET_QSPI(0x1);
	SET_QSPI(0x1,(qspi_csr & 0xf0));
#else
#ifdef QSPI_CS_REVERT
	*(volatile unsigned char*)(0x800000001420192d) = 1;
#else
	*(volatile unsigned char*)(0x800000001420192d) = 0;
#endif
	delay(1);
#endif
}

void reset_qspi_cs()
{
#if 0
	uint8_t	qspi_csr	=	GET_QSPI(0x1);
	SET_QSPI(0x1,(qspi_csr | 0x08));
#else
	delay(1);
#ifdef QSPI_CS_REVERT
	*(volatile unsigned char*)(0x800000001420192d) = 0;
#else
	*(volatile unsigned char*)(0x800000001420192d) = 1;
#endif
#endif
}

void qspi_set_spi_type(uint8_t Qtype)
{
	/*************/
	//binary 00: standard spi; 01: dual spi; 10: quad spi;
	/*************/
	//#ifdef NEW_RELEASE1  ERROR
	uint8_t qspi_cr	=	GET_QSPI(QSPICR);
	SET_QSPI(QSPICR, (qspi_cr & 0xe7) | ((Qtype & 0x03)<<3));	//set spi type
}


unsigned char flash_type;
int qspi_write_read_8bit(const u8 **tx_buf, u8 **rx_buf, unsigned int num)
{

	if (tx_buf && *tx_buf){
		if(flash_type)
		printf("tx_buf is %x\n",*((*tx_buf)));
		SET_QSPI(QSPIDR, *((*tx_buf)++));
	}else{
	//	bcm_qspi_write(qspi, MSPI, LOONGSON_QSPIDR, 0);
	//	while((bcm_qspi_write(qspi, MSPI, LOONGSON_QSPICSR) & 0x1) == 1);
	}

	if (rx_buf && *rx_buf) {
		*(*rx_buf) = GET_QSPI(QSPIDR);
		if(flash_type)
		printf("rx_buf is %x\n",*((*rx_buf)));
		(*rx_buf)++ ;
	}else{
//		ls_spi_read_reg(ls_spi,QSPIDR);
	}
	return 1;
}

static unsigned int ls_qspi_write_read(struct spi_transfer *xfer)
{
	unsigned int count;
	const u8 *tx = xfer->tx_buf;
	u8 width;
	u8 *rx = xfer->rx_buf;

	count = xfer->len;
#if 1
	if(tx){
		if(xfer->tx_nbits == 1){
			qspi_set_spi_type(0);	//quad SPI   0 单线  1 双线  2 四线
		}else{
			qspi_set_spi_type(2);	//quad SPI   0 单线  1 双线  2 四线
		}
		
	}else if(rx){
		if(xfer->rx_nbits == 1){
			qspi_set_spi_type(0);	//quad SPI   0 单线  1 双线  2 四线
		}else{
			qspi_set_spi_type(2);	//quad SPI   0 单线  1 双线  2 四线
		}
	}
#endif
	do {
		if (qspi_write_read_8bit( &tx, &rx, count) < 0)
			goto out;
		count--;
	} while (count);

out:
	return xfer->len - count;
}


unsigned char tmpbuf[20];
int qspi_exec_mem_op( const struct spi_mem_op *op)
{
	struct spi_transfer t[2];
	struct spi_transfer xfers[4] = { };
	u8 cmd[6] = { };
	int ret, i;
	unsigned int tmpbufsize, xferpos = 0, totalxferlen = 0;
	tmpbufsize = sizeof(op->cmd.opcode) + op->addr.nbytes +
		     op->dummy.nbytes;

	/*
	 * Allocate a buffer to transmit the CMD, ADDR cycles with kmalloc() so
	 * we're guaranteed that this buffer is DMA-able, as required by the
	 * SPI layer.
	 */


	/*in here set cs*/
	init_qspi();
	set_qspi_cs();

	tmpbuf[0] = op->cmd.opcode;
	xfers[xferpos].tx_buf = tmpbuf;
	xfers[xferpos].len = sizeof(op->cmd.opcode);
	xfers[xferpos].tx_nbits = op->cmd.buswidth;
	if (op->addr.nbytes || op->dummy.nbytes || op->data.nbytes)
		xfers[xferpos].cs_change = 1;
	else
		xfers[xferpos].cs_change = 0;
		ls_qspi_write_read(&xfers[xferpos]);
	xferpos++;

	if (op->addr.nbytes) {
		int i;

		for (i = 0; i < op->addr.nbytes; i++)
			tmpbuf[i + 1] = op->addr.val >>
					(8 * (op->addr.nbytes - i - 1));

		xfers[xferpos].tx_buf = tmpbuf + 1;
		xfers[xferpos].len = op->addr.nbytes;
		xfers[xferpos].tx_nbits = op->addr.buswidth;
		if (op->dummy.nbytes || op->data.nbytes)
			xfers[xferpos].cs_change = 1;
		else
			xfers[xferpos].cs_change = 0;
			ls_qspi_write_read(&xfers[xferpos]);
		xferpos++;
	}

	if (op->dummy.nbytes) {
		memset(tmpbuf + op->addr.nbytes + 1, 0xff, op->dummy.nbytes);
		xfers[xferpos].tx_buf = tmpbuf + op->addr.nbytes + 1;
		xfers[xferpos].len = op->dummy.nbytes;
		xfers[xferpos].tx_nbits = op->dummy.buswidth;
		if (op->data.nbytes)
			xfers[xferpos].cs_change = 1;
		else
			xfers[xferpos].cs_change = 0;
			ls_qspi_write_read(&xfers[xferpos]);
		xferpos++;
	}

	if (op->data.nbytes) {
		if (op->data.dir == SPI_MEM_DATA_IN) {
			xfers[xferpos].rx_buf = op->data.buf.in;
			xfers[xferpos].rx_nbits = op->data.buswidth;
		} else {
			xfers[xferpos].tx_buf = op->data.buf.out;
			xfers[xferpos].tx_nbits = op->data.buswidth;
		}

		xfers[xferpos].len = op->data.nbytes;
		xfers[xferpos].cs_change = 0;
		ls_qspi_write_read(&xfers[xferpos]);
		xferpos++;
	}

	reset_qspi_cs();
	exit_qspi();

	return ret;
}







unsigned char scratch_buf[100];
unsigned char read_buf[2248];
unsigned char write_buf[2248];

static int spinand_wait()
{
	int time=0x10000;
 struct spi_mem_op op = SPINAND_GET_FEATURE_OP(0xc0, scratch_buf);
        u8 status;
        int ret;
	do{
		qspi_exec_mem_op(&op);
		status = scratch_buf[0];
	}while((status & 1) && time--);
              //	printf("status is %x\n",status);
                 return ret;
}

static int spinor_wait()
{
	int time=0x10000;
 struct spi_mem_op op = SPINAND_GET_FEATURE_OP(0xc0, scratch_buf);
        u8 status;
        int ret;
	do{
		qspi_exec_mem_op(&op);
		status = scratch_buf[0];
	}while((status & 1) && time--);
              //	printf("status is %x\n",status);
                 return ret;
}



void qspi_set_flash_type(int argc , char **argv)  //cys
{
	if(argc != 2){
		printf("input data type error!\n");
		printf("qspi_set_flash_type [flash_type]\n");
		return -1;
	}
	
	flash_type = strtoul(argv[1], 0, 0);
	
}


void qspi_init()
{
	init_qspi();
}

void qspi_clk_set(int argc , char **argv)  //cys
{
	if(argc != 2){
		printf("input data type error!\n");
		printf("qspi_clk_set [clk]\n");
		return -1;
	}
	unsigned char clk_div;
	unsigned char val;
	clk_div = strtoul(argv[1], 0, 0);
	
	val = GET_QSPI(QSPICSR);
	val &= 0xf;
	SET_QSPI(QSPICSR, clk_div<<4|val);
}


int qspi_freset()
{
	 struct spi_mem_op op = SPINAND_RESET_OP;	
		qspi_exec_mem_op(&op);
		spinand_wait();
		return 0;
}

int qspi_readid()
{
	int i=0;
 struct spi_mem_op op = SPINAND_READID_OP(0, scratch_buf,
                                                 4);

		qspi_exec_mem_op(&op);
		printf("flash id is:\n");
		for(i=0;i<4;i++)
			printf("%x ",scratch_buf[i]);
		printf("\n");
		//spinand_wait();
		return 0;
}
int qspi_qeenable()
{
	int i=0;
 struct spi_mem_op op = SPINAND_GET_FEATURE_OP(0xb0, scratch_buf);

		qspi_exec_mem_op(&op);
		scratch_buf[0] |= (1);//qeenable and otp en 	
 struct spi_mem_op op1 = SPINAND_SET_FEATURE_OP(0xb0,scratch_buf);		
		qspi_exec_mem_op(&op1);

		scratch_buf[0] = 0;//all unlock 	
 struct spi_mem_op op2 = SPINAND_SET_FEATURE_OP(0xa0,scratch_buf);		
		qspi_exec_mem_op(&op2);
		return 0;
}

static int qspi_get_status(int argc , char **argv)
{

	if(argc != 2){
		printf("input data type error!\n");
		printf("qspi_status [addr]\n");
		return -1;
	}
	unsigned char val;
	val = strtoul(argv[1], 0, 0);
 struct spi_mem_op op = SPINAND_GET_FEATURE_OP(val, scratch_buf);
        u8 status;
        int ret;
		qspi_exec_mem_op(&op);
		status = scratch_buf[0];
              	printf("status is %x\n",status);
                 return ret;
}
static int qspi_set_status(int argc , char **argv)
{

	if(argc != 3){
		printf("input data type error!\n");
		printf("qspi_status [addr] [val]\n");
		return -1;
	}
	unsigned char addr;
	unsigned char val;
        int ret;

	addr = strtoul(argv[1], 0, 0);
	val = strtoul(argv[2], 0, 0);
	scratch_buf[0]=val;
 struct spi_mem_op op = SPINAND_SET_FEATURE_OP(addr, scratch_buf);
	qspi_exec_mem_op(&op);
        return 0;
}



int qspi_fread(int argc , char **argv)  //cys
{
	if(argc != 2){
		printf("input data type error!\n");
		printf("qspi_fread [addr]\n");
		return -1;
	}
	unsigned int val;
	unsigned int i;
	val = strtoul(argv[1], 0, 0);
	struct spi_mem_op op = SPINAND_PAGE_READ_OP(val);

		qspi_exec_mem_op(&op);
		spinand_wait();
	struct spi_mem_op op1 = SPINAND_PAGE_READ_FROM_CACHE_QUADIO_OP(0, 2, NULL, 0);
	//struct spi_mem_op op1 = SPINAND_PAGE_READ_FROM_CACHE_X4_OP_3A(0, 1, NULL, 0);
	        op1.addr.val = val;
		op1.data.buf.in = read_buf;
                op1.data.nbytes = 2048;
		qspi_exec_mem_op(&op1);
		printf("read data:\n");
		for(i=0;i<2048;i++){
			printf("%x ",read_buf[i]);
			if(i%20 == 0)
				printf("\n");
		}
				printf("\n");
		spinand_wait();
		return 0;
}

int qspi_fwrite(int argc , char **argv)  //cys
{
	if(argc != 2){
		printf("input data type error!\n");
		printf("qspi_fwrite  	[addr]\n");
		return -1;
	}
	unsigned int val;
	unsigned int i;
	for(i=0;i<2048;i++)
		write_buf[i] = i;
	val = strtoul(argv[1], 0, 0);


	struct spi_mem_op op1= SPINAND_WR_EN_DIS_OP(true);

		qspi_exec_mem_op(&op1);
         struct spi_mem_op op2= SPINAND_PROG_LOAD_X4(true, 0, NULL, 0);
	         op2.addr.val = val;
		 op2.data.buf.out = write_buf;
                 op2.data.nbytes = 2048;
		qspi_exec_mem_op(&op2);

	   struct spi_mem_op op3 = SPINAND_PROG_EXEC_OP(val);
		qspi_exec_mem_op(&op3);
		spinand_wait();
		return 0;
}

int qspi_ferase(int argc , char **argv)  //cys
{
	if(argc != 2){
		printf("input data type error!\n");
		printf("qspi_ferase [addr]\n");
		return -1;
	}
	unsigned int val;
	val = strtoul(argv[1], 0, 0);
	printf("val is %x\n",val);
        struct spi_mem_op op = SPINAND_BLK_ERASE_OP(val);
	struct spi_mem_op op1= SPINAND_WR_EN_DIS_OP(true);

		qspi_exec_mem_op(&op1);
		qspi_exec_mem_op(&op);
		spinand_wait();
		return 0;
}

int qspi_read_test(int argc , char **argv)  //cys
{
	if(argc != 3){
		printf("input data type error!\n");
		printf("qspi_fread_test [addr] [num]\n");
		return -1;
	}
	unsigned int val;
	unsigned int i,num;
	val = strtoul(argv[1], 0, 0);
	num = strtoul(argv[2], 0, 0);
	for (i=0;i<num;i++){
	struct spi_mem_op op = SPINAND_PAGE_READ_OP(val);

		qspi_exec_mem_op(&op);
		spinand_wait();
	struct spi_mem_op op1 = SPINAND_PAGE_READ_FROM_CACHE_QUADIO_OP(0, 2, NULL, 0);
	//struct spi_mem_op op1 = SPINAND_PAGE_READ_FROM_CACHE_X4_OP_3A(0, 1, NULL, 0);
	        op1.addr.val = val;
		op1.data.buf.in = read_buf;
                op1.data.nbytes = 2048;
		qspi_exec_mem_op(&op1);
#if 0
		printf("read data:\n");
		for(i=0;i<2048;i++){
			printf("%x ",read_buf[i]);
			if(i%20 == 0)
				printf("\n");
		}
				printf("\n");
#endif
		spinand_wait();
		val +=0x800;
	}
		return 0;
}








#define prefetch(x) (x)

static struct ls_qspi {
	void	*base;
	int hz;
}  ls_qspi0 = {PHYS_TO_UNCACHED(QSPI_BASE)} ;

struct spi_device qspi_nand =
{
	.dev = &ls_qspi0,
	.chip_select = 0,
	.max_speed_hz = 5000000,
};

#define DIV_ROUND_UP(n,d)	(((n) + (d) - 1) / (d))

void spi_init_n()
{
}

void spi_init_r(void)
{
}

#ifdef QSPI_NAND
int qspinand_probe(struct spi_device *spi_nand);

int ls2k_qspi_nand_probe()
{
	init_qspi();
	#ifdef CONFIG_SPINAND_CS
	qspi_nand.chip_select = CONFIG_SPINAND_CS;
	#endif
	qspinand_probe(&qspi_nand);
	exit_qspi();
}

#endif
static const Cmd Cmds[] =
{
	{"MyCmds"},
	{"qspi_init", "", 0, "qspi_init", qspi_init, 0, 99, CMD_REPEAT},
	{"qspi_exit", "", 0, "qspi_init", exit_qspi, 0, 99, CMD_REPEAT},
	{"qspi_clk_set", "", 0, "qspi_clk_set", qspi_clk_set, 0, 99, CMD_REPEAT},
	{"qspi_qe", "", 0, "qspi_qe", qspi_qeenable, 0, 99, CMD_REPEAT},
	{"qspi_get_status", "", 0, "qspi_get_status", qspi_get_status, 0, 99, CMD_REPEAT},
	{"qspi_set_status", "", 0, "qspi_set_status", qspi_set_status, 0, 99, CMD_REPEAT},
	{"qspi_freset", "", 0, "qspi_freset", qspi_freset, 0, 99, CMD_REPEAT},
	{"qspi_ferase", "", 0, "qspi_ferase", qspi_ferase, 0, 99, CMD_REPEAT},
	{"qspi_fread", "", 0, "qspi_fread", qspi_fread, 0, 99, CMD_REPEAT},
	{"qspi_fwrite", "", 0, "qspi_fwrite", qspi_fwrite, 0, 99, CMD_REPEAT},
	{"qspi_readid", "", 0, "qspi_readid", qspi_readid, 0, 99, CMD_REPEAT},
	{"qspi_wait", "", 0, "qspi_wait", spinand_wait, 0, 99, CMD_REPEAT},
	{"qspi_read_test", "", 0, "qspi_read_test", qspi_read_test, 0, 99, CMD_REPEAT},
	{"flash_type_set", "", 0, "flash_type_set", qspi_set_flash_type, 0, 99, CMD_REPEAT},

	{0, 0}
};

static void init_cmd __P((void)) __attribute__ ((constructor));

	static void
init_cmd()
{
	cmdlist_expand(Cmds, 1);
}

