#include <pmon.h>
#include <stdlib.h>
#include <stdio.h>
#include <ctype.h>
#include <unistd.h>
#include <string.h>
#include <termio.h>
#include <fcntl.h>
#include <file.h>
#include <ctype.h>
#include <ramfile.h>
#include <sys/unistd.h>
#include <stdlib.h>
#include <errno.h>
#include <linux/types.h>

#define 	LS2P500_MAILBOX_BASE 		(PHYS_TO_UNCACHED(0X15103800))
void mailbox_w32(unsigned int data, volatile unsigned int *addr)
{
	*addr = data;
}
unsigned int mailbox_r32(volatile unsigned int *addr)
{
	return (*addr);
}


static int mailbox_for_print(int argc, char **argv)
{
	unsigned int bpl=0;
	unsigned int vact=0;
	unsigned int paper_type=0;
	unsigned int mode=0;
	unsigned int page_num=0;
	unsigned int band_num=0;
	unsigned int last_page_enable=0;
	unsigned int page_band=0;
	unsigned int paperform=0;
	if(argc<7)
	{
		printf("mailbox (640(a4)/640(a5h)/448(a5z)/544(b5)/576(16k)) (0(a4)/1(a5h)/2(b5)/3(16k)/4(a5z)) (0:singal 1:dual) (0:box 1:hand) (page_num) (last_page_enable(1)) \n");
		return 0;
	}
	bpl=strtoul(argv[1],0,0);
	paper_type=strtoul(argv[2],0,0);
	mode=strtoul(argv[3],0,0);
	paperform=strtoul(argv[4],0,0);
	page_num=strtoul(argv[5],0,0);
	last_page_enable=strtoul(argv[6],0,0);
	printf("bpl=%d\n",bpl);
	printf("paper_type=%d\n",paper_type);
	printf("mode=%d\n",mode);
	printf("paperform=%d\n",paperform);
	printf("page_num=%d\n",page_num);
	printf("last_page_enable=%d\n",last_page_enable);
	if(page_num)
	{
		/************************print_conf************************************/
		mailbox_w32(0xAA810107,(void*)LS2P500_MAILBOX_BASE+0x40);	//head:0xaa type:0x81 core:0x01 data_num:0x06
		mailbox_w32(600,(void*)LS2P500_MAILBOX_BASE+0x44);			//dpi
		mailbox_w32(bpl*8,(void*)LS2P500_MAILBOX_BASE+0x48);		//hact
		mailbox_w32(1,(void*)LS2P500_MAILBOX_BASE+0x4c);			//om
		mailbox_w32(1,(void*)LS2P500_MAILBOX_BASE+0x50);			//colorturn
		mailbox_w32(128,(void*)LS2P500_MAILBOX_BASE+0x54);			//lpb
		mailbox_w32(0,(void*)LS2P500_MAILBOX_BASE+0x58);			//clone
		switch(paper_type)
		{
		case 0://a4
			vact=7040;
			break;
		case 1://a5h
			vact=3584;
			break;
		case 2://b5
			vact=6144;
			break;
		case 3://16k
			vact=6400;
			break;
		case 4://a5z
			vact=4992;
		default:
			printf("unknowen paper_type\n");
		}
		mailbox_w32(vact,(void*)LS2P500_MAILBOX_BASE+0x5c);			//vact
		mailbox_w32(1,(void*)LS2P500_MAILBOX_BASE+0x38);			//mailbox_start_send
		while(mailbox_r32((void*)LS2P500_MAILBOX_BASE+0x30));

		page_band=vact/128;

		/***************************print_mode*********************************/	
		mailbox_w32(0xAA820103,(void*)LS2P500_MAILBOX_BASE+0x40);	//head:0xaa type:0x81 core:0x01 data_num:0x06
		mailbox_w32(paper_type,(void*)LS2P500_MAILBOX_BASE+0x44);	//paper_type
		mailbox_w32(mode,(void*)LS2P500_MAILBOX_BASE+0x48);			//mode
		mailbox_w32(paperform,(void*)LS2P500_MAILBOX_BASE+0x4c);			//paperform
		mailbox_w32(1,(void*)LS2P500_MAILBOX_BASE+0x38);			//mailbox_start_send
		while(mailbox_r32((void*)LS2P500_MAILBOX_BASE+0x30));
		/************************************************************/
		for(int j=0;j<page_num;j++)
		{
			printf("j=%d\n",j);
			for(int i=0;i<page_band;i++)
			{
				printf("i=%d\n",i);
				mailbox_w32(0xAA800104,(void*)LS2P500_MAILBOX_BASE+0x40);
				mailbox_w32(0xb0000000+(i*bpl*128),(void*)LS2P500_MAILBOX_BASE+0x44); 	//addr
				mailbox_w32(i,(void*)LS2P500_MAILBOX_BASE+0x48);			//band_num
				if(i==0){
					mailbox_w32(1,(void*)LS2P500_MAILBOX_BASE+0x4c);			//first_band
				}else if(i==(page_band-1)){
					mailbox_w32(2,(void*)LS2P500_MAILBOX_BASE+0x4c);			//last_band
				}else{
					mailbox_w32(0,(void*)LS2P500_MAILBOX_BASE+0x4c);			//mid_band
				}
				if(last_page_enable)
				{
					if(j==page_num-1){
						mailbox_w32(1,(void*)LS2P500_MAILBOX_BASE+0x50); 			//last_page
					}else{
						mailbox_w32(0,(void*)LS2P500_MAILBOX_BASE+0x50); 			//not last_page
					}
				}
				else
				{
					mailbox_w32(0,(void*)LS2P500_MAILBOX_BASE+0x50); 			//not last_page
				}
				mailbox_w32(1,(void*)LS2P500_MAILBOX_BASE+0x38); 
				while(mailbox_r32((void*)LS2P500_MAILBOX_BASE+0x30));
			}
		}
	}
	return 0;
}


static const Cmd Cmds[] =
{
	{"MAILBOX"},
	{"mailbox","page_num",0,"mailbox page_num", mailbox_for_print,0,99,CMD_REPEAT},
	{0, 0}
};
static void init_cmd __P((void)) __attribute__ ((constructor));
static void init_cmd()
{
	cmdlist_expand(Cmds, 1);
}
