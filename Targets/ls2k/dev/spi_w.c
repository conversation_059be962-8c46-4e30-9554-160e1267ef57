#include <stdio.h>
//#include "include/fcr.h"
#include <stdlib.h>
#include <ctype.h>
#undef _KERNEL
#include <errno.h>
#include <pmon.h>
//#include <types.h>
#include <pflash.h>
#if   defined(LOONGARCH_2K500)
#include <target/ls2k500.h>
#elif defined(LOONGARCH_2K1000)
#include <target/ls2k1000.h>
#elif   defined(LOONGARCH_2P500)
#include <target/ls2p500.h>
#elif   defined(LOONGARCH_2P300)
#include <target/ls2p300.h>
#endif
#include <linux/spi.h>
#include <linux/bitops.h>
#include "m25p80.h"

unsigned char qspi_flags=0;
#if   defined(LOONGARCH_2K500)
#define LS2K_SPI0_BASE	LS2K500_SPI0_BASE
#define LS2K_SPI1_BASE	LS2K500_SPI1_BASE
#define LS2K_SPI2_BASE	LS2K500_SPI2_BASE
#elif defined(LOONGARCH_2K1000)
#define LS2K_SPI0_BASE	LS2K1000_SPI0_BASE
#define LS2K_SPI1_BASE	LS2K1000_SPI1_BASE
#define LS2K_SPI2_BASE	LS2K1000_SPI2_BASE
#elif   defined(LOONGARCH_2P500)
#define LS2K_SPI0_BASE	LS2P500_SPI0_BASE
#define LS2K_SPI1_BASE	LS2P500_SPI1_BASE
#define LS2K_SPI2_BASE	LS2P500_SPI1_BASE
#elif   defined(LOONGARCH_2P300)
#define LS2K_SPI0_BASE	LS2P300_SPI0_BASE
#define LS2K_SPI1_BASE	LS2P300_SPI1_BASE
#define LS2K_SPI2_BASE	LS2P300_SPI2_BASE
#endif

char* ls_spi_base = LS2K_SPI0_BASE;		//ls2k spi2~5 is apb mode
unsigned char mac_read_spi_buf[22] = {0};
#define SPCR      0x0
#define SPSR      0x1
#define TXFIFO    0x2
#define RXFIFO    0x2
#define SPER      0x3
#define PARAM     0x4
#define SOFTCS    0x5
#define PARAM2    0x6
#define APB_SPCS  0x4

#define RFEMPTY 1

#define SET_SPI(addr,val)	writeb(val, ls_spi_base + addr)
#define GET_SPI(addr)		readb(ls_spi_base + addr)

#define SET_CE(x)									\
do {											\
	if (ls_spi_base == LS2K_SPI0_BASE || ls_spi_base == LS2K_SPI1_BASE) {	\
		SET_SPI(SOFTCS, x);							\
	} else {									\
		SET_SPI(APB_SPCS, (x >> 4) | (!(x & 1) << 1));				\
	}										\
} while(0)

void spi_initw(void)
{
	if(qspi_flags){
		printf("spi can not use 1\n");	
		return 0;
	}
	SET_SPI(SPSR, 0xc0);
	if (ls_spi_base == LS2K_SPI0_BASE || ls_spi_base == LS2K_SPI1_BASE) {
		SET_SPI(PARAM, 0x20);//espr:0100
		SET_SPI(PARAM2,0x01);
	}
	SET_SPI(SPER, 0x05);//spre:01
	SET_SPI(SPCR, 0x50);
}

void spi_initr(void)
{
	if(qspi_flags){
		printf("spi can not use 1\n");	
		return 0;
	}
	if (ls_spi_base == LS2K_SPI0_BASE || ls_spi_base == LS2K_SPI1_BASE) {
		SET_SPI(PARAM, 0x27);	     //espr:0100
	}
}

int send_spi_cmd(unsigned char command)
{
	if(qspi_flags){
		printf("spi can not use 1\n");	
		return 0;
	}
	int timeout = 1000;
	unsigned char val;

	SET_SPI(TXFIFO,command);
	while(((GET_SPI(SPSR)) & RFEMPTY) && timeout--);
	val = GET_SPI(RXFIFO);

	if (timeout < 0) {
		printf("wait rfempty timeout!\n");
		return -1;
	}
	return val;
}

///////////////////read status reg /////////////////
int read_sr(void)
{
	if(qspi_flags){
		printf("spi can not use 1\n");	
		return 0;
	}
	int val;

	SET_CE(0x01);
	send_spi_cmd(0x05);
	val = send_spi_cmd(0x00);
	SET_CE(0x11);

	return val;
}

int spi_wait_busy(void)
{
	if(qspi_flags){
		printf("spi can not use 1\n");	
		return 0;
	}
	int timeout = 1000;
	unsigned char res;

	do {
		res = read_sr();
	} while ((res & 0x01) && timeout--);

	if (timeout < 0) {
		printf("wait status register busy bit time out!\n");
		return -1;
	}
	return 0;
}

////////////set write enable//////////
int set_wren(void)
{
	if(qspi_flags){
		printf("spi can not use 1\n");	
		return 0;
	}
	if (spi_wait_busy() < 0) {
		return -1;
	}

	SET_CE(0x01);
	send_spi_cmd(0x6);
	SET_CE(0x11);

	return 1;
}

////////////Enable-Write-Status-Register//////////
int en_write_sr(void)
{
	if(qspi_flags){
		printf("spi can not use 1\n");	
		return 0;
	}
	if (spi_wait_busy() < 0)
		return -1;

	SET_CE(0x01);
	send_spi_cmd(0x50);
	SET_CE(0x11);

	return 1;
}

///////////////////////write status reg///////////////////////
int write_sr(char val)
{
	if(qspi_flags){
		printf("spi can not use 1\n");	
		return 0;
	}
	/*this command do'nt used to check busy bit otherwise cause error*/
	en_write_sr();

	SET_CE(0x01);
	send_spi_cmd(0x01);
	/*set status register*/
	send_spi_cmd(val);
	SET_CE(0x11);

	return 1;
}

///////////erase all memory/////////////
int erase_all(void)
{
	if(qspi_flags){
		printf("spi can not use ............1\n");	
		return 0;
	}
	int i=1;
	spi_initw();
	set_wren();
	if (spi_wait_busy() < 0)
		return -1;

	SET_CE(0x01);
	send_spi_cmd(0xc7);
	SET_CE(0x11);
	while(i++) {
		if(read_sr() & 0x1) {
			if(i % 10000 == 0)
				printf(".");
		} else {
			printf("done...\n");
			break;
		}
	}
	return 1;
}

void spi_read_id(void)
{
	if(qspi_flags){
		printf("spi can not use i.,,,,,,,,,,,,,,,,,1\n");	
		return 0;
	}
	unsigned char val;
	char i;

	spi_initw();
	if (spi_wait_busy() < 0)
		return;

	/*CE 0*/
	SET_CE(0x01);
	/*READ ID CMD*/
	send_spi_cmd(0x90);

	/*address bit [23-0]*/
	for (i = 0;i < 3;i++) {
		send_spi_cmd(0);
	}

	/*Manufacturer’s ID*/
	val = send_spi_cmd(0);
	printf("Manufacturer's ID:         %x\n",val);
	/*Device ID*/
	val = send_spi_cmd(0);
	printf("Device ID:                 %x\n",val);
	/*CE 1*/
	SET_CE(0x11);
}

void spi_jedec_id(void)
{
	if(qspi_flags){
		printf("spi can not use ................,,,,,,,,,,,1\n");	
		return 0;
	}
	unsigned char val;
	spi_initw();

	if (spi_wait_busy() < 0)
		return;
	/*CE 0*/
	SET_CE(0x01);
	/*JEDEC ID CMD*/
	send_spi_cmd(0x9f);

	/*Manufacturer’s ID*/
	val = send_spi_cmd(0x00);
	printf("Manufacturer's ID:         %x\n",val);

	/*Device ID:Memory Type*/
	val = send_spi_cmd(0x00);
 	printf("Device ID-memory_type:     %x\n",val);

	/*Device ID:Memory Capacity*/
	val = send_spi_cmd(0x00);
	printf("Device ID-memory_capacity: %x\n",val);

	/*CE 1*/
	SET_CE(0x11);
}

void spi_write_byte(unsigned int addr,unsigned char data)
{
	if(qspi_flags){
		printf("spi can not use ...........1\n");	
		return 0;
	}
	write_sr(0x0);
	set_wren();
	if (spi_wait_busy() < 0)
		return;

	SET_CE(0x01);/*CE 0*/

	send_spi_cmd(0x2);

	/*send addr [23 16]*/
	send_spi_cmd((addr >> 16) & 0xff);
	/*send addr [15 8]*/
	send_spi_cmd((addr >> 8) & 0xff);
	/*send addr [8 0]*/
	send_spi_cmd(addr & 0xff);

	/*send data(one byte)*/
	send_spi_cmd(data);

	/*CE 1*/
	SET_CE(0x11);
}

int spi_write_page(unsigned int addr, char *buffer, unsigned len)
{
	int i;

	len = len > 256 ? 256 : len;
	len = len - addr % 256;

	write_sr(0x0);
	set_wren();

	if (spi_wait_busy() < 0)
		return;

	SET_CE(0x01);/*CE 0*/

	send_spi_cmd(0x2);

	/*send addr [23 16]*/
	send_spi_cmd((addr >> 16) & 0xff);
	/*send addr [15 8]*/
	send_spi_cmd((addr >> 8) & 0xff);
	/*send addr [8 0]*/
	send_spi_cmd(addr & 0xff);

	for (i = 0; i < len; i++)
		send_spi_cmd(buffer[i]);

	/*CE 1*/
	SET_CE(0x11);

	return len;
}

int write_pmon_byte(int argc,char ** argv)
{
	if(qspi_flags){
		printf("spi can not use ...........1\n");	
		return 0;
	}
	unsigned int addr;
	unsigned char val;
	if(argc != 3){
		printf("\nuse: write_pmon_byte  dst(flash addr) data\n");
		return -1;
	}
	addr = strtoul(argv[1],0,0);
	val = strtoul(argv[2],0,0);
	spi_write_byte(addr,val);
	return 0;
}

int write_pmon(int argc,char **argv)
{
	if(qspi_flags){
		printf("spi can not use ..........1\n");	
		return 0;
	}
	long int j=0;
	unsigned char val;
	unsigned int ramaddr,flashaddr,size;
	if(argc != 4){
		printf("\nuse: write_pmon src(ram addr) dst(flash addr) size\n");
		return -1;
	}

	ramaddr = strtoul(argv[1],0,0);
	flashaddr = strtoul(argv[2],0,0);
	size = strtoul(argv[3],0,0);

	spi_initw();
	write_sr(0);
	//read flash id command
	spi_read_id();
	val = GET_SPI(SPSR);
	printf("====spsr value:%x\n",val);

	SET_SPI(0x5,0x10);
	//erase the flash
	write_sr(0x00);
	//erase_all();
	printf("\nfrom ram 0x%08x  to flash 0x%08x size 0x%08x \n\nprogramming	    ",ramaddr,flashaddr,size);
	for(j=0;size > 0;flashaddr++,ramaddr++,size--,j++)
	{
		spi_write_byte(flashaddr,*((unsigned char*)ramaddr));
		if(j % 0x1000 == 0)
		printf("\b\b\b\b\b\b\b\b\b\b0x%08x",j);
	}
	printf("\b\b\b\b\b\b\b\b\b\b0x%08x end...\n",j);

	SET_CE(0x11);
	return 1;
}

int read_pmon_byte(unsigned int addr)
{
	if(qspi_flags){
		printf("spi can not use ..........1\n");	
		return 0;
	}
	unsigned char data;

	spi_wait_busy();
	SET_CE(0x01);
	// read flash command
	send_spi_cmd(0x03);
	/*send addr [23 16]*/
	send_spi_cmd((addr >> 16) & 0xff);
	/*send addr [15 8]*/
	send_spi_cmd((addr >> 8) & 0xff);
	/*send addr [8 0]*/
	send_spi_cmd(addr & 0xff);

	data = send_spi_cmd(0x00);
	SET_CE(0x11);
	return data;
}

int read_pmon(int argc,char **argv)
{
	if(qspi_flags){
		printf("spi can not use .........1\n");	
		return 0;
	}
	unsigned char data;
	int base = 0;
	int addr;
	int i;
	if(argc != 3) {
		printf("\nuse: read_pmon addr(flash) size\n");
		return -1;
	}
	addr = strtoul(argv[1],0,0);
	i = strtoul(argv[2],0,0);
	spi_initw();

	if (spi_wait_busy() < 0) {
		return -1;
	}
	/*CE 0*/
	SET_CE(0x01);
	// read flash command
	send_spi_cmd(0x3);
	/*send addr [23 16]*/
	send_spi_cmd((addr >> 16) & 0xff);
	/*send addr [15 8]*/
	send_spi_cmd((addr >> 8) & 0xff);
	/*send addr [8 0]*/
	send_spi_cmd(addr & 0xff);

	printf("\n");
	while(i--) {
		data = send_spi_cmd(0x0);
		if(base % 16 == 0 ){
			printf("0x%08x    ",base);
		}
		printf("%02x ",data);
		if(base % 16 == 7)
			printf("  ");
		if(base % 16 == 15)
			printf("\n");
		base++;
	}
	printf("\n");
	return 1;
}

int spi_erase_area(unsigned int saddr,unsigned int eaddr,unsigned sectorsize)
{
	if(qspi_flags){
		printf("spi can not use ........1\n");	
		return 0;
	}
	unsigned int addr;
	spi_initw();

	for(addr=saddr;addr<eaddr;addr+=sectorsize) {
		SET_CE(0x11);

		set_wren();
		write_sr(0x00);
		while(read_sr()&1);
		set_wren();
		SET_CE(0x01);
		/*
		 * 0x20 erase 4kbyte of memory array
		 * 0x52 erase 32kbyte of memory array
		 * 0xd8 erase 64kbyte of memory array
		 */
		send_spi_cmd(0x20);

		/*send addr [23 16]*/
		send_spi_cmd((addr >> 16) & 0xff);
		/*send addr [15 8]*/
		send_spi_cmd((addr >> 8) & 0xff);
		/*send addr [8 0]*/
		send_spi_cmd(addr & 0xff);
		SET_CE(0x11);

		while(read_sr()&1);
	}
	SET_CE(0x11);
	delay(10);

	return 0;
}

int spi_write_area(int flashaddr,char *buffer,int size)
{
	if(qspi_flags){
		printf("spi can not use .......1\n");	
		return 0;
	}
	int j;
	int len;

	spi_initw();
	SET_CE(0x01);
	write_sr(0x00);

	for(j = 0; size > 0; flashaddr += len, size -= len, j += len) {
		len = spi_write_page(flashaddr, &buffer[j], size);
		dotik(32, 0);
	}

	SET_CE(0x11);
	while(read_sr() & 1);
	SET_CE(0x11);
	delay(10);
	return 0;
}


int spi_read_area(int addr,char *buffer,int size)
{
	if(qspi_flags){
		printf("spi can not use .....1\n");	
		return 0;
	}
	int i;
	spi_initw();

	SET_CE(0x01);

	send_spi_cmd(0x3);

	/*send addr [23 16]*/
	send_spi_cmd((addr >> 16) & 0xff);
	/*send addr [15 8]*/
	send_spi_cmd((addr >> 8) & 0xff);
	/*send addr [8 0]*/
	send_spi_cmd(addr & 0xff);

	for(i = 0;i < size; i++) {
		buffer[i] = send_spi_cmd(0x0);
	}

	SET_CE(0x11);
	delay(10);
	return 0;
}
void spi_read_mac(unsigned char * inbuf, int num) {

	unsigned char * buf;
	int i, v, j;
	if(qspi_flags){
		printf("spi can not use ....1\n");	
		return 0;
	}
	if (num == 0) {
		spi_initw();
#ifdef  MAC_ADDR
		spi_read_area(MAC_ADDR, inbuf, 22);
#else
		spi_read_area(0xf0000, inbuf, 22);
#endif
	}
	if (num == 0) {
		buf = inbuf;
	} else if(num == 1) {
		buf = inbuf + 16;
	}
	if (!is_valid_ether_addr_linux(buf)) {
		printf("syn%d Mac is invalid, now get a new mac\n", num);
		generate_mac_val(buf);
		printf("set syn%d  Mac address: ",num);
		for (v = 0; v < 6; v++)
			printf("%2x%s", *(buf + v) & 0xff, (5 - v) ? ":" : " ");
		printf("\n");
		printf("syn%d Mac is invalid, please use set_mac to update spi mac address\n", num);
	} else {
		printf("syn%d Mac address: ", num);
		for (j = 0; j < 6; j++)
			printf("%02x%s", buf[j], (5 - j) ? ":" : " ");
		printf("\n");
	}
	spi_initr();
}
int cmd_set_mac(int ac, unsigned char *av[])
{
	if(qspi_flags){
		printf("spi can not use ...1\n");	
		return 0;
	}
        int i, j, v, count, num, param = 0;
        unsigned char *s = NULL;
        unsigned int data_addr;
        unsigned char  buf[32] = {0};

	if (av[2]) s = av[2];
        else goto warning;

        count = strlen(s) / 3 + 1;
        if (count - 6)
                goto warning;

        for (i = 0; i < count; i++) {
                gethex(&v, s, 2);
                buf[i] = v;
                s += 3;
        }

        spi_initw();

#ifdef  MAC_ADDR
        spi_read_area(MAC_ADDR, mac_read_spi_buf, 22);
#else
	spi_read_area(0xf0000, mac_read_spi_buf, 22);
#endif
        data_addr = strtoul(av[1] + 3, NULL, 0);

        if (data_addr == 0)
                memcpy(mac_read_spi_buf, buf, 6);
        else if (data_addr == 1)
                memcpy((mac_read_spi_buf + 16), buf, 6);

#ifdef  MAC_ADDR
        spi_erase_area(MAC_ADDR,MAC_ADDR+0x1000, 0x1000);
        spi_write_area(MAC_ADDR, mac_read_spi_buf, 22);
#else
	spi_erase_area(0xf0000,0xf1000, 0x1000);
	spi_write_area(0xf0000, mac_read_spi_buf, 22);
#endif
	printf("set syn%d  Mac address: %s\n",data_addr / 6, av[2]);
        printf("The machine should be restarted to make the mac change to take effect!!\n");
        return 0;


warning:
        printf("Please accord to correct format.\nFor example:\n");
        printf("\tsetmac syn1 \"00:11:22:33:44:55\"\n");
        printf("\tThis means set syn1's Mac address 00:11:22:33:44:55\n");
        return 0;
}

struct fl_device myflash = {
	.fl_name="spiflash",
	.fl_size=0x100000,
	.fl_secsize=0x1000,
};

struct fl_device *spi_fl_devident(void *base, struct fl_map **m)
{
	if(m)
	*m = fl_find_map(base);
	return &myflash;
}

int spi_fl_program_device(void *fl_base, void *data_base, int data_size, int verbose)
{
	if(qspi_flags){
		printf("spi can not use ..1\n");	
		return 0;
	}
	struct fl_map *map;
	int off;
	map = fl_find_map(fl_base);
	off = (int)(fl_base - map->fl_map_base) + map->fl_map_offset;
	spi_write_area(off,data_base,data_size);
	spi_initr();
	return 0;
}


int spi_fl_erase_device(void *fl_base, int size, int verbose)
{
	if(qspi_flags){
		printf("spi can not use .1\n");	
		return 0;
	}
	struct fl_map *map;
	int off;
	map = fl_find_map(fl_base);
	off = (int)(fl_base - map->fl_map_base) + map->fl_map_offset;
	spi_erase_area(off,off+size, myflash.fl_secsize);
	spi_initr();
	return 0;
}

int selected_lpc_spi(void *base)
{
#if defined(LOONGARCH_2K500)
//	printf("base[0x%lx], spi[0x%lx],lio[0x%lx],lpc[0x%lx],bonito[0x%lx],sel[0x%x]\n", base, \
			SPI_MEM_BASE, LIO_MEM_BASE0, LPC_MEM_BASE, BONITO_FLASH_BASE, readl(LS2K500_SAMPLE_CFG0) & 0x7);
	if ((unsigned long)base >= SPI_MEM_BASE && (unsigned long)base < (SPI_MEM_BASE + SPI_MEM_SIZE)) {
		printf("flash select: spi\n");
		return 1;
	} else if ((unsigned long)base >= LIO_MEM_BASE0 && (unsigned long)base < (LIO_MEM_BASE0 + LIO_MEM_SIZE * 2)) {
		printf("flash select: lio\n");
		loop_set_pin(100, 142, 2, 0);
		readl(LS2K500_GENERAL_CFG1) |= (1 << 28) | (8 << 23);
	} else if ((unsigned long)base >= LPC_FIRMWARE_MEM_BASE && (unsigned long)base < (LPC_FIRMWARE_MEM_BASE + LPC_MEM_SIZE)) {
		printf("flash select: lpc\n");
		loop_set_pin(70, 75, 5, 0);
		readl(LS2K500_LPC_CFG1_REG) |= (1 << 31);	// Access Firmware Memory
		readl(LS2K500_GENERAL_CFG2) &= ~(1 << 5);	// Select 4M LPC flash
	} else if ((unsigned long)base >= BONITO_FLASH_BASE && (unsigned long)base < (BONITO_FLASH_BASE + BONITO_FLASH_SIZE)) {
		int boot_sel = readl(LS2K500_SAMPLE_CFG0) & 0x7;
		if ((boot_sel & 3) == 0) {	//spi
			printf("flash select: spi\n");
			return 1;
		} else if (boot_sel == 3) {	//lio
			printf("flash select: lio\n");
			loop_set_pin(100, 142, 2, 0);
			readl(LS2K500_GENERAL_CFG1) |= (1 << 28) | (8 << 23);
		} else if ((boot_sel & 3) == 1) {	//lpc
			printf("flash select: lpc\n");
			loop_set_pin(70, 75, 5, 0);
			readl(LS2K500_LPC_CFG1_REG) |= (1 << 31);	// Access Firmware Memory
			readl(LS2K500_GENERAL_CFG2) &= ~(1 << 5);	// Select 4M LPC flash
		} else {
			printf("boot sel[0x%x], is not flash?\n", boot_sel);
		}
	} else {
		printf("err flash base:0x%lx\n", base);
	}
	return 0;
#elif defined(LOONGARCH_2K1000)
	return 1;
#endif
}

int set_spi_base(int ac, unsigned char *av[])
{
	uint32_t bus_num;
	char dev_name[5] = "spi0";
	if(ac != 2) {
		printf("spi_base <spi bus num: 0/1/2...>\n");
		return 0;
	}
	bus_num = (uint32_t)strtoul(av[1], NULL, 0);
	dev_name[3] += bus_num;
	if (bus_num < 0 || bus_num > 5) {
		printf("error spi bus num: %d\n", bus_num);
		return 0;
	} else if (bus_num & 0x6) {
		ls_spi_base = LS2K_SPI2_BASE + (bus_num - 2) * 0x1000;
		printf("Note: now you select spi2~5 that has no spi memory space，just ignore the verify error after loading flash");
	} else {
		ls_spi_base = LS2K_SPI0_BASE + bus_num * 0x40000;
	}
#if defined(LOONGARCH_2K500)
	if (bus_num == 0) {
		loop_set_pin(40, 43, 5, 0);
		loop_set_pin(64, 66, 3, 0);
	} else if (bus_num == 1) {
		loop_set_pin(44, 47, 5, 0);
		loop_set_pin(67, 69, 3, 0);
	} else if (bus_num == 2)
		loop_set_pin(113, 116, 4, 0);
	else if (bus_num == 3)
		loop_set_pin(32, 35, 3, 0);
	else if (bus_num == 4)
		loop_set_pin(24, 27, 3, 0);
	else if (bus_num == 5)
		loop_set_pin(28, 31, 3, 0);
#endif
	return 0;
}
#if defined(LOONGARCH_2K500) || defined(LOONGARCH_2P500)
#define prefetch(x) (x)

#define	LS2K500_SPI0_BASE	LS2P500_SPI0_BASE
#define	LS2K500_SPI1_BASE	LS2P500_SPI1_BASE


static struct ls2k500_spi {
	void	*base;
	int hz;
}  ls2k500_spi0 = {LS2P500_SPI0_BASE} ;

struct spi_device spi_nand =
{
	.dev = &ls2k500_spi0,
	.chip_select = 0,
	.max_speed_hz = 12500000,
};

static char ls2k500_spi_write_reg(struct ls2k500_spi *spi,
				unsigned char reg, unsigned char data)
{
	readb(spi->base +reg) = data;
}

static char ls2k500_spi_read_reg(struct ls2k500_spi *spi,
				unsigned char reg)
{
	return readb(spi->base + reg);
}


static int
ls2k500_spi_write_read_8bit(struct spi_device *spi, const u8 **tx_buf, u8 **rx_buf, unsigned int num)
{
	struct ls2k500_spi *ls2k500_spi = spi->dev;
	unsigned char value;
	int i, ret;

	for(i = 0; i < 4 && i < num; i++) {
		if (tx_buf && *tx_buf)
			value = *((*tx_buf)++);
		else
			value = 0;
		ls2k500_spi_write_reg(ls2k500_spi, TXFIFO, value);
	}

	ret = i;

	for(; i > 0; i--) {
		while ((ls2k500_spi_read_reg(ls2k500_spi, SPSR) & 0x1) == 1);
		value = ls2k500_spi_read_reg(ls2k500_spi, RXFIFO);
		if (rx_buf && *rx_buf)
			*(*rx_buf)++ = value;
	}

	return ret;
}

static unsigned int ls2k500_spi_write_read(struct spi_device *spi, struct spi_transfer *xfer)
{
	struct ls2k500_spi *ls2k500_spi;
	unsigned int count;
	int ret;
	const u8 *tx = xfer->tx_buf;
	u8 *rx = xfer->rx_buf;

	ls2k500_spi = spi->dev;
	count = xfer->len;

	do {
		if ((ret = ls2k500_spi_write_read_8bit(spi, &tx, &rx, count)) < 0)
			goto out;
		count -= ret;
	} while (count);
out:
	return xfer->len - count;
	//return count;
}

#define DIV_ROUND_UP(n,d)	(((n) + (d) - 1) / (d))
static int ls_spi_setup(struct ls2k500_spi *ls2k500_spi,  struct spi_device *spi)
{
	unsigned int hz;
	unsigned int div, div_tmp;
	unsigned int bit;
	unsigned long clk;
	unsigned char val, spcr, sper;
	const char rdiv[12]= {0,1,4,2,3,5,6,7,8,9,10,11};

	hz  = spi->max_speed_hz;

	if (hz) {
		clk = 100000000;
		div = DIV_ROUND_UP(clk, hz);

		if (div < 2)
			div = 2;

		if (div > 4096)
			div = 4096;

		bit = fls(div) - 1;
		if ((1<<bit) == div) bit--;
		div_tmp = rdiv[bit];
		ls2k500_spi->hz = hz;
		spcr = div_tmp & 3;
		sper = (div_tmp >> 2) & 3;

		val = ls2k500_spi_read_reg(ls2k500_spi, SPCR);
		ls2k500_spi_write_reg(ls2k500_spi, SPCR, (val & ~3) | spcr);
		val = ls2k500_spi_read_reg(ls2k500_spi, SPER);
		ls2k500_spi_write_reg(ls2k500_spi, SPER, (val & ~3) | sper);
	}
	return 0;
}

int spi_sync(struct spi_device *spi, struct spi_message *m)
{
	struct ls2k500_spi *ls2k500_spi = &ls2k500_spi0;
	struct spi_transfer *t = NULL;
	unsigned long flags;
	int cs;
	int param;
	if(qspi_flags){
		printf("spi sync can not use\n");	
		return 0;
	}
	ls_spi_setup(ls2k500_spi, spi);

	m->actual_length = 0;
	m->status = 0;

	if (list_empty(&m->transfers) /*|| !m->complete*/)
		return -EINVAL;


	list_for_each_entry(t, &m->transfers, transfer_list) {
		if (t->tx_buf == NULL && t->rx_buf == NULL && t->len) {
			printf("message rejected : "
				"invalid transfer data buffers\n");
			goto msg_rejected;
		}
	/*other things not check*/
	}

	if ((unsigned long)(ls2k500_spi->base) == LS2K500_SPI0_BASE ||	\
			(unsigned long)(ls2k500_spi->base) == LS2K500_SPI1_BASE) {
		param = ls2k500_spi_read_reg(ls2k500_spi, PARAM);
		ls2k500_spi_write_reg(ls2k500_spi, PARAM, param&~1);
	}

	cs = 0xff & ~(0x11<<spi->chip_select);
	if ((unsigned long)(ls2k500_spi->base) != LS2K500_SPI0_BASE && 	\
			(unsigned long)(ls2k500_spi->base) != LS2K500_SPI1_BASE) {
		ls2k500_spi_write_reg(ls2k500_spi, APB_SPCS,0x0);
	} else {
		ls2k500_spi_write_reg(ls2k500_spi, SOFTCS, (0x1 << spi->chip_select)|cs);
	}
	list_for_each_entry(t, &m->transfers, transfer_list) {
		if (t->len)
			m->actual_length += ls2k500_spi_write_read(spi, t);
	}

	if ((unsigned long)(ls2k500_spi->base) != LS2K500_SPI0_BASE && 	\
			(unsigned long)(ls2k500_spi->base) != LS2K500_SPI1_BASE) {
		ls2k500_spi_write_reg(ls2k500_spi, APB_SPCS,0x1);
	} else {
		ls2k500_spi_write_reg(ls2k500_spi, SOFTCS, (0x11<<spi->chip_select)|cs);
		ls2k500_spi_write_reg(ls2k500_spi, PARAM, param);
	}
	return 0;
msg_rejected:

	m->status = -EINVAL;
	if (m->complete)
		m->complete(m->context);
	return -EINVAL;
}

void spi_init_n()
{
	int d;
	ls2k500_spi_write_reg(&ls2k500_spi0, SPSR, 0xc0);
	if ((unsigned long)(ls2k500_spi0.base) == LS2K500_SPI0_BASE || 	\
			(unsigned long)(ls2k500_spi0.base) == LS2K500_SPI1_BASE) {
		ls2k500_spi_write_reg(&ls2k500_spi0, PARAM, 0x40);
		ls2k500_spi_write_reg(&ls2k500_spi0, PARAM2,0x01);
	}
	ls2k500_spi_write_reg(&ls2k500_spi0, SPER, 0x05);
	ls2k500_spi_write_reg(&ls2k500_spi0, SPCR, 0x50);

	if ((unsigned long)(ls2k500_spi0.base) != LS2K500_SPI0_BASE && 	\
			(unsigned long)(ls2k500_spi0.base) != LS2K500_SPI1_BASE) {
		ls2k500_spi_write_reg(&ls2k500_spi0, APB_SPCS, 0x1);
	} else {
		ls2k500_spi_write_reg(&ls2k500_spi0, SOFTCS, 0xff);
	}
}

void spi_init_r(void)
{
	if ((unsigned long)(ls2k500_spi0.base) == LS2K500_SPI0_BASE || 	\
			(unsigned long)(ls2k500_spi0.base) == LS2K500_SPI1_BASE)
		ls2k500_spi_write_reg(&ls2k500_spi0, PARAM, 0x47);
}

#if NM25P80
int ls_m25p_probe()
{
	char dev_name[5] = "spi0";
	if ((unsigned long)ls2k500_spi0.base == LS2K500_SPI1_BASE) {
		loop_set_pin(44, 47, 5, 0);
		loop_set_pin(67, 69, 3, 0);
	} else if ((unsigned long)ls2k500_spi0.base == LS2K500_SPI2_BASE)
		loop_set_pin(113, 116, 4, 0);
	else if ((unsigned long)ls2k500_spi0.base == LS2K500_SPI3_BASE)
		loop_set_pin(32, 35, 3, 0);
	else if ((unsigned long)ls2k500_spi0.base == LS2K500_SPI4_BASE)
		loop_set_pin(24, 27, 3, 0);
	else if ((unsigned long)ls2k500_spi0.base == LS2K500_SPI5_BASE)
		loop_set_pin(28, 31, 3, 0);

	spi_init_n();
	m25p_probe(&spi_nand, "w25q32");
	spi_init_r();
}

#endif
#endif

static const Cmd Cmds[] =
{
	{"MyCmds"},
	{"spi_base", "", NULL, "set spi base", set_spi_base , 0, 99, 1},
	{"spi_initw","",0,"spi_initw(sst25vf080b)",spi_initw,0,99,CMD_REPEAT},
	{"read_pmon","",0,"read_pmon(sst25vf080b)",read_pmon,0,99,CMD_REPEAT},
	{"write_pmon","",0,"write_pmon(sst25vf080b)",write_pmon,0,99,CMD_REPEAT},
	{"erase_all","",0,"erase_all(sst25vf080b)",erase_all,0,99,CMD_REPEAT},
	{"write_pmon_byte","",0,"write_pmon_byte(sst25vf080b)",write_pmon_byte,0,99,CMD_REPEAT},
	{"read_flash_id","",0,"read_flash_id(sst25vf080b)",spi_read_id,0,99,CMD_REPEAT},
	{"spi_id","",0,"read_flash_id(sst25vf080b)",spi_jedec_id,0,99,CMD_REPEAT},
	{"set_mac", "", NULL, "set the Mac address of syn0 and syn1", cmd_set_mac, 1, 5, 0},
	{0,0}
};

static void init_cmd __P((void)) __attribute__ ((constructor));

static void
init_cmd()
{
	cmdlist_expand(Cmds,1);
}
