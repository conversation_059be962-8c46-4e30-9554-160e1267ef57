/******************** (C) COPYRIGHT 2008 STMicroelectronics ********************
* File Name          : stm32f10x_adc.h
* Author             : MCD Application Team
* Version            : V2.0.3
* Date               : 09/22/2008
* Description        : This file contains all the functions prototypes for the
*                      ADC firmware library.
********************************************************************************
* THE PRESENT FIRMWARE WHICH IS FOR GUIDANCE ONLY AIMS AT PROVIDING CUSTOMERS
* WITH CODING INFORMATION REGARDING THEIR PRODUCTS IN ORDER FOR THEM TO SAVE TIME.
* AS A RESULT, STMICROELECTRONICS SHALL NOT BE HELD LIABLE FOR ANY DIRECT,
* INDIRECT OR CONSEQUENTIAL DAMAGES WITH RESPECT TO ANY CLAIMS ARISING FROM THE
* CONTENT OF SUCH FIRMWARE AND/OR THE USE MADE BY CUSTOMERS OF THE CODING
* INFORMATION CONTAINED HEREIN IN CONNECTION WITH THEIR PRODUCTS.
*******************************************************************************/

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __LS2K0300_ADC_H
#define __LS2K0300_ADC_H

/* Includes ------------------------------------------------------------------*/
#if   defined(LOONGARCH_2P500)
#include "../../include/ls2p500.h"
#elif   defined(LOONGARCH_2P300)
#include "../../include/ls2p300.h"
#endif
#include "../type.h"
/*------------------------ Analog to Digital Converter -----------------------*/
typedef struct
{
  vu32 SR;
  vu32 CR1;
  vu32 CR2;
  vu32 SMPR1;
  vu32 SMPR2;
  vu32 JOFR1;
  vu32 JOFR2;
  vu32 JOFR3;
  vu32 JOFR4;
  vu32 HTR;
  vu32 LTR;
  vu32 SQR1;
  vu32 SQR2;
  vu32 SQR3;
  vu32 JSQR;
  vu32 JDR1;
  vu32 JDR2;
  vu32 JDR3;
  vu32 JDR4;
  vu32 DR;
} ADC_TypeDef;


/* Exported types ------------------------------------------------------------*/
/* ADC Init structure definition */
typedef struct
{
  u32 ADC_Mode;
  FunctionalState ADC_ScanConvMode;  //CR1 scan
  FunctionalState ADC_ContinuousConvMode;//CR2 cont
  u32 ADC_ExternalTrigConv;//CR2 extsel
  u32 ADC_DataAlign;//CR2 align
  u8  ADC_NbrOfChannel;//SQR1 l
  u16 ADC_ClkDivider;//CR1 clkdiv
  u8  ADC_JTrigMod;//CR2 jtrigmod
  u8  ADC_ADCEdge;//CR2 adcedge
  u8  ADC_DiffMod;//CR1 diffmod
  u8  ADC_OutPhaseSel;//CR1 ops
  u8  ADC_ClkMask;//CR2 clkmask
}ADC_InitTypeDef;

/* Exported constants --------------------------------------------------------*/
#define IS_ADC_ALL_PERIPH(PERIPH) (((*(u32*)&(PERIPH)) == ADC1_BASE) || \
                                   ((*(u32*)&(PERIPH)) == ADC2_BASE) || \
                                   ((*(u32*)&(PERIPH)) == ADC3_BASE))

#define IS_ADC_DMA_PERIPH(PERIPH) (((*(u32*)&(PERIPH)) == ADC1_BASE) || \
                                   ((*(u32*)&(PERIPH)) == ADC3_BASE))

/* ADC dual mode -------------------------------------------------------------*/
#define ADC_Mode_Independent                       ((u32)0x00000000)
#define ADC_Mode_RegInjecSimult                    ((u32)0x00010000)
#define ADC_Mode_RegSimult_AlterTrig               ((u32)0x00020000)
#define ADC_Mode_InjecSimult_FastInterl            ((u32)0x00030000)
#define ADC_Mode_InjecSimult_SlowInterl            ((u32)0x00040000)
#define ADC_Mode_InjecSimult                       ((u32)0x00050000)
#define ADC_Mode_RegSimult                         ((u32)0x00060000)
#define ADC_Mode_FastInterl                        ((u32)0x00070000)
#define ADC_Mode_SlowInterl                        ((u32)0x00080000)
#define ADC_Mode_AlterTrig                         ((u32)0x00090000)

#define IS_ADC_MODE(MODE) (((MODE) == ADC_Mode_Independent) || \
                           ((MODE) == ADC_Mode_RegInjecSimult) || \
                           ((MODE) == ADC_Mode_RegSimult_AlterTrig) || \
                           ((MODE) == ADC_Mode_InjecSimult_FastInterl) || \
                           ((MODE) == ADC_Mode_InjecSimult_SlowInterl) || \
                           ((MODE) == ADC_Mode_InjecSimult) || \
                           ((MODE) == ADC_Mode_RegSimult) || \
                           ((MODE) == ADC_Mode_FastInterl) || \
                           ((MODE) == ADC_Mode_SlowInterl) || \
                           ((MODE) == ADC_Mode_AlterTrig))

/* ADC extrenal trigger sources for regular channels conversion --------------*/
/* for ADC1 and ADC2 */
#define ADC_ExternalTrigConv_T1_CC1                ((u32)0x00000000)
#define ADC_ExternalTrigConv_T1_CC2                ((u32)0x00020000)
#define ADC_ExternalTrigConv_T2_CC2                ((u32)0x00060000)
#define ADC_ExternalTrigConv_T3_TRGO               ((u32)0x00080000)
#define ADC_ExternalTrigConv_T4_CC4                ((u32)0x000A0000)
#define ADC_ExternalTrigConv_Ext_IT11_TIM8_TRGO    ((u32)0x000C0000)
/* for ADC1, ADC2 and ADC3 */
#define ADC_ExternalTrigConv_T1_CC3                ((u32)0x00040000)
#define ADC_ExternalTrigConv_None                  ((u32)0x000E0000)
/* for ADC3 */
#define ADC_ExternalTrigConv_T3_CC1                ((u32)0x00000000)
#define ADC_ExternalTrigConv_T2_CC3                ((u32)0x00020000)
#define ADC_ExternalTrigConv_T8_CC1                ((u32)0x00060000)
#define ADC_ExternalTrigConv_T8_TRGO               ((u32)0x00080000)
#define ADC_ExternalTrigConv_T5_CC1                ((u32)0x000A0000)
#define ADC_ExternalTrigConv_T5_CC3                ((u32)0x000C0000)

#define IS_ADC_EXT_TRIG(REGTRIG) (((REGTRIG) == ADC_ExternalTrigConv_T1_CC1) || \
                                  ((REGTRIG) == ADC_ExternalTrigConv_T1_CC2) || \
                                  ((REGTRIG) == ADC_ExternalTrigConv_T1_CC3) || \
                                  ((REGTRIG) == ADC_ExternalTrigConv_T2_CC2) || \
                                  ((REGTRIG) == ADC_ExternalTrigConv_T3_TRGO) || \
                                  ((REGTRIG) == ADC_ExternalTrigConv_T4_CC4) || \
                                  ((REGTRIG) == ADC_ExternalTrigConv_Ext_IT11_TIM8_TRGO) || \
                                  ((REGTRIG) == ADC_ExternalTrigConv_None) || \
                                  ((REGTRIG) == ADC_ExternalTrigConv_T3_CC1) || \
                                  ((REGTRIG) == ADC_ExternalTrigConv_T2_CC3) || \
                                  ((REGTRIG) == ADC_ExternalTrigConv_T8_CC1) || \
                                  ((REGTRIG) == ADC_ExternalTrigConv_T8_TRGO) || \
                                  ((REGTRIG) == ADC_ExternalTrigConv_T5_CC1) || \
                                  ((REGTRIG) == ADC_ExternalTrigConv_T5_CC3))

/* ADC data align ------------------------------------------------------------*/
#define ADC_DataAlign_Right                        ((u32)0x00000000)
#define ADC_DataAlign_Left                         ((u32)0x00000800)

#define IS_ADC_DATA_ALIGN(ALIGN) (((ALIGN) == ADC_DataAlign_Right) || \
                                  ((ALIGN) == ADC_DataAlign_Left))

/* ADC channels --------------------------------------------------------------*/
#define ADC_Channel_0                               ((u8)0x00)
#define ADC_Channel_1                               ((u8)0x01)
#define ADC_Channel_2                               ((u8)0x02)
#define ADC_Channel_3                               ((u8)0x03)
#define ADC_Channel_4                               ((u8)0x04)
#define ADC_Channel_5                               ((u8)0x05)
#define ADC_Channel_6                               ((u8)0x06)
#define ADC_Channel_7                               ((u8)0x07)
#define ADC_Channel_8                               ((u8)0x08)
#define ADC_Channel_9                               ((u8)0x09)
#define ADC_Channel_10                              ((u8)0x0a)
#define ADC_Channel_11                              ((u8)0x13)
#define ADC_Channel_12                              ((u8)0x0C)
#define ADC_Channel_13                              ((u8)0x0D)
#define ADC_Channel_14                              ((u8)0x0E)
#define ADC_Channel_15                              ((u8)0x0F)
#define ADC_Channel_16                              ((u8)0x22)
#define ADC_Channel_17                              ((u8)0x23)

#define IS_ADC_CHANNEL(CHANNEL) (((CHANNEL) == ADC_Channel_0) || ((CHANNEL) == ADC_Channel_1) || \
                                 ((CHANNEL) == ADC_Channel_2) || ((CHANNEL) == ADC_Channel_3) || \
                                 ((CHANNEL) == ADC_Channel_4) || ((CHANNEL) == ADC_Channel_5) || \
                                 ((CHANNEL) == ADC_Channel_6) || ((CHANNEL) == ADC_Channel_7) || \
                                 ((CHANNEL) == ADC_Channel_8) || ((CHANNEL) == ADC_Channel_9) || \
                                 ((CHANNEL) == ADC_Channel_10) || ((CHANNEL) == ADC_Channel_11) || \
                                 ((CHANNEL) == ADC_Channel_12) || ((CHANNEL) == ADC_Channel_13) || \
                                 ((CHANNEL) == ADC_Channel_14) || ((CHANNEL) == ADC_Channel_15) || \
                                 ((CHANNEL) == ADC_Channel_16) || ((CHANNEL) == ADC_Channel_17))

/* ADC sampling times --------------------------------------------------------*/
#define ADC_SampleTime_1Cycles                    ((u8)0x00)
#define ADC_SampleTime_2Cycles                    ((u8)0x01)
#define ADC_SampleTime_4Cycles                    ((u8)0x02)
#define ADC_SampleTime_8Cycles                    ((u8)0x03)
#define ADC_SampleTime_16Cycles                   ((u8)0x04)
#define ADC_SampleTime_32Cycles                   ((u8)0x05)
#define ADC_SampleTime_64Cycles                   ((u8)0x06)
#define ADC_SampleTime_128Cycles                  ((u8)0x07)

#define IS_ADC_SAMPLE_TIME(TIME) (((TIME) == ADC_SampleTime_1Cycles)  || \
                                  ((TIME) == ADC_SampleTime_2Cycles)  || \
                                  ((TIME) == ADC_SampleTime_4Cycles)  || \
                                  ((TIME) == ADC_SampleTime_8Cycles)  || \
                                  ((TIME) == ADC_SampleTime_16Cycles) || \
                                  ((TIME) == ADC_SampleTime_32Cycles) || \
                                  ((TIME) == ADC_SampleTime_64Cycles) || \
                                  ((TIME) == ADC_SampleTime_128Cycles))

/* ADC extrenal trigger sources for injected channels conversion -------------*/
/* For ADC1 and ADC2 */
#define ADC_ExternalTrigInjecConv_T2_TRGO           ((u32)0x00002000)
#define ADC_ExternalTrigInjecConv_T2_CC1            ((u32)0x00003000)
#define ADC_ExternalTrigInjecConv_T3_CC4            ((u32)0x00004000)
#define ADC_ExternalTrigInjecConv_T4_TRGO           ((u32)0x00005000)
#define ADC_ExternalTrigInjecConv_Ext_IT15_TIM8_CC4 ((u32)0x00006000)
/* For ADC1, ADC2 and ADC3 */
#define ADC_ExternalTrigInjecConv_T1_TRGO           ((u32)0x00000000)
#define ADC_ExternalTrigInjecConv_T1_CC4            ((u32)0x00001000)
#define ADC_ExternalTrigInjecConv_None              ((u32)0x00007000)
/* For ADC3 */
#define ADC_ExternalTrigInjecConv_T4_CC3            ((u32)0x00002000)
#define ADC_ExternalTrigInjecConv_T8_CC2            ((u32)0x00003000)
#define ADC_ExternalTrigInjecConv_T8_CC4            ((u32)0x00004000)
#define ADC_ExternalTrigInjecConv_T5_TRGO           ((u32)0x00005000)
#define ADC_ExternalTrigInjecConv_T5_CC4            ((u32)0x00006000)

#define IS_ADC_EXT_INJEC_TRIG(INJTRIG) (((INJTRIG) == ADC_ExternalTrigInjecConv_T1_TRGO) || \
                                        ((INJTRIG) == ADC_ExternalTrigInjecConv_T1_CC4) || \
                                        ((INJTRIG) == ADC_ExternalTrigInjecConv_T2_TRGO) || \
                                        ((INJTRIG) == ADC_ExternalTrigInjecConv_T2_CC1) || \
                                        ((INJTRIG) == ADC_ExternalTrigInjecConv_T3_CC4) || \
                                        ((INJTRIG) == ADC_ExternalTrigInjecConv_T4_TRGO) || \
                                        ((INJTRIG) == ADC_ExternalTrigInjecConv_Ext_IT15_TIM8_CC4) || \
                                        ((INJTRIG) == ADC_ExternalTrigInjecConv_None) || \
                                        ((INJTRIG) == ADC_ExternalTrigInjecConv_T4_CC3) || \
                                        ((INJTRIG) == ADC_ExternalTrigInjecConv_T8_CC2) || \
                                        ((INJTRIG) == ADC_ExternalTrigInjecConv_T8_CC4) || \
                                        ((INJTRIG) == ADC_ExternalTrigInjecConv_T5_TRGO) || \
                                        ((INJTRIG) == ADC_ExternalTrigInjecConv_T5_CC4))

/* ADC injected channel selection --------------------------------------------*/
#define ADC_InjectedChannel_1                       ((u8)0x14)
#define ADC_InjectedChannel_2                       ((u8)0x18)
#define ADC_InjectedChannel_3                       ((u8)0x1C)
#define ADC_InjectedChannel_4                       ((u8)0x20)

#define IS_ADC_INJECTED_CHANNEL(CHANNEL) (((CHANNEL) == ADC_InjectedChannel_1) || \
                                          ((CHANNEL) == ADC_InjectedChannel_2) || \
                                          ((CHANNEL) == ADC_InjectedChannel_3) || \
                                          ((CHANNEL) == ADC_InjectedChannel_4))

/* ADC analog watchdog selection ---------------------------------------------*/
#define ADC_AnalogWatchdog_SingleRegEnable         ((u32)0x00800200)
#define ADC_AnalogWatchdog_SingleInjecEnable       ((u32)0x00400200)
#define ADC_AnalogWatchdog_SingleRegOrInjecEnable  ((u32)0x00C00200)
#define ADC_AnalogWatchdog_AllRegEnable            ((u32)0x00800000)
#define ADC_AnalogWatchdog_AllInjecEnable          ((u32)0x00400000)
#define ADC_AnalogWatchdog_AllRegAllInjecEnable    ((u32)0x00C00000)
#define ADC_AnalogWatchdog_None                    ((u32)0x00000000)

#define IS_ADC_ANALOG_WATCHDOG(WATCHDOG) (((WATCHDOG) == ADC_AnalogWatchdog_SingleRegEnable) || \
                                          ((WATCHDOG) == ADC_AnalogWatchdog_SingleInjecEnable) || \
                                          ((WATCHDOG) == ADC_AnalogWatchdog_SingleRegOrInjecEnable) || \
                                          ((WATCHDOG) == ADC_AnalogWatchdog_AllRegEnable) || \
                                          ((WATCHDOG) == ADC_AnalogWatchdog_AllInjecEnable) || \
                                          ((WATCHDOG) == ADC_AnalogWatchdog_AllRegAllInjecEnable) || \
                                          ((WATCHDOG) == ADC_AnalogWatchdog_None))

/* ADC interrupts definition -------------------------------------------------*/
#define ADC_IT_EOC                                 ((u16)0x0220)
#define ADC_IT_AWD                                 ((u16)0x0140)
#define ADC_IT_JEOC                                ((u16)0x0480)

#define IS_ADC_IT(IT) ((((IT) & (u16)0xF81F) == 0x00) && ((IT) != 0x00))
#define IS_ADC_GET_IT(IT) (((IT) == ADC_IT_EOC) || ((IT) == ADC_IT_AWD) || \
                           ((IT) == ADC_IT_JEOC))

/* ADC flags definition ------------------------------------------------------*/
#define ADC_FLAG_AWD                               ((u8)0x01)
#define ADC_FLAG_EOC                               ((u8)0x02)
#define ADC_FLAG_JEOC                              ((u8)0x04)
#define ADC_FLAG_JSTRT                             ((u8)0x08)
#define ADC_FLAG_STRT                              ((u8)0x10)

#define IS_ADC_CLEAR_FLAG(FLAG) ((((FLAG) & (u8)0xE0) == 0x00) && ((FLAG) != 0x00))
#define IS_ADC_GET_FLAG(FLAG) (((FLAG) == ADC_FLAG_AWD) || ((FLAG) == ADC_FLAG_EOC) || \
                               ((FLAG) == ADC_FLAG_JEOC) || ((FLAG)== ADC_FLAG_JSTRT) || \
                               ((FLAG) == ADC_FLAG_STRT))

/* ADC thresholds ------------------------------------------------------------*/
#define IS_ADC_THRESHOLD(THRESHOLD) ((THRESHOLD) <= 0xFFF)

/* ADC injected offset -------------------------------------------------------*/
#define IS_ADC_OFFSET(OFFSET) ((OFFSET) <= 0xFFF)

/* ADC injected length -------------------------------------------------------*/
#define IS_ADC_INJECTED_LENGTH(LENGTH) (((LENGTH) >= 0x1) && ((LENGTH) <= 0x4))

/* ADC injected rank ---------------------------------------------------------*/
#define IS_ADC_INJECTED_RANK(RANK) (((RANK) >= 0x1) && ((RANK) <= 0x4))

/* ADC regular length --------------------------------------------------------*/
#define IS_ADC_REGULAR_LENGTH(LENGTH) (((LENGTH) >= 0x1) && ((LENGTH) <= 0x10))

/* ADC regular rank ----------------------------------------------------------*/
#define IS_ADC_REGULAR_RANK(RANK) (((RANK) >= 0x1) && ((RANK) <= 0x10))

/* ADC regular discontinuous mode number -------------------------------------*/
#define IS_ADC_REGULAR_DISC_NUMBER(NUMBER) (((NUMBER) >= 0x1) && ((NUMBER) <= 0x8))

/* Exported macro ------------------------------------------------------------*/
/* Exported functions ------------------------------------------------------- */
void ADC_DeInit(ADC_TypeDef* ADCx);
void ADC_Init(ADC_TypeDef* ADCx, ADC_InitTypeDef* ADC_InitStruct);
void ADC_StructInit(ADC_InitTypeDef* ADC_InitStruct);
void ADC_Cmd(ADC_TypeDef* ADCx, FunctionalState NewState);
void ADC_DMACmd(ADC_TypeDef* ADCx, FunctionalState NewState);
void ADC_ITConfig(ADC_TypeDef* ADCx, u16 ADC_IT, FunctionalState NewState);
void ADC_ResetCalibration(ADC_TypeDef* ADCx);
FlagStatus ADC_GetResetCalibrationStatus(ADC_TypeDef* ADCx);
void ADC_StartCalibration(ADC_TypeDef* ADCx);
FlagStatus ADC_GetCalibrationStatus(ADC_TypeDef* ADCx);
void ADC_SoftwareStartConvCmd(ADC_TypeDef* ADCx, FunctionalState NewState);
FlagStatus ADC_GetSoftwareStartConvStatus(ADC_TypeDef* ADCx);
void ADC_DiscModeChannelCountConfig(ADC_TypeDef* ADCx, u8 Number);
void ADC_DiscModeCmd(ADC_TypeDef* ADCx, FunctionalState NewState);
void ADC_TestChannelMap(u8 ADC_Channel, u8 ADC_DOUT);
void ADC_RegularChannelConfig(ADC_TypeDef* ADCx, u8 ADC_Channel, u8 Rank, u8 ADC_SampleTime);
void ADC_ExternalTrigConvCmd(ADC_TypeDef* ADCx, FunctionalState NewState);
u16 ADC_GetConversionValue(ADC_TypeDef* ADCx);
u32 ADC_GetDualModeConversionValue(void);
void ADC_AutoInjectedConvCmd(ADC_TypeDef* ADCx, FunctionalState NewState);
void ADC_InjectedDiscModeCmd(ADC_TypeDef* ADCx, FunctionalState NewState);
void ADC_ExternalTrigInjectedConvConfig(ADC_TypeDef* ADCx, u32 ADC_ExternalTrigInjecConv);
void ADC_ExternalTrigInjectedConvCmd(ADC_TypeDef* ADCx, FunctionalState NewState);
void ADC_SoftwareStartInjectedConvCmd(ADC_TypeDef* ADCx, FunctionalState NewState);
FlagStatus ADC_GetSoftwareStartInjectedConvCmdStatus(ADC_TypeDef* ADCx);
void ADC_InjectedChannelConfig(ADC_TypeDef* ADCx, u8 ADC_Channel, u8 Rank, u8 ADC_SampleTime);
void ADC_InjectedSequencerLengthConfig(ADC_TypeDef* ADCx, u8 Length);
void ADC_SetInjectedOffset(ADC_TypeDef* ADCx, u8 ADC_InjectedChannel, u16 Offset);
u16 ADC_GetInjectedConversionValue(ADC_TypeDef* ADCx, u8 ADC_InjectedChannel);
void ADC_AnalogWatchdogCmd(ADC_TypeDef* ADCx, u32 ADC_AnalogWatchdog);
void ADC_AnalogWatchdogThresholdsConfig(ADC_TypeDef* ADCx, u16 HighThreshold, u16 LowThreshold);
void ADC_AnalogWatchdogSingleChannelConfig(ADC_TypeDef* ADCx, u8 ADC_Channel);
void ADC_TempSensorVrefintCmd(FunctionalState NewState);
FlagStatus ADC_GetFlagStatus(ADC_TypeDef* ADCx, u8 ADC_FLAG);
void ADC_ClearFlag(ADC_TypeDef* ADCx, u8 ADC_FLAG);
ITStatus ADC_GetITStatus(ADC_TypeDef* ADCx, u16 ADC_IT);
void ADC_ClearITPendingBit(ADC_TypeDef* ADCx, u16 ADC_IT);

//Loongson Feature
#define RCH0  ADC_Channel_0
#define RCH1  ADC_Channel_1
#define RCH2  ADC_Channel_2
#define RCH3  ADC_Channel_3
#define RCH4  ADC_Channel_4
#define RCH5  ADC_Channel_5
#define RCH6  ADC_Channel_6
#define RCH7  ADC_Channel_7

//Loongson Feature
#define JCH0  ADC_Channel_0
#define JCH1  ADC_Channel_1
#define JCH2  ADC_Channel_2
#define JCH3  ADC_Channel_3

#endif /*__STM32F10x_ADC_H */

/******************* (C) COPYRIGHT 2008 STMicroelectronics *****END OF FILE****/
