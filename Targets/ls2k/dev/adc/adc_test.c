#include "../../include/ls2p300.h"
#include "ls2p300_adc.h"
#include "../ls2p300_dma.h"
#include <pmon.h>

#define TEST_CH  8

static u16 __attribute__((__aligned__(128))) ADCConvertedValue[TEST_CH] = {0};

int dma_adc_test(int argc, char *argv[])
{
  int channel = atoi(argv[1]);

  ADC_InitTypeDef ADC_InitStructure;
  DMA_InitTypeDef DMA_InitStructure;
  DMA_StructInit(&DMA_InitStructure);
  ADC_StructInit(&ADC_InitStructure);

  // ADC DMA map config
  *(uint64_t*)(CONFBUS_BASE) = (((*(uint64_t*)(CONFBUS_BASE)) & 0xfffffffffffff8ff) | 0x0);

  /* DMA1 channel1 configuration ----------------------------------------------*/
  DMA_DeInit(DMA1_Channel1);
  DMA_InitStructure.DMA_PeripheralBaseAddr = (u32)(&ADC1->DR);
  DMA_InitStructure.DMA_MemoryBaseAddr = (u32)ADCConvertedValue;
  DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralSRC;
  DMA_InitStructure.DMA_BufferSize = TEST_CH;
  DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
  DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
  DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;
  DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;
  DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;
  DMA_InitStructure.DMA_Priority = DMA_Priority_High;
  DMA_InitStructure.DMA_M2M = DMA_M2M_Disable;
  DMA_Init(DMA1_Channel1, &DMA_InitStructure);

  /* Clear DMA1 FLAG of channel1 */
  DMA_ClearFlag(DMA1_FLAG_GL1);
  /* Enable DMA1 channel1 */
  DMA_Cmd(DMA1_Channel1, ENABLE);

  /* Deinitializes the ADC*/
  ADC_DeInit(ADC1);

  /* ADC1 configuration */
  ADC_InitStructure.ADC_Mode = ADC_Mode_Independent;
  ADC_InitStructure.ADC_ScanConvMode = ENABLE;
  ADC_InitStructure.ADC_ContinuousConvMode = DISABLE;
  ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_None;
  ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;
  ADC_InitStructure.ADC_NbrOfChannel = TEST_CH;
  ADC_InitStructure.ADC_ClkDivider = 1;  //Loongson Feature
  ADC_InitStructure.ADC_JTrigMod = 0;    //Loongson Feature
  ADC_InitStructure.ADC_ADCEdge = 0;     //Loongson Feature
  ADC_InitStructure.ADC_DiffMod = 0;     //Loongson Feature
  ADC_Init(ADC1, &ADC_InitStructure);

  ADC_RegularChannelConfig(ADC1, 0 , 1, ADC_SampleTime_64Cycles);
  ADC_RegularChannelConfig(ADC1, 1 , 2, ADC_SampleTime_64Cycles);
  ADC_RegularChannelConfig(ADC1, 2 , 3, ADC_SampleTime_64Cycles);
  ADC_RegularChannelConfig(ADC1, 3 , 4, ADC_SampleTime_64Cycles);
  ADC_RegularChannelConfig(ADC1, 4 , 5, ADC_SampleTime_64Cycles);
  ADC_RegularChannelConfig(ADC1, 5 , 6, ADC_SampleTime_64Cycles);
  ADC_RegularChannelConfig(ADC1, 6 , 7, ADC_SampleTime_64Cycles);
  ADC_RegularChannelConfig(ADC1, 7 , 8, ADC_SampleTime_64Cycles);
  /* Enable ADC1 DMA */
  ADC_DMACmd(ADC1, ENABLE);

  /* Enable ADC1 */
  ADC_Cmd(ADC1, ENABLE);


  /* Start ADC1 Software Conversion */
  ADC_SoftwareStartConvCmd(ADC1, ENABLE);


printf("%s %d  flag = %d   channel =%d-----------------\n",__func__,__LINE__,DMA_GetFlagStatus(DMA1_FLAG_TC1),channel);
  while (!DMA_GetFlagStatus(DMA1_FLAG_TC1));

  DMA_ClearFlag(DMA1_FLAG_GL1);

  printf("val of %d conv: %dmv\n", channel, (*(((u16*)(ADCConvertedValue))+channel)*1800/4095));


  /* Deinitializes the ADC*/
  ADC_DeInit(ADC1);

  return 0;
}

static const Cmd Cmds[] = {
  {"2k300 adc"},
  {"adc_dma", "", 0, "test the adc function", dma_adc_test, 1, 99, 0},
  {0, 0}
};

static void init_cmd __P((void)) __attribute__ ((constructor));
static void init_cmd()
{
        cmdlist_expand(Cmds, 1);
}
