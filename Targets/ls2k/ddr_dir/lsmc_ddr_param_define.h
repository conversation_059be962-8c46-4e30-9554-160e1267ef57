/*****************************
    Macro defination for DDR MC parameters
    Author: Chen Xinke
    v0.1
*******************************/
#define DDR_MC_CONFIG_BASE      0x800000000ff00000
#define MC_CONFIG_REG_BASE_ADDR 0x800000000ff00000
#ifdef  LSMC_2
#define DDR_PARAM_NUM   110
#else
#define DDR_PARAM_NUM   100
#endif

#define MAX_ROW_SIZE            16

#define START_ADDR              (0x18)
#define START_OFFSET            0

#define DRAM_INIT_ADDR          (0x160)
#define DRAM_INIT_OFFSET        24
#define ODT_MAP_CS_ADDR         (0x170)
#define ADDRESS_MIRROR_ADDR     (0x168)
#define ADDRESS_MIRROR_OFFSET   48

#define CMD_TIMING              (0x160)
#define CMD_TIMING_OFFSET       8
#define TRDDATA                 (0x1c0)
#define TRDDATA_OFFSET          0
#define TPHY_WRLAT              (0x1d0)
#define TPHY_WRLAT_OFFSET       32


#define MR2_DATA_0_ADDR         (0x1a0)
#define MR2_DATA_0_OFFSET       32
#define MR2_DATA_1_ADDR         (0x1a8)
#define MR2_DATA_1_OFFSET       32
#define MR2_DATA_2_ADDR         (0x1b0)
#define MR2_DATA_2_OFFSET       32
#define MR2_DATA_3_ADDR         (0x1b8)
#define MR2_DATA_3_OFFSET       32

#define RD_GATE_LEVEL           0x2
#define ONE_BYTE_MASK           0xff
#define LEVEL_MODE_ADDR         (0x180)
#define LEVEL_MODE_OFFSET       0
#define LEVEL_CS_ADDR           (0x180)
#define LEVEL_CS_OFFSET         24
#define LEVEL_READY_ADDR        (0x180)
#define LEVEL_READY_OFFSET      40
#define LEVEL_REQ_ADDR          (0x180)
#define LEVEL_REQ_OFFSET        8
#define LEVEL_DONE_ADDR         (0x180)
#define LEVEL_DONE_OFFSET       48
#define LEVEL_RESP_0_ADDR       (0x180)
#define LEVEL_RESP_0_OFFSET     56
#define LEVEL_RESP_7_ADDR       (0x188)
#define LEVEL_RESP_7_OFFSET     48

#define RD_OE_EDGE_8_ADDR        (0x128)
#define RD_OE_EDGE_7_ADDR        (0x108)
#define RD_OE_EDGE_6_ADDR        (0x0e8)
#define RD_OE_EDGE_5_ADDR        (0x0c8)
#define RD_OE_EDGE_4_ADDR        (0x0a8)
#define RD_OE_EDGE_3_ADDR        (0x088)
#define RD_OE_EDGE_2_ADDR        (0x068)
#define RD_OE_EDGE_1_ADDR        (0x048)
#define RD_OE_EDGE_0_ADDR        (0x028)
#define RD_OE_EDGE_8_OFFSET      32
#define RD_OE_EDGE_7_OFFSET      32
#define RD_OE_EDGE_6_OFFSET      32
#define RD_OE_EDGE_5_OFFSET      32
#define RD_OE_EDGE_4_OFFSET      32
#define RD_OE_EDGE_3_OFFSET      32
#define RD_OE_EDGE_2_OFFSET      32
#define RD_OE_EDGE_1_OFFSET      32
#define RD_OE_EDGE_0_OFFSET      32
#define RD_OE_EDGE_MASK          (0xffffffff)

#define WR_DQS_OE_EDGE_8_ADDR        (0x128)
#define WR_DQS_OE_EDGE_7_ADDR        (0x108)
#define WR_DQS_OE_EDGE_6_ADDR        (0x0e8)
#define WR_DQS_OE_EDGE_5_ADDR        (0x0c8)
#define WR_DQS_OE_EDGE_4_ADDR        (0x0a8)
#define WR_DQS_OE_EDGE_3_ADDR        (0x088)
#define WR_DQS_OE_EDGE_2_ADDR        (0x068)
#define WR_DQS_OE_EDGE_1_ADDR        (0x048)
#define WR_DQS_OE_EDGE_0_ADDR        (0x028)
#define WR_DQS_OE_EDGE_8_OFFSET      0
#define WR_DQS_OE_EDGE_7_OFFSET      0
#define WR_DQS_OE_EDGE_6_OFFSET      0
#define WR_DQS_OE_EDGE_5_OFFSET      0
#define WR_DQS_OE_EDGE_4_OFFSET      0
#define WR_DQS_OE_EDGE_3_OFFSET      0
#define WR_DQS_OE_EDGE_2_OFFSET      0
#define WR_DQS_OE_EDGE_1_OFFSET      0
#define WR_DQS_OE_EDGE_0_OFFSET      0
#define WR_DQS_OE_EDGE_MASK          (0xffffffff)

#define WR_DQ_OE_EDGE_8_ADDR        (0x120)
#define WR_DQ_OE_EDGE_7_ADDR        (0x100)
#define WR_DQ_OE_EDGE_6_ADDR        (0x0e0)
#define WR_DQ_OE_EDGE_5_ADDR        (0x0c0)
#define WR_DQ_OE_EDGE_4_ADDR        (0x0a0)
#define WR_DQ_OE_EDGE_3_ADDR        (0x080)
#define WR_DQ_OE_EDGE_2_ADDR        (0x060)
#define WR_DQ_OE_EDGE_1_ADDR        (0x040)
#define WR_DQ_OE_EDGE_0_ADDR        (0x020)
#define WR_DQ_OE_EDGE_8_OFFSET      32
#define WR_DQ_OE_EDGE_7_OFFSET      32
#define WR_DQ_OE_EDGE_6_OFFSET      32
#define WR_DQ_OE_EDGE_5_OFFSET      32
#define WR_DQ_OE_EDGE_4_OFFSET      32
#define WR_DQ_OE_EDGE_3_OFFSET      32
#define WR_DQ_OE_EDGE_2_OFFSET      32
#define WR_DQ_OE_EDGE_1_OFFSET      32
#define WR_DQ_OE_EDGE_0_OFFSET      32
#define WR_DQ_OE_EDGE_MASK          (0xffffffff)

#define WR_ODT_OE_EDGE_8_ADDR        (0x130)
#define WR_ODT_OE_EDGE_7_ADDR        (0x110)
#define WR_ODT_OE_EDGE_6_ADDR        (0x0f0)
#define WR_ODT_OE_EDGE_5_ADDR        (0x0d0)
#define WR_ODT_OE_EDGE_4_ADDR        (0x0b0)
#define WR_ODT_OE_EDGE_3_ADDR        (0x090)
#define WR_ODT_OE_EDGE_2_ADDR        (0x070)
#define WR_ODT_OE_EDGE_1_ADDR        (0x050)
#define WR_ODT_OE_EDGE_0_ADDR        (0x030)
#define WR_ODT_OE_EDGE_8_OFFSET      0
#define WR_ODT_OE_EDGE_7_OFFSET      0
#define WR_ODT_OE_EDGE_6_OFFSET      0
#define WR_ODT_OE_EDGE_5_OFFSET      0
#define WR_ODT_OE_EDGE_4_OFFSET      0
#define WR_ODT_OE_EDGE_3_OFFSET      0
#define WR_ODT_OE_EDGE_2_OFFSET      0
#define WR_ODT_OE_EDGE_1_OFFSET      0
#define WR_ODT_OE_EDGE_0_OFFSET      0
#define WR_ODT_OE_EDGE_MASK          (0xffffffff)

#define WRDQ_CLK_DELAY_8_ADDR        (0x130)
#define WRDQ_CLK_DELAY_7_ADDR        (0x110)
#define WRDQ_CLK_DELAY_6_ADDR        (0x0f0)
#define WRDQ_CLK_DELAY_5_ADDR        (0x0d0)
#define WRDQ_CLK_DELAY_4_ADDR        (0x0b0)
#define WRDQ_CLK_DELAY_3_ADDR        (0x090)
#define WRDQ_CLK_DELAY_2_ADDR        (0x070)
#define WRDQ_CLK_DELAY_1_ADDR        (0x050)
#define WRDQ_CLK_DELAY_0_ADDR        (0x030)
#define WRDQ_CLK_DELAY_8_OFFSET      32
#define WRDQ_CLK_DELAY_7_OFFSET      32
#define WRDQ_CLK_DELAY_6_OFFSET      32
#define WRDQ_CLK_DELAY_5_OFFSET      32
#define WRDQ_CLK_DELAY_4_OFFSET      32
#define WRDQ_CLK_DELAY_3_OFFSET      32
#define WRDQ_CLK_DELAY_2_OFFSET      32
#define WRDQ_CLK_DELAY_1_OFFSET      32
#define WRDQ_CLK_DELAY_0_OFFSET      32
#define WRDQ_CLK_DELAY_MASK          (0xff)

#define RDDATA_DELAY_8_ADDR        (0x120)
#define RDDATA_DELAY_7_ADDR        (0x100)
#define RDDATA_DELAY_6_ADDR        (0x0e0)
#define RDDATA_DELAY_5_ADDR        (0x0c0)
#define RDDATA_DELAY_4_ADDR        (0x0a0)
#define RDDATA_DELAY_3_ADDR        (0x080)
#define RDDATA_DELAY_2_ADDR        (0x060)
#define RDDATA_DELAY_1_ADDR        (0x040)
#define RDDATA_DELAY_0_ADDR        (0x020)
#define RDDATA_DELAY_8_OFFSET      24
#define RDDATA_DELAY_7_OFFSET      24
#define RDDATA_DELAY_6_OFFSET      24
#define RDDATA_DELAY_5_OFFSET      24
#define RDDATA_DELAY_4_OFFSET      24
#define RDDATA_DELAY_3_OFFSET      24
#define RDDATA_DELAY_2_OFFSET      24
#define RDDATA_DELAY_1_OFFSET      24
#define RDDATA_DELAY_0_OFFSET      24
#define RDDATA_DELAY_MASK          (0xff)

#define RDDQS_LT_HALF_8_ADDR        (0x120)
#define RDDQS_LT_HALF_7_ADDR        (0x100)
#define RDDQS_LT_HALF_6_ADDR        (0x0e0)
#define RDDQS_LT_HALF_5_ADDR        (0x0c0)
#define RDDQS_LT_HALF_4_ADDR        (0x0a0)
#define RDDQS_LT_HALF_3_ADDR        (0x080)
#define RDDQS_LT_HALF_2_ADDR        (0x060)
#define RDDQS_LT_HALF_1_ADDR        (0x040)
#define RDDQS_LT_HALF_0_ADDR        (0x020)
#define RDDQS_LT_HALF_8_OFFSET      16
#define RDDQS_LT_HALF_7_OFFSET      16
#define RDDQS_LT_HALF_6_OFFSET      16
#define RDDQS_LT_HALF_5_OFFSET      16
#define RDDQS_LT_HALF_4_OFFSET      16
#define RDDQS_LT_HALF_3_OFFSET      16
#define RDDQS_LT_HALF_2_OFFSET      16
#define RDDQS_LT_HALF_1_OFFSET      16
#define RDDQS_LT_HALF_0_OFFSET      16
#define RDDQS_LT_HALF_MASK          (0xff)

#define WRDQS_LT_HALF_8_ADDR        (0x120)
#define WRDQS_LT_HALF_7_ADDR        (0x100)
#define WRDQS_LT_HALF_6_ADDR        (0x0e0)
#define WRDQS_LT_HALF_5_ADDR        (0x0c0)
#define WRDQS_LT_HALF_4_ADDR        (0x0a0)
#define WRDQS_LT_HALF_3_ADDR        (0x080)
#define WRDQS_LT_HALF_2_ADDR        (0x060)
#define WRDQS_LT_HALF_1_ADDR        (0x040)
#define WRDQS_LT_HALF_0_ADDR        (0x020)
#define WRDQS_LT_HALF_8_OFFSET      8
#define WRDQS_LT_HALF_7_OFFSET      8
#define WRDQS_LT_HALF_6_OFFSET      8
#define WRDQS_LT_HALF_5_OFFSET      8
#define WRDQS_LT_HALF_4_OFFSET      8
#define WRDQS_LT_HALF_3_OFFSET      8
#define WRDQS_LT_HALF_2_OFFSET      8
#define WRDQS_LT_HALF_1_OFFSET      8
#define WRDQS_LT_HALF_0_OFFSET      8
#define WRDQS_LT_HALF_MASK          (0xff)

#define WRDQ_LT_HALF_8_ADDR        (0x120)
#define WRDQ_LT_HALF_7_ADDR        (0x100)
#define WRDQ_LT_HALF_6_ADDR        (0x0e0)
#define WRDQ_LT_HALF_5_ADDR        (0x0c0)
#define WRDQ_LT_HALF_4_ADDR        (0x0a0)
#define WRDQ_LT_HALF_3_ADDR        (0x080)
#define WRDQ_LT_HALF_2_ADDR        (0x060)
#define WRDQ_LT_HALF_1_ADDR        (0x040)
#define WRDQ_LT_HALF_0_ADDR        (0x020)
#define WRDQ_LT_HALF_8_OFFSET      0
#define WRDQ_LT_HALF_7_OFFSET      0
#define WRDQ_LT_HALF_6_OFFSET      0
#define WRDQ_LT_HALF_5_OFFSET      0
#define WRDQ_LT_HALF_4_OFFSET      0
#define WRDQ_LT_HALF_3_OFFSET      0
#define WRDQ_LT_HALF_2_OFFSET      0
#define WRDQ_LT_HALF_1_OFFSET      0
#define WRDQ_LT_HALF_0_OFFSET      0
#define WRDQ_LT_HALF_MASK          (0xff)

//3C param
#define CLKLVL_DELAY_MASK       (0xff)
#define RDLVL_GATE_DELAY_MASK   (0xff)
#define RDLVL_DELAY_MASK        (0xff)
#define RDLVL_DQSN_DELAY_MASK   (0xff)
#define WRLVL_DELAY_MASK        (0xff)
#define WRLVL_DQ_DELAY_MASK     (0xff)

#define CLKLVL_DELAY_3_ADDR      (0x018)
#define CLKLVL_DELAY_2_ADDR      (0x018)
#define CLKLVL_DELAY_1_ADDR      (0x018)
#define CLKLVL_DELAY_0_ADDR      (0x018)
#define CLKLVL_DELAY_3_OFFSET    56
#define CLKLVL_DELAY_2_OFFSET    48
#define CLKLVL_DELAY_1_OFFSET    40
#define CLKLVL_DELAY_0_OFFSET    32

#define RDLVL_GATE_DELAY_8_ADDR (0x138)
#define RDLVL_GATE_DELAY_7_ADDR (0x118)
#define RDLVL_GATE_DELAY_6_ADDR (0x0f8)
#define RDLVL_GATE_DELAY_5_ADDR (0x0d8)
#define RDLVL_GATE_DELAY_4_ADDR (0x0b8)
#define RDLVL_GATE_DELAY_3_ADDR (0x098)
#define RDLVL_GATE_DELAY_2_ADDR (0x078)
#define RDLVL_GATE_DELAY_1_ADDR (0x058)
#define RDLVL_GATE_DELAY_0_ADDR (0x038)
#define RDLVL_GATE_DELAY_8_OFFSET    0
#define RDLVL_GATE_DELAY_7_OFFSET    0
#define RDLVL_GATE_DELAY_6_OFFSET    0
#define RDLVL_GATE_DELAY_5_OFFSET    0
#define RDLVL_GATE_DELAY_4_OFFSET    0
#define RDLVL_GATE_DELAY_3_OFFSET    0
#define RDLVL_GATE_DELAY_2_OFFSET    0
#define RDLVL_GATE_DELAY_1_OFFSET    0
#define RDLVL_GATE_DELAY_0_OFFSET    0

#define RDLVL_DELAY_8_ADDR      (0x138)
#define RDLVL_DELAY_7_ADDR      (0x118)
#define RDLVL_DELAY_6_ADDR      (0x0f8)
#define RDLVL_DELAY_5_ADDR      (0x0d8)
#define RDLVL_DELAY_4_ADDR      (0x0b8)
#define RDLVL_DELAY_3_ADDR      (0x098)
#define RDLVL_DELAY_2_ADDR      (0x078)
#define RDLVL_DELAY_1_ADDR      (0x058)
#define RDLVL_DELAY_0_ADDR      (0x038)
#define RDLVL_DELAY_8_OFFSET    24
#define RDLVL_DELAY_7_OFFSET    24
#define RDLVL_DELAY_6_OFFSET    24
#define RDLVL_DELAY_5_OFFSET    24
#define RDLVL_DELAY_4_OFFSET    24
#define RDLVL_DELAY_3_OFFSET    24
#define RDLVL_DELAY_2_OFFSET    24
#define RDLVL_DELAY_1_OFFSET    24
#define RDLVL_DELAY_0_OFFSET    24

#define RDLVL_DQSN_DELAY_8_ADDR      (0x138)
#define RDLVL_DQSN_DELAY_7_ADDR      (0x118)
#define RDLVL_DQSN_DELAY_6_ADDR      (0x0f8)
#define RDLVL_DQSN_DELAY_5_ADDR      (0x0d8)
#define RDLVL_DQSN_DELAY_4_ADDR      (0x0b8)
#define RDLVL_DQSN_DELAY_3_ADDR      (0x098)
#define RDLVL_DQSN_DELAY_2_ADDR      (0x078)
#define RDLVL_DQSN_DELAY_1_ADDR      (0x058)
#define RDLVL_DQSN_DELAY_0_ADDR      (0x038)
#define RDLVL_DQSN_DELAY_8_OFFSET    32
#define RDLVL_DQSN_DELAY_7_OFFSET    32
#define RDLVL_DQSN_DELAY_6_OFFSET    32
#define RDLVL_DQSN_DELAY_5_OFFSET    32
#define RDLVL_DQSN_DELAY_4_OFFSET    32
#define RDLVL_DQSN_DELAY_3_OFFSET    32
#define RDLVL_DQSN_DELAY_2_OFFSET    32
#define RDLVL_DQSN_DELAY_1_OFFSET    32
#define RDLVL_DQSN_DELAY_0_OFFSET    32

#define WRLVL_DELAY_8_ADDR      (0x138)
#define WRLVL_DELAY_7_ADDR      (0x118)
#define WRLVL_DELAY_6_ADDR      (0x0f8)
#define WRLVL_DELAY_5_ADDR      (0x0d8)
#define WRLVL_DELAY_4_ADDR      (0x0b8)
#define WRLVL_DELAY_3_ADDR      (0x098)
#define WRLVL_DELAY_2_ADDR      (0x078)
#define WRLVL_DELAY_1_ADDR      (0x058)
#define WRLVL_DELAY_0_ADDR      (0x038)
#define WRLVL_DELAY_8_OFFSET    16
#define WRLVL_DELAY_7_OFFSET    16
#define WRLVL_DELAY_6_OFFSET    16
#define WRLVL_DELAY_5_OFFSET    16
#define WRLVL_DELAY_4_OFFSET    16
#define WRLVL_DELAY_3_OFFSET    16
#define WRLVL_DELAY_2_OFFSET    16
#define WRLVL_DELAY_1_OFFSET    16
#define WRLVL_DELAY_0_OFFSET    16

#define WRLVL_DQ_DELAY_8_ADDR      (0x138)
#define WRLVL_DQ_DELAY_7_ADDR      (0x118)
#define WRLVL_DQ_DELAY_6_ADDR      (0x0f8)
#define WRLVL_DQ_DELAY_5_ADDR      (0x0d8)
#define WRLVL_DQ_DELAY_4_ADDR      (0x0b8)
#define WRLVL_DQ_DELAY_3_ADDR      (0x098)
#define WRLVL_DQ_DELAY_2_ADDR      (0x078)
#define WRLVL_DQ_DELAY_1_ADDR      (0x058)
#define WRLVL_DQ_DELAY_0_ADDR      (0x038)
#define WRLVL_DQ_DELAY_8_OFFSET    8
#define WRLVL_DQ_DELAY_7_OFFSET    8
#define WRLVL_DQ_DELAY_6_OFFSET    8
#define WRLVL_DQ_DELAY_5_OFFSET    8
#define WRLVL_DQ_DELAY_4_OFFSET    8
#define WRLVL_DQ_DELAY_3_OFFSET    8
#define WRLVL_DQ_DELAY_2_OFFSET    8
#define WRLVL_DQ_DELAY_1_OFFSET    8
#define WRLVL_DQ_DELAY_0_OFFSET    8

//------------------------
//define for ddr configure register param location
#define ADDR_INFO_CS_0_ADDR      (0x210)
#define ADDR_INFO_CS_1_ADDR      (0x218)
#define ADDR_INFO_CS_2_ADDR      (0x220)
#define ADDR_INFO_CS_3_ADDR      (0x228)

#define COLUMN_DIFF_ADDR         (0x210)
#define COLUMN_DIFF_OFFSET       0
#define EIGHT_BANK_MODE_ADDR     (0x210)
#define EIGHT_BANK_MODE_OFFSET   8
#define ROW_DIFF_ADDR            (0x210)
#define ROW_DIFF_OFFSET          16
#define CS_DIFF_ADDR             (0x210)
#define CS_DIFF_OFFSET           24
#define ADDR_WIN_DATA_WIDTH_ADDR   (0x210)
#define ADDR_WIN_DATA_WIDTH_OFFSET 32
#define ADDR_WIN_BANK_NUM_ADDR   (0x210)
#define ADDR_WIN_BANK_NUM_OFFSET 34
#define CS_MAP_ADDR              (0x1f0)
#define CS_MAP_OFFSET            32
#define CS_PLACE_ADDR            (0x210)
#define CS_PLACE_OFFSET          48
#define CS_ENABLE_ADDR           (0x168)
#define CS_ENABLE_OFFSET         0
#define CS_MRS_ADDR              (0x168)
#define CS_MRS_OFFSET            8
#define CS_ZQ_ADDR               (0x168)
#define CS_ZQ_OFFSET             16
#define BANK_NUM_ADDR            (0x168)
#define BANK_NUM_OFFSET          24
#define MC_MULTI_CHANNEL_ADDR    (0x1f0)
#define MC_MULTI_CHANNEL_OFFSET  16
#define DATA_WIDTH_32_ADDR       (0x1f0)
#define DATA_WIDTH_32_OFFSET     18
#define ECC_ENABLE_ADDR          (0x250)
#define ECC_ENABLE_OFFSET        16
#define ECC_INT_ENABLE_ADDR      (0x250)
#define ECC_INT_ENABLE_OFFSET    0
#define ECC_DISABLE_W_UC_ERR_ADDR   (0x250)
#define ECC_DISABLE_W_UC_ERR_OFFSET 18
//------------------------
