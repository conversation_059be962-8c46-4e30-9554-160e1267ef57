/*
 * This file is for Loongson SOC pin and gpio ctrl
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON>@loongson.cn
 */
#include "target/pincfgs.h"
#include <pmon.h>
#include <string.h>
pin_cfgs_t default_pin_cfgs[155] = {
	//0:GPIO , 5:main
	{  0, 0},	//0:GPIO0
	{  1, 0},	//0:GPIO1
	{  2, 5},	//5:vga_hsync
	{  3, 5},	//5:vga_vsync
	{  4, 5},	//5:lcd_clk
	{  5, 5},	//5:lcd_vsync
	{  6, 5},	//5:lcd_hsync
	{  7, 5},	//5:lcd_en
	{  8, 5},	//5:lcd_dat_b[0]
	{  9, 5},	//5:lcd_dat_b[1]
	{ 10, 5},	//5:lcd_dat_b[2]
	{ 11, 5},	//5:lcd_dat_b[3]
	{ 12, 5},	//5:lcd_dat_b[4]
	{ 13, 5},	//5:lcd_dat_b[5]
	{ 14, 5},	//5:lcd_dat_b[6]
	{ 15, 5},	//5:lcd_dat_b[7]
	{ 16, 5},	//5:lcd_dat_g[0]
	{ 17, 5},	//5:lcd_dat_g[1]
	{ 18, 5},	//5:lcd_dat_g[2]
	{ 19, 5},	//5:lcd_dat_g[3]
	{ 20, 5},	//5:lcd_dat_g[4]
	{ 21, 5},	//5:lcd_dat_g[5]
	{ 22, 5},	//5:lcd_dat_g[6]
	{ 23, 5},	//5:lcd_dat_g[7]
	{ 24, 5},	//5:lcd_dat_r[0]	3:spi4_clk
	{ 25, 5},	//5:lcd_dat_r[1]	3:spi4_miso
	{ 26, 5},	//5:lcd_dat_r[2]	3:spi4_mosi
	{ 27, 5},	//5:lcd_dat_r[3]	3:spi4_cs
	{ 28, 5},	//5:lcd_dat_r[4]	3:spi5_clk
	{ 29, 5},	//5:lcd_dat_r[5]	3:spi5_miso
	{ 30, 5},	//5:lcd_dat_r[6]	3:spi5_mosi
	{ 31, 5},	//5:lcd_dat_r[7]	3:spi5_cs
	{ 32, 5},	//5:kb_clk		3:spi3_clk
	{ 33, 5},	//5:kb_dat		3:spi3_miso
	{ 34, 5},	//5:ms_clk		3:spi3_mosi
	{ 35, 5},	//5:ms_dat		3:spi3_cs
	{ 36, 5},	//5:ac97_datai
	{ 37, 5},	//5:ac97_datao
	{ 38, 5},	//5:ac97_sync
	{ 39, 5},	//5:ac97_reset
	{ 40, 5},	//5:spi0_clk
	{ 41, 5},	//5:spi0_miso
	{ 42, 5},	//5:spi0_mosi
	{ 43, 5},	//5:spi0_cs[0]
	{ 44, 0},	//5:spi1_clk		0:GPIO44
	{ 45, 0},	//5:spi1_miso		0:GPIO45
	{ 46, 0},	//5:spi1_mosi		0:GPIO46
	{ 47, 0},	//5:spi1_cs[0]		0:GPIO47
	{ 48, 1},	//5:uart0_rx		1:gmac1_rx_ctl
	{ 49, 1},	//5:uart0_tx		1:gmac1_rx[0]
	{ 50, 1},	//5:uart0_rts		1:gmac1_rx[1]	3:scl1
	{ 51, 1},	//5:uart0_cts		1:gmac1_rx[2]	3:sda1
	{ 52, 1},	//5:uart0_dsr		1:gmac1_rx[3]
	{ 53, 1},	//5:uart0_dtr		1:gmac1_tx_ctl
	{ 54, 1},	//5:uart0_dcd		1:gmac1_tx[0]
	{ 55, 1},	//5:uart0_ri		1:gmac1_tx[1]
	{ 56, 1},	//1:gmac1_tx[2]
	{ 57, 1},	//1:gmac1_tx[3]
	{ 58, 1},	//1:gmac1_mdck
	{ 59, 1},	//1:gmac1_mdio
	{ 60, 5},	//5:uart2_tx
	{ 61, 5},	//5:uart2_rx
	{ 62, 1},	//5:uart3_tx		1:pix1_scl
	{ 63, 1},	//5:uart3_rx		1:pix1_sda	2:pwm[13]
	{ 64, 1},	//5:scl0		1:nand_rdy[1]	3:spi0_cs[3]
	{ 65, 1},	//5:sda0		1:nand_ce[1]	3:spi0_cs[2]
	{ 66, 1},	//5:can0_rx		1:nand_rdy[2]	2:sda2		3:spi0_cs[1]
	{ 67, 1},	//5:can0_tx		1:nand_ce[2]	2:scl2		3:spi1_cs[3]
	{ 68, 1},	//5:can1_rx		1:nand_rdy[3]	2:sda3		3:spi1_cs[2]
	{ 69, 1},	//5:can1_tx		1:nand_ce[3]	2:scl3		3:spi1_cs[1]
	{ 70, 1},	//5:lpc_ad[0]		1:nand_d[0]
	{ 71, 1},	//5:lpc_ad[1]		1:nand_d[1]
	{ 72, 1},	//5:lpc_ad[2]		1:nand_d[2]
	{ 73, 1},	//5:lpc_ad[3]		1:nand_d[3]
	{ 74, 1},	//5:lpc_frame		1:nand_d[4]
	{ 75, 1},	//5:lpc_serirq		1:nand_d[5]
	{ 76, 5},	//5:nand_cle
	{ 77, 5},	//5:nand_ale
	{ 78, 5},	//5:nand_rd
	{ 79, 5},	//5:nand_wr
	{ 80, 5},	//5:nand_ce[0]
	{ 81, 5},	//5:nand_rdy[0]
	{ 82, 5},	//5:nand_d[6]
	{ 83, 5},	//5:nand_d[7]
	{ 84, 5},	//5:pwm[0]
	{ 85, 5},	//5:pwm[1]
	{ 86, 0},	//5:pwm[2]		0:GPIO86
	{ 87, 5},	//5:pwm[3]
	{ 88, 5},	//5:gmac0_rx_ctl
	{ 89, 5},	//5:gmac0_rx[0]
	{ 90, 5},	//5:gmac0_rx[1]
	{ 91, 5},	//5:gmac0_rx[2]
	{ 92, 5},	//5:gmac0_rx[3]
	{ 93, 5},	//5:gmac0_tx_ctl
	{ 94, 5},	//5:gmac0_tx[0]
	{ 95, 5},	//5:gmac0_tx[1]
	{ 96, 5},	//5:gmac0_tx[2]
	{ 97, 5},	//5:gmac0_tx[3]
	{ 98, 5},	//5:gmac0_mdck
	{ 99, 5},	//5:gmac0_mdio
	{100, 1},	//1:pr_int		2:lioa[0]
	{101, 1},	//1:pr0_clk		2:lioa[1]
	{102, 1},	//1:pr0_start		2:lioa[2]
	{103, 1},	//1:pr0_ready		2:lioa[3]
	{104, 1},	//1:pr0_enable		2:lioa[4]
	{105, 1},	//1:pr0_shold		2:lioa[5]	3:pwm[5]
	{106, 1},	//1:pr0_data		2:lioa[6]	3:pwm[6]
	{107, 1},	//1:pr0_hsync		2:lioa[7]	3:pwm[7]
	{108, 1},	//1:pr1_enable		2:lioa[8]	3:pwm[8]
	{109, 1},	//1:pr1_shold		2:lioa[9]	3:pwm[9]
	{110, 1},	//1:pr1_data		2:lioa[10]	3:pwm[10]
	{111, 1},	//1:pr2_clk		2:lioa[11]	3:pwm[11]
	{112, 1},	//1:pr2_start		2:lioa[12]	3:pwm[12]
	{113, 1},	//1:pr2_ready		2:lioa[13]	3:pwm[13]	4:spi2_clk
	{114, 1},	//1:pr2_enable		2:lioa[14]	3:pwm[14]	4:spi2_miso
	{115, 1},	//1:pr2_shold		2:lioa[15]	3:pwm[15]	4:spi2_mosi
	{116, 3},	//1:pr2_data		2:lio_data[0]	3:uart1_rx	4:spi2_cs
	{117, 3},	//1:pr2_hsync		2:lio_data[1]	3:uart1_tx	4:spi3_clk
	{118, 3},	//1:pr3_enable		2:lio_data[2]	3:uart1_rts	4:spi3_miso
	{119, 3},	//1:pr3_shold		2:lio_data[3]	3:uart1_cts	4:spi3_mosi
	{120, 3},	//1:pr3_data		2:lio_data[4]	3:uart1_dsr	4:spi3_cs
	{121, 3},	//1:pr4_clk		2:lio_data[5]	3:uart1_dtr	4:spi4_clk
	{122, 3},	//1:pr4_start		2:lio_data[6]	3:uart1_dcd	4:spi4_miso
	{123, 3},	//1:pr4_ready		2:lio_data[7]	3:uart1_ri	4:spi4_mosi
	{124, 1},	//1:pr4_enable		2:lio_data[8]	4:spi4_cs
	{125, 1},	//1:pr4_shold		2:lio_data[9]	4:spi5_clk
	{126, 1},	//1:pr4_data		2:lio_data[10]	4:spi5_miso
	{127, 1},	//1:pr4_hsync		2:lio_data[11]	4:spi5_mosi
	{128, 1},	//1:pr5_enable		2:lio_data[12]	4:spi5_cs
	{129, 1},	//1:pr5_shold		2:lio_data[13]
	{130, 1},	//1:pr5_data		2:lio_data[14]
	{131, 1},	//1:pr6_clk		2:lio_data[15]
	{132, 1},	//1:pr6_start		2:lioa[16]
	{133, 1},	//1:pr6_ready		2:lioa[17]	4:can2_rx
	{134, 1},	//1:pr6_enable		2:lioa[18]	4:can2_tx
	{135, 1},	//1:pr6_shold		2:lioa[19]	4:can3_rx
	{136, 1},	//1:pr6_data		2:lioa[20]	4:can3_tx
	{137, 1},	//1:pr6_hsync		2:lioa[21]
	{138, 1},	//1:pr7_enable		2:lioa[22]
	{139, 1},	//1:pr7_shold		2:liocsn[0]
	{140, 1},	//1:pr7_data		2:liocsn[1]
	{141, 1},	//1:pix0_scl		2:liowrn
	{142, 1},	//1:pix0_sda		2:liordn
	{143, 4},	//4:sdio1_clk
	{144, 4},	//4:sdio1_cmd
	{145, 4},	//4:sdio1_d[0]
	{146, 4},	//4:sdio1_d[1]
	{147, 4},	//4:sdio1_d[2]
	{148, 4},	//4:sdio1_d[3]
	{149, 5},	//5:sdio_clk
	{150, 5},	//5:sdio_cmd
	{151, 5},	//5:sdio_d[0]
	{152, 5},	//5:sdio_d[1]
	{153, 5},	//5:sdio_d[2]
	{154, 5}	//5:sdio_d[3]
};

/* add all pins that you want to cfg. just like this, then call cfg_all_pin_multi() */
pin_cfgs_t can0_pin_cfgs[] = {
	{ 66, 5 },	//5:can0_rx	1:nand_rdy[2]	2:sda2		3:spi0_cs[1]
	{ 67, 5 },	//5:can0_tx	1:nand_ce[2]	2:scl2		3:spi1_cs[3]
};

void ls2k500_gpio_out(int gpio_num, int val)
{
	set_pin_mode(gpio_num, 0);
	uint64_t *addr_dir = LS2K500_GPIO_00_63_DIR + gpio_num / 64 * GPIO_SKIP_64_OFFSET;
	uint64_t *addr_out = LS2K500_GPIO_00_63_OUT + gpio_num / 64 * GPIO_SKIP_64_OFFSET;
	int bit = gpio_num % 64;
	readq(addr_dir) &= ~(1ULL << bit);
	readq(addr_out) = readq(addr_out) & ~(1ULL << bit) | ((unsigned long long)!!val << bit);
}

int ls2k500_gpio_in(int gpio_num)
{
	set_pin_mode(gpio_num, 0);
	uint64_t *addr_dir = LS2K500_GPIO_00_63_DIR + gpio_num / 64 * GPIO_SKIP_64_OFFSET;
	uint64_t *addr_in = LS2K500_GPIO_00_63_IN + gpio_num / 64 * GPIO_SKIP_64_OFFSET;
	int bit = gpio_num % 64;
	readq(addr_dir) |= (1ULL << bit);
	return !!(readq(addr_in) & (1ULL << bit));
}

void ls2k500_gpio_irq_enable(int gpio_num, int val)
{
	uint64_t *addr_irqen = LS2K500_GPIO_IRQ_EN_CFG + gpio_num / 64 * 8;
	int bit = gpio_num % 64;
	readq(addr_irqen) = readq(addr_irqen) & ~(1ULL << bit) | ((unsigned long long)!!val << bit);
}

void ls2k500_gpio_irq_init(int gpio_base, uint64_t mask, uint64_t val)
{
	uint64_t *addr_irqen = LS2K500_GPIO_IRQ_EN_CFG + gpio_base / 64 * 8;
	readq(addr_irqen) = readq(addr_irqen) & mask | val;
}

int cmd_ls2k500_gpio_out(int ac, unsigned char *av[])
{
	if (ac != 3) {
		printf("gpio_out <gpio_num> <output_val>\n");
		return 0;
	}
	ls2k500_gpio_out(strtoul(av[1], NULL, 0), strtoul(av[2], NULL, 0));
	return 0;
}

int cmd_ls2k500_gpio_in(int ac, unsigned char *av[])
{
	if (ac != 2) {
		printf("gpio_in <gpio_num>\n");
		return 0;
	}
	printf("gpio read val: %d\n", ls2k500_gpio_in(strtoul(av[1], NULL, 0)));
	return 0;
}

char pin_set[156] = {0};
int loop_set_pin(int from, int to, int val, int skip)
{
	int i;
	for (i = from; skip && i <= to; i++) {	//check
		if (pin_set[i]) {
			return -1;
		}
	}
	for (i = from; i <= to; i++) {
		set_pin_mode(i, val);
		pin_set[i] = 1;
	}
	return 0;
}


static const Cmd Cmds[] =
{
	{"MyCmds"},
	{"gpio_out","",0,"set gpio out put <gpio num> <val>", cmd_ls2k500_gpio_out , 0, 3, CMD_REPEAT},
	{"gpio_in","",0,"set gpio in, and get val", cmd_ls2k500_gpio_in , 0, 2, CMD_REPEAT},
	{0,0}
};

static void init_cmd __P((void)) __attribute__ ((constructor));

static void
init_cmd()
{
	cmdlist_expand(Cmds,1);
}
